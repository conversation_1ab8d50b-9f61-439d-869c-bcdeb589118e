from selenium import webdriver
from selenium.webdriver.common.by import By
import time

# Set the path to the Chrome WebDriver (replace with the path to your WebDriver)
chrome_driver_path = r'D:\RD\code-ET\chromedriver.exe'

# Initialize the Chrome WebDriver
driver = webdriver.Chrome(executable_path=chrome_driver_path)

# Navigate to the login page
driver.get('https://qualysguard.qg1.apps.qualys.in/portal-front/module/was/#tab=was-web-applications.datalist-webapps')  # Replace with the actual login page URL

# Find and input the username and password (replace with your credentials)
username_input = driver.find_element(By.ID, 'ext-comp-1005')
password_input = driver.find_element(By.ID, 'ext-comp-1006')
username_input.send_keys('techn8ad')
password_input.send_keys('Demo@123')

# Submit the login form (assuming there's a login button with the id 'ext-gen23')
login_button = driver.find_element(By.ID, 'ext-gen23')
login_button.click()

# Wait for the login process to complete (you may need to adjust the time)
driver.implicitly_wait(10)

# Click the "Knowledge Base" button using the provided XPath
knowledge_base_button = driver.find_element(By.XPATH, '//*[@id="ext-comp-1030"]')
knowledge_base_button.click()

# Wait for 60 seconds
time.sleep(60)

# Perform additional actions on the knowledge base page

# Close the browser window when done
driver.quit()
