{
    "scanId": 325,
    "scanlogId": 325,
    "attack": {
        "attackCount": 1,
        "method": "GET",
        "name": "Original Crawler Request",
        "hostname": "www.practo.com",
        "originalRequest": {
            "scanId": 325,
            "scanlog_id": 325,
            "scanner": "haiku",
            "httpRequest": {
                "method": "GET",
                "headers": {
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
                    "Accept-Encoding": "gzip, deflate, br",
                    "Accept-Language": "en-US",
                    "Cookie": "__cfruid=391b4bcdfe3a1f3fc7fabd6640449f1e335a6053-1717252440; _fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _ga=GA1.2.527041583.**********; _gid=GA1.2.315403102.**********; _gat=1; _gat_fabric=1; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A8%2C%22s%22%3A**********%2C%22t%22%3A1717252489%7D; mp_85d643d7bc71611832663cd683666848_mixpanel=%7B%22distinct_id%22%3A%20%22%24device%3A18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24device_id%22%3A%20%2218fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%2C%22__mps%22%3A%20%7B%7D%2C%22__mpso%22%3A%20%7B%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%7D%2C%22__mpus%22%3A%20%7B%7D%2C%22__mpa%22%3A%20%7B%7D%2C%22__mpu%22%3A%20%7B%7D%2C%22__mpr%22%3A%20%5B%5D%2C%22__mpap%22%3A%20%5B%5D%7D; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252489.12.0.0",
                    "Sec-Fetch-Dest": "document",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Site": "none",
                    "Sec-Fetch-User": "?1",
                    "Upgrade-Insecure-Requests": "1",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36"
                },
                "appTranaKey": "GET-practo.com/?",
                "crawlerBookmark": {
                    "state": 0,
                    "action": 3,
                    "bookmarkId": "state=0|action=3",
                    "actionList": {
                        "serliaziedName": "./datastructure\\action-list.js",
                        "args": ["bookmark", [{
                                    "serliaziedName": "./datastructure\\action-list.js",
                                    "args": ["initial actions", [{
                                                "serliaziedName": "./datastructure\\load-action.js",
                                                "args": ["[initial]", "https://www.practo.com/portalservermb/", "initial, mustRunAtInit: true"]
                                            }
                                        ], "seq", true]
                                }
                            ], "seq", false]
                    },
                    "rootActionCount": 1
                },
                "resourceType": "mainFrame",
                "crawlerKey": "GET-practo.com/?",
                "uri": "https://www.practo.com/",
                "haikuKey": "GET-practo.com/?",
                "haikuResourceType": "core",
                "scanlog_id": 325,
                "scanId": 325,
                "attackRequestId": 319
            },
            "requestId": 7,
            "haikuPriority": {
                "priority": 100,
                "requestParams": [],
                "nonEmptyValueCount": 0,
                "pathComponentsCount": 0
            },
            "inProgessRequest": {},
            "pluginData": {
                "Remote File Inclusion (RFI)": {},
                "Cross Origin Resource Sharing": {},
                "Email Address Found": {
                    "emailidFound": true
                },
                "Possible Sensitive Directory or file": {},
                "Authentication details found": {},
                "Clickjacking - HTTP Header Check": {
                    "XpcdpHdrVulnFound": true
                },
                "HTTP Header Info Disclosure": {},
                "Web Server details found": {
                    "serverdtsfound": true,
                    "Appdtsfound": true
                },
                "Password-submitted-without-ssl": {},
                "Insecure HTTP Transport": {
                    "insecureCount": 88990
                },
                "Insecure HTTP Method Transition": {
                    "InsecureHTTPMethod": {
                        "OriResBodylength": 88990
                    }
                },
                "Sensitive-information-cached": {
                    "sensitiveInfoCacheVulnFound": true
                },
                "Application-Error-Message": {},
                "Cookie Manipulation": {
                    "originalChecksum": "af2a0cfe14558625103f1d82e8078d2c"
                },
                "Weak Session ID": {},
                "Insecure-Flash-Embed-Param-Found": {},
                "Application Detection": {},
                "Credential Guessing": {},
                "Authentication Bypass": {},
                "Apache ETag header Found vulnerability": {},
                "VMware Server-side Template Injection (RCE) Vulnerability": {},
                "Apache OFBiz Authentication Bypass Vulnerability (CVE-2023-51467)": {},
                "ExtJs Arbitrary File Read": {},
                "Reveals Sensitive Information": {
                    "SenInfobodyFound": true
                }
            }
        },
        "href": "https://www.practo.com/",
        "area": "original-crawler-request",
        "headers": {
            "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
            "accept-language": "en-US",
            "cookie": "__cfruid=391b4bcdfe3a1f3fc7fabd6640449f1e335a6053-1717252440; _fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _ga=GA1.2.527041583.**********; _gid=GA1.2.315403102.**********; _gat=1; _gat_fabric=1; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A8%2C%22s%22%3A**********%2C%22t%22%3A1717252489%7D; mp_85d643d7bc71611832663cd683666848_mixpanel=%7B%22distinct_id%22%3A%20%22%24device%3A18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24device_id%22%3A%20%2218fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%2C%22__mps%22%3A%20%7B%7D%2C%22__mpso%22%3A%20%7B%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%7D%2C%22__mpus%22%3A%20%7B%7D%2C%22__mpa%22%3A%20%7B%7D%2C%22__mpu%22%3A%20%7B%7D%2C%22__mpr%22%3A%20%5B%5D%2C%22__mpap%22%3A%20%5B%5D%7D; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252489.12.0.0; PHPSESSID=96fkcd63lbc35kjq09dp6llg37; availability_pla_client_id=225BEC25-57C0-AB3B-BBE5-157C3B44D0CB",
            "sec-fetch-dest": "document",
            "sec-fetch-mode": "navigate",
            "sec-fetch-site": "none",
            "sec-fetch-user": "?1",
            "upgrade-insecure-requests": "1",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36",
            "host": "www.practo.com",
            "accept-encoding": "gzip, deflate"
        },
        "httpRequest": {
            "method": "GET",
            "headers": {
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept-Language": "en-US",
                "Cookie": "__cfruid=391b4bcdfe3a1f3fc7fabd6640449f1e335a6053-1717252440; _fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _ga=GA1.2.527041583.**********; _gid=GA1.2.315403102.**********; _gat=1; _gat_fabric=1; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A8%2C%22s%22%3A**********%2C%22t%22%3A1717252489%7D; mp_85d643d7bc71611832663cd683666848_mixpanel=%7B%22distinct_id%22%3A%20%22%24device%3A18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24device_id%22%3A%20%2218fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%2C%22__mps%22%3A%20%7B%7D%2C%22__mpso%22%3A%20%7B%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%7D%2C%22__mpus%22%3A%20%7B%7D%2C%22__mpa%22%3A%20%7B%7D%2C%22__mpu%22%3A%20%7B%7D%2C%22__mpr%22%3A%20%5B%5D%2C%22__mpap%22%3A%20%5B%5D%7D; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252489.12.0.0",
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1",
                "Upgrade-Insecure-Requests": "1",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36"
            },
            "appTranaKey": "GET-practo.com/?",
            "crawlerBookmark": {
                "state": 0,
                "action": 3,
                "bookmarkId": "state=0|action=3",
                "actionList": {
                    "serliaziedName": "./datastructure\\action-list.js",
                    "args": ["bookmark", [{
                                "serliaziedName": "./datastructure\\action-list.js",
                                "args": ["initial actions", [{
                                            "serliaziedName": "./datastructure\\load-action.js",
                                            "args": ["[initial]", "https://www.practo.com/portalservermb/", "initial, mustRunAtInit: true"]
                                        }
                                    ], "seq", true]
                            }
                        ], "seq", false]
                },
                "rootActionCount": 1
            },
            "resourceType": "mainFrame",
            "crawlerKey": "GET-practo.com/?",
            "uri": "https://www.practo.com/",
            "haikuKey": "GET-practo.com/?",
            "haikuResourceType": "core",
            "scanlog_id": 325,
            "scanId": 325,
            "attackRequestId": 319
        },
        "httpResponse": {
            "headers": {
                "date": "Sat, 01 Jun 2024 14:34:58 GMT",
                "content-type": "text/html; charset=utf-8",
                "content-length": "20867",
                "connection": "close",
                "content-encoding": "gzip",
                "content-security-policy": "frame-ancestors 'self' https://microapps.google.com; report-uri https://www.practo.com/consumer-home/cspreport",
                "etag": "W/\"15ba7-serd1CemNFu+DwsMUth43/5mZ58\"",
                "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
                "vary": "Accept-Encoding",
                "x-content-type-options": "nosniff",
                "x-frame-options": "SAMEORIGIN",
                "x-powered-by": "Express",
                "x-xss-protection": "1; mode=block",
                "cf-cache-status": "DYNAMIC",
                "server": "cloudflare",
                "cf-ray": "88cfed718f734206-BOM",
                "alt-svc": "h3=\":443\"; ma=86400"
            },
            "statusCode": 200,
            "statusMessage": "OK",
            "httpVersion": "1.1",
            "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"attack-4.body\"}"
        }
    },
    "response": {
        "headers": {
            "date": "Sat, 01 Jun 2024 14:34:58 GMT",
            "content-type": "text/html; charset=utf-8",
            "content-length": "20867",
            "connection": "close",
            "content-encoding": "gzip",
            "content-security-policy": "frame-ancestors 'self' https://microapps.google.com; report-uri https://www.practo.com/consumer-home/cspreport",
            "etag": "W/\"15ba7-serd1CemNFu+DwsMUth43/5mZ58\"",
            "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
            "vary": "Accept-Encoding",
            "x-content-type-options": "nosniff",
            "x-frame-options": "SAMEORIGIN",
            "x-powered-by": "Express",
            "x-xss-protection": "1; mode=block",
            "cf-cache-status": "DYNAMIC",
            "server": "cloudflare",
            "cf-ray": "88cfed718f734206-BOM",
            "alt-svc": "h3=\":443\"; ma=86400"
        },
        "statusCode": 200,
        "statusMessage": "OK",
        "httpVersion": "1.1",
        "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"attack-4.body\"}"
    },
    "vulns": {
        "ID-email-id-found": {
            "foundBy": "Email Address Found",
            "productionReady": true,
            "canAttackInReplayScan": false,
            "details": {
                "contextBytes": 200,
                "context": "tname\":\"https://www.practo.com\",\"originalUrl\":\"/consumer-home\",\"assetPath\":\"https://www.practostatic.com/web-assets/home-dweb\"}</script>\n      <script>window.__secrets = {\"sentry_client_dsn\":\"https://<EMAIL>/101\",\"pel_lib_url\":\"https://www.practostatic.com/pel/pel-1.6.1.js\",\"clevertap_project_id\":\"8W6-695-WK5Z\",\"ga_tracking_id\":\"UA-18841050-55\",\"fabric_ga_tracking_id\":\"UA-18841050-11\",\"fbq_tracking_id\":\"",
                "details": "<EMAIL>",
                "foundVulnInResponseBody": true
            },
            "autoPOC": [{
                    "type": "attack",
                    "path": "httpResponse.body",
                    "highlightType": "text",
                    "details": ["<EMAIL>"],
                    "description": "following attack vectors has been found in attack response body. vectors=> <EMAIL>"
                }
            ]
        },
        "ID-X-Permitted-CDP": {
            "foundBy": "Clickjacking - HTTP Header Check",
            "productionReady": true,
            "canAttackInReplayScan": false,
            "details": {
                "result": "Susceptible to resource abuse"
            },
            "autoPOC": []
        },
        "ID-Web-Server-Version-Disclosure": {
            "foundBy": "Web Server details found",
            "productionReady": true,
            "canAttackInReplayScan": false,
            "details": {
                "result": "Server: cloudflare"
            },
            "autoPOC": []
        },
        "ID-info-disclosure-http-headers": {
            "foundBy": "Web Server details found",
            "productionReady": true,
            "canAttackInReplayScan": false,
            "details": [{
                    "result": "x-powered-by: Express"
                }
            ],
            "autoPOC": []
        },
        "ID-sensitive-information-cached": {
            "foundBy": "Sensitive-information-cached",
            "productionReady": true,
            "canAttackInReplayScan": false,
            "details": {
                "details": {
                    "msg": "No cache-control/pragma headers are present which means there is some default caching still present"
                }
            },
            "autoPOC": []
        },
        "ID-subresource-integrity-check": {
            "foundBy": "Subresource Integrity Missing",
            "productionReady": true,
            "canAttackInReplayScan": false,
            "details": [{
                    "tagsFound": "<script src=\"https://www.practostatic.com/web-assets/home-dweb/javascripts/manifest.d2c3cca4e4a0.js\">",
                    "urlsFound": "https://www.practostatic.com/web-assets/home-dweb/javascripts/manifest.d2c3cca4e4a0.js"
                }, {
                    "tagsFound": "<script src=\"https://www.practostatic.com/web-assets/home-dweb/javascripts/vendor.40feb43c7ece.js\">",
                    "urlsFound": "https://www.practostatic.com/web-assets/home-dweb/javascripts/vendor.40feb43c7ece.js"
                }, {
                    "tagsFound": "<script src=\"https://www.practostatic.com/web-assets/home-dweb/javascripts/client.a67eee99adae.js\">",
                    "urlsFound": "https://www.practostatic.com/web-assets/home-dweb/javascripts/client.a67eee99adae.js"
                }
            ],
            "autoPOC": []
        },
        "ID-reveals-sensitive-low": {
            "foundBy": "Reveals Sensitive Information",
            "productionReady": true,
            "canAttackInReplayScan": false,
            "details": [{
                    "result": "user_name\":\"Amit Sharma\"\n"
                }, {
                    "result": "user_name\":\"Jyothi Bhatia\"\n"
                }, {
                    "result": "user_name\":\"Avinash Kumar\"\n"
                }
            ],
            "autoPOC": []
        },
        "ID-port-scanner": {
            "foundBy": "Port scan",
            "productionReady": true,
            "canAttackInReplayScan": false,
            "details": {
                "result": "Found open port: 80,8080,8443"
            },
            "autoPOC": []
        }
    }
} {
    "scanId": 325,
    "scanlogId": 325,
    "attack": {
        "attackCount": 1,
        "method": "GET",
        "name": "Exchange Server SSRF attack",
        "hostname": "www.practo.com",
        "originalRequest": {
            "scanId": 325,
            "scanlog_id": 325,
            "scanner": "haiku",
            "httpRequest": {
                "method": "GET",
                "headers": {
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
                    "Accept-Encoding": "gzip, deflate, br",
                    "Accept-Language": "en-US",
                    "Cookie": "__cfruid=391b4bcdfe3a1f3fc7fabd6640449f1e335a6053-1717252440; _fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _ga=GA1.2.527041583.**********; _gid=GA1.2.315403102.**********; _gat=1; _gat_fabric=1; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A8%2C%22s%22%3A**********%2C%22t%22%3A1717252489%7D; mp_85d643d7bc71611832663cd683666848_mixpanel=%7B%22distinct_id%22%3A%20%22%24device%3A18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24device_id%22%3A%20%2218fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%2C%22__mps%22%3A%20%7B%7D%2C%22__mpso%22%3A%20%7B%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%7D%2C%22__mpus%22%3A%20%7B%7D%2C%22__mpa%22%3A%20%7B%7D%2C%22__mpu%22%3A%20%7B%7D%2C%22__mpr%22%3A%20%5B%5D%2C%22__mpap%22%3A%20%5B%5D%7D; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252489.12.0.0",
                    "Sec-Fetch-Dest": "document",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Site": "none",
                    "Sec-Fetch-User": "?1",
                    "Upgrade-Insecure-Requests": "1",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36"
                },
                "appTranaKey": "GET-practo.com/?",
                "crawlerBookmark": {
                    "state": 0,
                    "action": 3,
                    "bookmarkId": "state=0|action=3",
                    "actionList": {
                        "serliaziedName": "./datastructure\\action-list.js",
                        "args": ["bookmark", [{
                                    "serliaziedName": "./datastructure\\action-list.js",
                                    "args": ["initial actions", [{
                                                "serliaziedName": "./datastructure\\load-action.js",
                                                "args": ["[initial]", "https://www.practo.com/portalservermb/", "initial, mustRunAtInit: true"]
                                            }
                                        ], "seq", true]
                                }
                            ], "seq", false]
                    },
                    "rootActionCount": 1
                },
                "resourceType": "mainFrame",
                "crawlerKey": "GET-practo.com/?",
                "uri": "https://www.practo.com/",
                "haikuKey": "GET-practo.com/?",
                "haikuResourceType": "core",
                "scanlog_id": 325,
                "scanId": 325,
                "attackRequestId": 319
            },
            "requestId": 7,
            "haikuPriority": {
                "priority": 100,
                "requestParams": [],
                "nonEmptyValueCount": 0,
                "pathComponentsCount": 0
            },
            "pluginData": {
                "Remote File Inclusion (RFI)": {},
                "Cross Origin Resource Sharing": {},
                "Email Address Found": {
                    "emailidFound": true
                },
                "Possible Sensitive Directory or file": {},
                "Authentication details found": {},
                "Clickjacking - HTTP Header Check": {
                    "XpcdpHdrVulnFound": true
                },
                "HTTP Header Info Disclosure": {},
                "Web Server details found": {
                    "serverdtsfound": true,
                    "Appdtsfound": true
                },
                "Password-submitted-without-ssl": {},
                "Insecure HTTP Transport": {
                    "insecureCount": 88990
                },
                "Insecure HTTP Method Transition": {
                    "InsecureHTTPMethod": {
                        "OriResBodylength": 88990
                    }
                },
                "Sensitive-information-cached": {
                    "sensitiveInfoCacheVulnFound": true
                },
                "Application-Error-Message": {},
                "Cookie Manipulation": {
                    "originalChecksum": "af2a0cfe14558625103f1d82e8078d2c"
                },
                "Weak Session ID": {},
                "Insecure-Flash-Embed-Param-Found": {},
                "Application Detection": {},
                "Credential Guessing": {},
                "Authentication Bypass": {},
                "Apache ETag header Found vulnerability": {},
                "VMware Server-side Template Injection (RCE) Vulnerability": {},
                "Apache OFBiz Authentication Bypass Vulnerability (CVE-2023-51467)": {},
                "ExtJs Arbitrary File Read": {},
                "Reveals Sensitive Information": {
                    "SenInfobodyFound": true
                },
                "SQL Injection": {},
                "Apache Struts": {},
                "Cross Site Scripting (XSS)": {},
                "Host Header Injection": {},
                "XPATH Injection": {},
                "OS Command Injection": {},
                "Cross-Site Tracing (XST)": {},
                "Cross-Site Tracing (XST) OOB": {},
                "User-Controllable-Tag-Parameter": {},
                "Unencoded character check": {},
                "Iframe Injection": {},
                "Link Injection": {},
                "Web cache poisoning attack": {},
                "Exchange Server SSRF attack": {},
                "Breach attack": {},
                "ESI Injection vulnerability": {},
                "Apache log4j vulnerability": {},
                "OS Command Inj OOB": {},
                "SQL Injection OOB": {},
                "Cross Site Scripting (XSS) OOB": {},
                "Host Header Injection OOB": {},
                "Server-Side Template Injection OOB": {},
                "Client-Side Template Injection OOB": {},
                "Spring Expression Resource Access RCE": {},
                "Code Injection OOB": {},
                "Link Injection OOB": {},
                "Iframe Injection OOB": {},
                "Text Injection": {},
                "WebSocket URL poisoning": {},
                "Form Action Hijacking": {},
                "Code Injection Attack": {},
                "Expression Language Injection": {},
                "DEBUG method enabled": {},
                "PROPFIND method enabled": {},
                "TRACE TRACK method enabled": {},
                "Options enabled": {},
                "HTTP Verb Tampering": {},
                "Cookie Checker": {
                    "sessionCookieScopeVulnerabilityFound": true,
                    "cookiePathOverlyBroadVulnerabilityFound": true
                }
            },
            "httpResponse": {
                "err": null,
                "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"original-5.body\"}",
                "headers": {
                    "date": "Sat, 01 Jun 2024 14:34:58 GMT",
                    "content-type": "text/html; charset=utf-8",
                    "content-length": "20867",
                    "connection": "close",
                    "content-encoding": "gzip",
                    "content-security-policy": "frame-ancestors 'self' https://microapps.google.com; report-uri https://www.practo.com/consumer-home/cspreport",
                    "etag": "W/\"15ba7-serd1CemNFu+DwsMUth43/5mZ58\"",
                    "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
                    "vary": "Accept-Encoding",
                    "x-content-type-options": "nosniff",
                    "x-frame-options": "SAMEORIGIN",
                    "x-powered-by": "Express",
                    "x-xss-protection": "1; mode=block",
                    "cf-cache-status": "DYNAMIC",
                    "server": "cloudflare",
                    "cf-ray": "88cfed718f734206-BOM",
                    "alt-svc": "h3=\":443\"; ma=86400"
                },
                "statusCode": 200,
                "statusMessage": "OK",
                "redirectsFollowed": 0,
                "redirects": []
            }
        },
        "href": "https://www.practo.com/owa/auth/x.js",
        "area": "HTTPHeaders",
        "type": "value",
        "param": "User-Agent",
        "vector": "Mozilla/5.0 (Windows NT 10.0; rv:68.0) Gecko/20100101 Firefox/68.0",
        "encoding": "raw",
        "headers": {
            "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
            "accept-language": "en-US",
            "cookie": "X-AnonResource=true; X-AnonResource-Backend=localhost/ecp/default.flt?~3; X-BEResource=localhost/owa/auth/logon.aspx?~3;; PHPSESSID=96fkcd63lbc35kjq09dp6llg37; availability_pla_client_id=225BEC25-57C0-AB3B-BBE5-157C3B44D0CB",
            "sec-fetch-dest": "document",
            "sec-fetch-mode": "navigate",
            "sec-fetch-site": "none",
            "sec-fetch-user": "?1",
            "upgrade-insecure-requests": "1",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; rv:68.0) Gecko/20100101 Firefox/68.0",
            "host": "www.practo.com",
            "accept-encoding": "gzip, deflate"
        },
        "httpRequest": {
            "method": "GET",
            "headers": {
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept-Language": "en-US",
                "Cookie": "X-AnonResource=true; X-AnonResource-Backend=localhost/ecp/default.flt?~3; X-BEResource=localhost/owa/auth/logon.aspx?~3;",
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1",
                "Upgrade-Insecure-Requests": "1",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; rv:68.0) Gecko/20100101 Firefox/68.0"
            },
            "appTranaKey": "GET-practo.com/?",
            "crawlerBookmark": {
                "state": 0,
                "action": 3,
                "bookmarkId": "state=0|action=3",
                "actionList": {
                    "serliaziedName": "./datastructure\\action-list.js",
                    "args": ["bookmark", [{
                                "serliaziedName": "./datastructure\\action-list.js",
                                "args": ["initial actions", [{
                                            "serliaziedName": "./datastructure\\load-action.js",
                                            "args": ["[initial]", "https://www.practo.com/portalservermb/", "initial, mustRunAtInit: true"]
                                        }
                                    ], "seq", true]
                            }
                        ], "seq", false]
                },
                "rootActionCount": 1
            },
            "resourceType": "mainFrame",
            "crawlerKey": "GET-practo.com/?",
            "uri": "https://www.practo.com/owa/auth/x.js",
            "haikuKey": "GET-practo.com/?",
            "haikuResourceType": "core",
            "scanlog_id": 325,
            "scanId": 325,
            "attackRequestId": 865
        },
        "httpResponse": {
            "headers": {
                "date": "Sat, 01 Jun 2024 14:39:59 GMT",
                "content-type": "text/html; charset=utf-8",
                "transfer-encoding": "chunked",
                "connection": "close",
                "cid": "cb3f25758d65c9de807b83a13b46e74f",
                "content-security-policy-report-only": "default-src 'self' *.practo.com *.practostatic.com ; base-uri none; object-src none; script-src https://js-agent.newrelic.com https://bam.nr-data.net https://bam-cell.nr-data.net 'nonce-vq2BuHwg2jC7W3q5PGAsTLZKWbjXVnou' 'self' 'strict-dynamic' 'unsafe-eval' 'unsafe-inline' *.ampproject.org *.criteo.com *.criteo.net *.doubleclick.net *.facebook.com *.facebook.net *.google-analytics.com *.google.com *.googleadservices.com *.googleapis.com *.googlesyndication.com *.googletagmanager.com *.googletagservices.com *.gstatic.com *.mixpanel.com *.mxpnl.com *.netcore.co.in *.netcoresmartech.com *.onesignal.com *.practo.com *.practostatic.com *.speedcurve.com *.survicate.com *.twitter.com https://secure.livechatinc.com in.wzrkt.com ; style-src 'self' 'unsafe-inline' *.googleapis.com *.practo.com *.practostatic.com *.survicate.com ; connect-src https://bam.nr-data.net https://bam-cell.nr-data.net 'self' *.addthis.com *.ampproject.org *.criteo.com *.criteo.net *.doubleclick.net *.facebook.com *.facebook.net *.freshchat.io *.google-analytics.com *.google.com *.googleadservices.com *.googleapis.com *.googlesyndication.com *.googletagmanager.com *.gstatic.com *.hotjar.com *.mixpanel.com *.mxpnl.com *.netcore.co.in *.netcoresmartech.com *.onesignal.com *.practo.com *.practostatic.com *.speedcurve.com *.survicate.com *.twimg.com *.twitter.com *.ytimg.com hn.inspectlet.com ; img-src * data: ; font-src 'self' *.gstatic.com *.practo.com *.practostatic.com *.survicate.com data: ; frame-src 'self' *.criteo.com *.criteo.net *.doubleclick.net *.google.com *.googlesyndication.com *.practo.com https://secure.livechatinc.com https://survicate.com https://www.facebook.com ; worker-src 'self' *.practostatic.com ; report-uri https://www.practo.com/topaz/cspreport",
                "country": "IN",
                "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
                "x-powered-by": "Express",
                "cf-cache-status": "MISS",
                "vary": "Accept-Encoding",
                "set-cookie": ["__cfruid=8d37cb336149be71f82f3f84bcca4b9dbe18fc8e-1717252799; path=/; domain=.practo.com; HttpOnly; Secure; SameSite=None"],
                "server": "cloudflare",
                "cf-ray": "88cff4c8f97d80af-BOM",
                "alt-svc": "h3=\":443\"; ma=86400"
            },
            "statusCode": 544,
            "statusMessage": "unknown",
            "httpVersion": "1.1",
            "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"attack-6.body\"}"
        }
    },
    "response": {
        "headers": {
            "date": "Sat, 01 Jun 2024 14:39:59 GMT",
            "content-type": "text/html; charset=utf-8",
            "transfer-encoding": "chunked",
            "connection": "close",
            "cid": "cb3f25758d65c9de807b83a13b46e74f",
            "content-security-policy-report-only": "default-src 'self' *.practo.com *.practostatic.com ; base-uri none; object-src none; script-src https://js-agent.newrelic.com https://bam.nr-data.net https://bam-cell.nr-data.net 'nonce-vq2BuHwg2jC7W3q5PGAsTLZKWbjXVnou' 'self' 'strict-dynamic' 'unsafe-eval' 'unsafe-inline' *.ampproject.org *.criteo.com *.criteo.net *.doubleclick.net *.facebook.com *.facebook.net *.google-analytics.com *.google.com *.googleadservices.com *.googleapis.com *.googlesyndication.com *.googletagmanager.com *.googletagservices.com *.gstatic.com *.mixpanel.com *.mxpnl.com *.netcore.co.in *.netcoresmartech.com *.onesignal.com *.practo.com *.practostatic.com *.speedcurve.com *.survicate.com *.twitter.com https://secure.livechatinc.com in.wzrkt.com ; style-src 'self' 'unsafe-inline' *.googleapis.com *.practo.com *.practostatic.com *.survicate.com ; connect-src https://bam.nr-data.net https://bam-cell.nr-data.net 'self' *.addthis.com *.ampproject.org *.criteo.com *.criteo.net *.doubleclick.net *.facebook.com *.facebook.net *.freshchat.io *.google-analytics.com *.google.com *.googleadservices.com *.googleapis.com *.googlesyndication.com *.googletagmanager.com *.gstatic.com *.hotjar.com *.mixpanel.com *.mxpnl.com *.netcore.co.in *.netcoresmartech.com *.onesignal.com *.practo.com *.practostatic.com *.speedcurve.com *.survicate.com *.twimg.com *.twitter.com *.ytimg.com hn.inspectlet.com ; img-src * data: ; font-src 'self' *.gstatic.com *.practo.com *.practostatic.com *.survicate.com data: ; frame-src 'self' *.criteo.com *.criteo.net *.doubleclick.net *.google.com *.googlesyndication.com *.practo.com https://secure.livechatinc.com https://survicate.com https://www.facebook.com ; worker-src 'self' *.practostatic.com ; report-uri https://www.practo.com/topaz/cspreport",
            "country": "IN",
            "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
            "x-powered-by": "Express",
            "cf-cache-status": "MISS",
            "vary": "Accept-Encoding",
            "set-cookie": ["__cfruid=8d37cb336149be71f82f3f84bcca4b9dbe18fc8e-1717252799; path=/; domain=.practo.com; HttpOnly; Secure; SameSite=None"],
            "server": "cloudflare",
            "cf-ray": "88cff4c8f97d80af-BOM",
            "alt-svc": "h3=\":443\"; ma=86400"
        },
        "statusCode": 544,
        "statusMessage": "unknown",
        "httpVersion": "1.1",
        "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"attack-6.body\"}"
    },
    "vulns": {
        "ID-session-cookie-scoped-parent-domain": {
            "foundBy": "Cookie Checker",
            "productionReady": true,
            "canAttackInReplayScan": true,
            "details": {
                "name": "ID-session-cookie-scoped-parent-domain",
                "details": [{
                        "name": "Set-Cookie",
                        "value": ".practo.com",
                        "fullCookie": "__cfruid=8d37cb336149be71f82f3f84bcca4b9dbe18fc8e-1717252799; path=/; domain=.practo.com; HttpOnly; Secure; SameSite=None"
                    }
                ]
            },
            "autoPOC": [{
                    "type": "original",
                    "path": "httpRequest.headers",
                    "highlightType": "param",
                    "details": ["User-Agent"],
                    "description": "orignal request header User-Agent param value will be attack"
                }, {
                    "type": "attack",
                    "path": "httpRequest.headers",
                    "highlightType": "param",
                    "details": ["User-Agent"],
                    "description": "attack request header User-Agent param value tampered"
                }
            ]
        },
        "ID-broad-cookie-path": {
            "foundBy": "Cookie Checker",
            "productionReady": true,
            "canAttackInReplayScan": true,
            "details": {
                "details": [{
                        "name": "Set-Cookie",
                        "value": "/",
                        "fullCookie": "__cfruid=8d37cb336149be71f82f3f84bcca4b9dbe18fc8e-1717252799; path=/; domain=.practo.com; HttpOnly; Secure; SameSite=None"
                    }
                ]
            },
            "autoPOC": [{
                    "type": "original",
                    "path": "httpRequest.headers",
                    "highlightType": "param",
                    "details": ["User-Agent"],
                    "description": "orignal request header User-Agent param value will be attack"
                }, {
                    "type": "attack",
                    "path": "httpRequest.headers",
                    "highlightType": "param",
                    "details": ["User-Agent"],
                    "description": "attack request header User-Agent param value tampered"
                }
            ]
        }
    }
} {
    "scanId": 325,
    "scanlogId": 325,
    "attack": {
        "attackCount": 1,
        "method": "GET",
        "name": "Cookie Manipulation",
        "hostname": "www.practo.com",
        "originalRequest": {
            "scanId": 325,
            "scanlog_id": 325,
            "scanner": "haiku",
            "httpRequest": {
                "method": "GET",
                "headers": {
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
                    "Accept-Encoding": "gzip, deflate, br",
                    "Accept-Language": "en-US",
                    "Cookie": "__cfruid=391b4bcdfe3a1f3fc7fabd6640449f1e335a6053-1717252440; _fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _ga=GA1.2.527041583.**********; _gid=GA1.2.315403102.**********; _gat=1; _gat_fabric=1; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A8%2C%22s%22%3A**********%2C%22t%22%3A1717252489%7D; mp_85d643d7bc71611832663cd683666848_mixpanel=%7B%22distinct_id%22%3A%20%22%24device%3A18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24device_id%22%3A%20%2218fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%2C%22__mps%22%3A%20%7B%7D%2C%22__mpso%22%3A%20%7B%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%7D%2C%22__mpus%22%3A%20%7B%7D%2C%22__mpa%22%3A%20%7B%7D%2C%22__mpu%22%3A%20%7B%7D%2C%22__mpr%22%3A%20%5B%5D%2C%22__mpap%22%3A%20%5B%5D%7D; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252489.12.0.0",
                    "Sec-Fetch-Dest": "document",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Site": "none",
                    "Sec-Fetch-User": "?1",
                    "Upgrade-Insecure-Requests": "1",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36"
                },
                "appTranaKey": "GET-practo.com/?",
                "crawlerBookmark": {
                    "state": 0,
                    "action": 3,
                    "bookmarkId": "state=0|action=3",
                    "actionList": {
                        "serliaziedName": "./datastructure\\action-list.js",
                        "args": ["bookmark", [{
                                    "serliaziedName": "./datastructure\\action-list.js",
                                    "args": ["initial actions", [{
                                                "serliaziedName": "./datastructure\\load-action.js",
                                                "args": ["[initial]", "https://www.practo.com/portalservermb/", "initial, mustRunAtInit: true"]
                                            }
                                        ], "seq", true]
                                }
                            ], "seq", false]
                    },
                    "rootActionCount": 1
                },
                "resourceType": "mainFrame",
                "crawlerKey": "GET-practo.com/?",
                "uri": "https://www.practo.com/",
                "haikuKey": "GET-practo.com/?",
                "haikuResourceType": "core",
                "scanlog_id": 325,
                "scanId": 325,
                "attackRequestId": 319
            },
            "requestId": 7,
            "haikuPriority": {
                "priority": 100,
                "requestParams": [],
                "nonEmptyValueCount": 0,
                "pathComponentsCount": 0
            },
            "pluginData": {
                "Remote File Inclusion (RFI)": {},
                "Cross Origin Resource Sharing": {},
                "Email Address Found": {
                    "emailidFound": true
                },
                "Possible Sensitive Directory or file": {},
                "Authentication details found": {},
                "Clickjacking - HTTP Header Check": {
                    "XpcdpHdrVulnFound": true
                },
                "HTTP Header Info Disclosure": {},
                "Web Server details found": {
                    "serverdtsfound": true,
                    "Appdtsfound": true
                },
                "Password-submitted-without-ssl": {},
                "Insecure HTTP Transport": {
                    "insecureCount": 88990
                },
                "Insecure HTTP Method Transition": {
                    "InsecureHTTPMethod": {
                        "OriResBodylength": 88990
                    }
                },
                "Sensitive-information-cached": {
                    "sensitiveInfoCacheVulnFound": true
                },
                "Application-Error-Message": {},
                "Cookie Manipulation": {
                    "originalChecksum": "af2a0cfe14558625103f1d82e8078d2c",
                    "cookieMisconfiguration": true
                },
                "Weak Session ID": {},
                "Insecure-Flash-Embed-Param-Found": {},
                "Application Detection": {},
                "Credential Guessing": {},
                "Authentication Bypass": {},
                "Apache ETag header Found vulnerability": {},
                "VMware Server-side Template Injection (RCE) Vulnerability": {},
                "Apache OFBiz Authentication Bypass Vulnerability (CVE-2023-51467)": {},
                "ExtJs Arbitrary File Read": {},
                "Reveals Sensitive Information": {
                    "SenInfobodyFound": true
                },
                "SQL Injection": {},
                "Apache Struts": {},
                "Cross Site Scripting (XSS)": {},
                "Host Header Injection": {},
                "XPATH Injection": {},
                "OS Command Injection": {},
                "Cross-Site Tracing (XST)": {},
                "Cross-Site Tracing (XST) OOB": {},
                "User-Controllable-Tag-Parameter": {},
                "Unencoded character check": {},
                "Iframe Injection": {},
                "Link Injection": {},
                "Web cache poisoning attack": {},
                "Exchange Server SSRF attack": {},
                "Breach attack": {},
                "ESI Injection vulnerability": {},
                "Apache log4j vulnerability": {},
                "OS Command Inj OOB": {},
                "SQL Injection OOB": {},
                "Cross Site Scripting (XSS) OOB": {},
                "Host Header Injection OOB": {},
                "Server-Side Template Injection OOB": {},
                "Client-Side Template Injection OOB": {},
                "Spring Expression Resource Access RCE": {},
                "Code Injection OOB": {},
                "Link Injection OOB": {},
                "Iframe Injection OOB": {},
                "Text Injection": {},
                "WebSocket URL poisoning": {},
                "Form Action Hijacking": {},
                "Code Injection Attack": {},
                "Expression Language Injection": {},
                "DEBUG method enabled": {},
                "PROPFIND method enabled": {},
                "TRACE TRACK method enabled": {},
                "Options enabled": {},
                "HTTP Verb Tampering": {},
                "Cookie Checker": {
                    "sessionCookieScopeVulnerabilityFound": true,
                    "cookiePathOverlyBroadVulnerabilityFound": true
                }
            },
            "httpResponse": {
                "err": null,
                "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"original-5.body\"}",
                "headers": {
                    "date": "Sat, 01 Jun 2024 14:34:58 GMT",
                    "content-type": "text/html; charset=utf-8",
                    "content-length": "20867",
                    "connection": "close",
                    "content-encoding": "gzip",
                    "content-security-policy": "frame-ancestors 'self' https://microapps.google.com; report-uri https://www.practo.com/consumer-home/cspreport",
                    "etag": "W/\"15ba7-serd1CemNFu+DwsMUth43/5mZ58\"",
                    "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
                    "vary": "Accept-Encoding",
                    "x-content-type-options": "nosniff",
                    "x-frame-options": "SAMEORIGIN",
                    "x-powered-by": "Express",
                    "x-xss-protection": "1; mode=block",
                    "cf-cache-status": "DYNAMIC",
                    "server": "cloudflare",
                    "cf-ray": "88cfed718f734206-BOM",
                    "alt-svc": "h3=\":443\"; ma=86400"
                },
                "statusCode": 200,
                "statusMessage": "OK",
                "redirectsFollowed": 0,
                "redirects": []
            }
        },
        "href": "https://www.practo.com/",
        "area": "Cookies",
        "type": "value",
        "param": "PHPSESSID",
        "vector": "abcd1234",
        "encoding": "raw",
        "headers": {
            "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
            "accept-language": "en-US",
            "cookie": "__cfruid=391b4bcdfe3a1f3fc7fabd6640449f1e335a6053-1717252440;_fbp=fb.1.1717252440910.**********;google_one_tap_attempt=1;_gcl_au=1.1.**********.**********;_ga=GA1.2.527041583.**********;_gid=GA1.2.315403102.**********;_gat=1;_gat_fabric=1;WZRK_G=72255c01167541c6bef49d46aed8c328;pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547;PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20abcd1234;availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF;initialReferrer=;WZRK_S_8W6-695-WK5Z={\"p\":8,\"s\":**********,\"t\":1717252489};mp_85d643d7bc71611832663cd683666848_mixpanel={\"distinct_id\": \"$device:18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776\",\"$device_id\": \"18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776\",\"$initial_referrer\": \"$direct\",\"$initial_referring_domain\": \"$direct\",\"__mps\": {},\"__mpso\": {\"$initial_referrer\": \"$direct\",\"$initial_referring_domain\": \"$direct\"},\"__mpus\": {},\"__mpa\": {},\"__mpu\": {},\"__mpr\": [],\"__mpap\": []};_ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252489.12.0.0; PHPSESSID=96fkcd63lbc35kjq09dp6llg37; availability_pla_client_id=225BEC25-57C0-AB3B-BBE5-157C3B44D0CB; __cfruid=8d37cb336149be71f82f3f84bcca4b9dbe18fc8e-1717252799",
            "sec-fetch-dest": "document",
            "sec-fetch-mode": "navigate",
            "sec-fetch-site": "none",
            "sec-fetch-user": "?1",
            "upgrade-insecure-requests": "1",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36",
            "host": "www.practo.com",
            "accept-encoding": "gzip, deflate"
        },
        "httpRequest": {
            "method": "GET",
            "headers": {
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept-Language": "en-US",
                "Cookie": "__cfruid=391b4bcdfe3a1f3fc7fabd6640449f1e335a6053-1717252440;_fbp=fb.1.1717252440910.**********;google_one_tap_attempt=1;_gcl_au=1.1.**********.**********;_ga=GA1.2.527041583.**********;_gid=GA1.2.315403102.**********;_gat=1;_gat_fabric=1;WZRK_G=72255c01167541c6bef49d46aed8c328;pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547;PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20abcd1234;availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF;initialReferrer=;WZRK_S_8W6-695-WK5Z={\"p\":8,\"s\":**********,\"t\":1717252489};mp_85d643d7bc71611832663cd683666848_mixpanel={\"distinct_id\": \"$device:18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776\",\"$device_id\": \"18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776\",\"$initial_referrer\": \"$direct\",\"$initial_referring_domain\": \"$direct\",\"__mps\": {},\"__mpso\": {\"$initial_referrer\": \"$direct\",\"$initial_referring_domain\": \"$direct\"},\"__mpus\": {},\"__mpa\": {},\"__mpu\": {},\"__mpr\": [],\"__mpap\": []};_ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252489.12.0.0",
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1",
                "Upgrade-Insecure-Requests": "1",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36"
            },
            "appTranaKey": "GET-practo.com/?",
            "crawlerBookmark": {
                "state": 0,
                "action": 3,
                "bookmarkId": "state=0|action=3",
                "actionList": {
                    "serliaziedName": "./datastructure\\action-list.js",
                    "args": ["bookmark", [{
                                "serliaziedName": "./datastructure\\action-list.js",
                                "args": ["initial actions", [{
                                            "serliaziedName": "./datastructure\\load-action.js",
                                            "args": ["[initial]", "https://www.practo.com/portalservermb/", "initial, mustRunAtInit: true"]
                                        }
                                    ], "seq", true]
                            }
                        ], "seq", false]
                },
                "rootActionCount": 1
            },
            "resourceType": "mainFrame",
            "crawlerKey": "GET-practo.com/?",
            "uri": "https://www.practo.com/",
            "haikuKey": "GET-practo.com/?",
            "haikuResourceType": "core",
            "scanlog_id": 325,
            "scanId": 325,
            "attackRequestId": 892
        },
        "httpResponse": {
            "headers": {
                "date": "Sat, 01 Jun 2024 14:40:00 GMT",
                "content-type": "text/html; charset=utf-8",
                "content-length": "20867",
                "connection": "close",
                "content-encoding": "gzip",
                "content-security-policy": "frame-ancestors 'self' https://microapps.google.com; report-uri https://www.practo.com/consumer-home/cspreport",
                "etag": "W/\"15ba7-serd1CemNFu+DwsMUth43/5mZ58\"",
                "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
                "vary": "Accept-Encoding",
                "x-content-type-options": "nosniff",
                "x-frame-options": "SAMEORIGIN",
                "x-powered-by": "Express",
                "x-xss-protection": "1; mode=block",
                "cf-cache-status": "DYNAMIC",
                "server": "cloudflare",
                "cf-ray": "88cff4cfbbdb3a33-BOM",
                "alt-svc": "h3=\":443\"; ma=86400"
            },
            "statusCode": 200,
            "statusMessage": "OK",
            "httpVersion": "1.1",
            "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"attack-7.body\"}"
        }
    },
    "response": {
        "headers": {
            "date": "Sat, 01 Jun 2024 14:40:00 GMT",
            "content-type": "text/html; charset=utf-8",
            "content-length": "20867",
            "connection": "close",
            "content-encoding": "gzip",
            "content-security-policy": "frame-ancestors 'self' https://microapps.google.com; report-uri https://www.practo.com/consumer-home/cspreport",
            "etag": "W/\"15ba7-serd1CemNFu+DwsMUth43/5mZ58\"",
            "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
            "vary": "Accept-Encoding",
            "x-content-type-options": "nosniff",
            "x-frame-options": "SAMEORIGIN",
            "x-powered-by": "Express",
            "x-xss-protection": "1; mode=block",
            "cf-cache-status": "DYNAMIC",
            "server": "cloudflare",
            "cf-ray": "88cff4cfbbdb3a33-BOM",
            "alt-svc": "h3=\":443\"; ma=86400"
        },
        "statusCode": 200,
        "statusMessage": "OK",
        "httpVersion": "1.1",
        "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"attack-7.body\"}"
    },
    "vulns": {
        "ID-cookie-manipulation": {
            "foundBy": "Cookie Manipulation",
            "productionReady": true,
            "canAttackInReplayScan": true,
            "details": {
                "href": "https://www.practo.com/",
                "attackedCookieName": "PHPSESSID",
                "wholeCookie": "__cfruid=391b4bcdfe3a1f3fc7fabd6640449f1e335a6053-1717252440;_fbp=fb.1.1717252440910.**********;google_one_tap_attempt=1;_gcl_au=1.1.**********.**********;_ga=GA1.2.527041583.**********;_gid=GA1.2.315403102.**********;_gat=1;_gat_fabric=1;WZRK_G=72255c01167541c6bef49d46aed8c328;pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547;PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20abcd1234;availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF;initialReferrer=;WZRK_S_8W6-695-WK5Z={\"p\":8,\"s\":**********,\"t\":1717252489};mp_85d643d7bc71611832663cd683666848_mixpanel={\"distinct_id\": \"$device:18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776\",\"$device_id\": \"18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776\",\"$initial_referrer\": \"$direct\",\"$initial_referring_domain\": \"$direct\",\"__mps\": {},\"__mpso\": {\"$initial_referrer\": \"$direct\",\"$initial_referring_domain\": \"$direct\"},\"__mpus\": {},\"__mpa\": {},\"__mpu\": {},\"__mpr\": [],\"__mpap\": []};_ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252489.12.0.0"
            },
            "autoPOC": [{
                    "type": "original",
                    "path": "httpRequest.headers.cookie",
                    "highlightType": "text",
                    "details": ["PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20"],
                    "description": "orignal request cookie PHPSESSID param value will be attack"
                }, {
                    "type": "attack",
                    "path": "httpRequest.headers.cookie",
                    "highlightType": "text",
                    "details": ["PHPSESSID=abcd1234"],
                    "description": "attack request cookie PHPSESSID param value tampered with vector abcd1234"
                }, {
                    "type": "original",
                    "path": "httpRequest.uri",
                    "highlightType": "param",
                    "details": ["https://www.practo.com/"],
                    "description": "PHPSESSID cookie can be misconfigured for the url https://www.practo.com/\nAttacked cookie: __cfruid=391b4bcdfe3a1f3fc7fabd6640449f1e335a6053-1717252440;_fbp=fb.1.1717252440910.**********;google_one_tap_attempt=1;_gcl_au=1.1.**********.**********;_ga=GA1.2.527041583.**********;_gid=GA1.2.315403102.**********;_gat=1;_gat_fabric=1;WZRK_G=72255c01167541c6bef49d46aed8c328;pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547;PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20abcd1234;availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF;initialReferrer=;WZRK_S_8W6-695-WK5Z={\"p\":8,\"s\":**********,\"t\":1717252489};mp_85d643d7bc71611832663cd683666848_mixpanel={\"distinct_id\": \"$device:18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776\",\"$device_id\": \"18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776\",\"$initial_referrer\": \"$direct\",\"$initial_referring_domain\": \"$direct\",\"__mps\": {},\"__mpso\": {\"$initial_referrer\": \"$direct\",\"$initial_referring_domain\": \"$direct\"},\"__mpus\": {},\"__mpa\": {},\"__mpu\": {},\"__mpr\": [],\"__mpap\": []};_ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252489.12.0.0"
                }, {
                    "type": "attack",
                    "path": "httpRequest.uri",
                    "highlightType": "param",
                    "details": ["https://www.practo.com/"],
                    "description": "PHPSESSID cookie can be misconfigured for the url https://www.practo.com/\nAttacked cookie: __cfruid=391b4bcdfe3a1f3fc7fabd6640449f1e335a6053-1717252440;_fbp=fb.1.1717252440910.**********;google_one_tap_attempt=1;_gcl_au=1.1.**********.**********;_ga=GA1.2.527041583.**********;_gid=GA1.2.315403102.**********;_gat=1;_gat_fabric=1;WZRK_G=72255c01167541c6bef49d46aed8c328;pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547;PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20abcd1234;availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF;initialReferrer=;WZRK_S_8W6-695-WK5Z={\"p\":8,\"s\":**********,\"t\":1717252489};mp_85d643d7bc71611832663cd683666848_mixpanel={\"distinct_id\": \"$device:18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776\",\"$device_id\": \"18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776\",\"$initial_referrer\": \"$direct\",\"$initial_referring_domain\": \"$direct\",\"__mps\": {},\"__mpso\": {\"$initial_referrer\": \"$direct\",\"$initial_referring_domain\": \"$direct\"},\"__mpus\": {},\"__mpa\": {},\"__mpu\": {},\"__mpr\": [],\"__mpap\": []};_ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252489.12.0.0"
                }, {
                    "type": "attack",
                    "path": "httpResponse",
                    "highlightType": "param",
                    "details": ["statusCode"],
                    "description": "PHPSESSID cookie can be misconfigured for the url https://www.practo.com/\nAttacked cookie: __cfruid=391b4bcdfe3a1f3fc7fabd6640449f1e335a6053-1717252440;_fbp=fb.1.1717252440910.**********;google_one_tap_attempt=1;_gcl_au=1.1.**********.**********;_ga=GA1.2.527041583.**********;_gid=GA1.2.315403102.**********;_gat=1;_gat_fabric=1;WZRK_G=72255c01167541c6bef49d46aed8c328;pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547;PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20abcd1234;availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF;initialReferrer=;WZRK_S_8W6-695-WK5Z={\"p\":8,\"s\":**********,\"t\":1717252489};mp_85d643d7bc71611832663cd683666848_mixpanel={\"distinct_id\": \"$device:18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776\",\"$device_id\": \"18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776\",\"$initial_referrer\": \"$direct\",\"$initial_referring_domain\": \"$direct\",\"__mps\": {},\"__mpso\": {\"$initial_referrer\": \"$direct\",\"$initial_referring_domain\": \"$direct\"},\"__mpus\": {},\"__mpa\": {},\"__mpu\": {},\"__mpr\": [],\"__mpap\": []};_ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252489.12.0.0"
                }, {
                    "type": "attack",
                    "path": "httpRequest.headers.Cookie",
                    "highlightType": "text",
                    "details": ["abcd1234"],
                    "description": "PHPSESSID cookie can be misconfigured for the url https://www.practo.com/\nAttacked cookie: __cfruid=391b4bcdfe3a1f3fc7fabd6640449f1e335a6053-1717252440;_fbp=fb.1.1717252440910.**********;google_one_tap_attempt=1;_gcl_au=1.1.**********.**********;_ga=GA1.2.527041583.**********;_gid=GA1.2.315403102.**********;_gat=1;_gat_fabric=1;WZRK_G=72255c01167541c6bef49d46aed8c328;pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547;PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20abcd1234;availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF;initialReferrer=;WZRK_S_8W6-695-WK5Z={\"p\":8,\"s\":**********,\"t\":1717252489};mp_85d643d7bc71611832663cd683666848_mixpanel={\"distinct_id\": \"$device:18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776\",\"$device_id\": \"18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776\",\"$initial_referrer\": \"$direct\",\"$initial_referring_domain\": \"$direct\",\"__mps\": {},\"__mpso\": {\"$initial_referrer\": \"$direct\",\"$initial_referring_domain\": \"$direct\"},\"__mpus\": {},\"__mpa\": {},\"__mpu\": {},\"__mpr\": [],\"__mpap\": []};_ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252489.12.0.0"
                }
            ]
        }
    }
} {
    "scanId": 325,
    "scanlogId": 325,
    "attack": {
        "attackCount": 1,
        "method": "GET",
        "name": "Web cache poisoning attack",
        "hostname": "www.practo.com",
        "originalRequest": {
            "scanId": 325,
            "scanlog_id": 325,
            "scanner": "haiku",
            "httpRequest": {
                "method": "GET",
                "headers": {
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
                    "Accept-Encoding": "gzip, deflate, br",
                    "Accept-Language": "en-US",
                    "Cookie": "__cfruid=391b4bcdfe3a1f3fc7fabd6640449f1e335a6053-1717252440; _fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _ga=GA1.2.527041583.**********; _gid=GA1.2.315403102.**********; _gat=1; _gat_fabric=1; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A8%2C%22s%22%3A**********%2C%22t%22%3A1717252489%7D; mp_85d643d7bc71611832663cd683666848_mixpanel=%7B%22distinct_id%22%3A%20%22%24device%3A18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24device_id%22%3A%20%2218fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%2C%22__mps%22%3A%20%7B%7D%2C%22__mpso%22%3A%20%7B%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%7D%2C%22__mpus%22%3A%20%7B%7D%2C%22__mpa%22%3A%20%7B%7D%2C%22__mpu%22%3A%20%7B%7D%2C%22__mpr%22%3A%20%5B%5D%2C%22__mpap%22%3A%20%5B%5D%7D; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252489.12.0.0",
                    "Sec-Fetch-Dest": "document",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Site": "none",
                    "Sec-Fetch-User": "?1",
                    "Upgrade-Insecure-Requests": "1",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36"
                },
                "appTranaKey": "GET-practo.com/?",
                "crawlerBookmark": {
                    "state": 0,
                    "action": 3,
                    "bookmarkId": "state=0|action=3",
                    "actionList": {
                        "serliaziedName": "./datastructure\\action-list.js",
                        "args": ["bookmark", [{
                                    "serliaziedName": "./datastructure\\action-list.js",
                                    "args": ["initial actions", [{
                                                "serliaziedName": "./datastructure\\load-action.js",
                                                "args": ["[initial]", "https://www.practo.com/portalservermb/", "initial, mustRunAtInit: true"]
                                            }
                                        ], "seq", true]
                                }
                            ], "seq", false]
                    },
                    "rootActionCount": 1
                },
                "resourceType": "mainFrame",
                "crawlerKey": "GET-practo.com/?",
                "uri": "https://www.practo.com/",
                "haikuKey": "GET-practo.com/?",
                "haikuResourceType": "core",
                "scanlog_id": 325,
                "scanId": 325,
                "attackRequestId": 319
            },
            "requestId": 7,
            "haikuPriority": {
                "priority": 100,
                "requestParams": [],
                "nonEmptyValueCount": 0,
                "pathComponentsCount": 0
            },
            "pluginData": {
                "Remote File Inclusion (RFI)": {},
                "Cross Origin Resource Sharing": {},
                "Email Address Found": {
                    "emailidFound": true
                },
                "Possible Sensitive Directory or file": {},
                "Authentication details found": {},
                "Clickjacking - HTTP Header Check": {
                    "XpcdpHdrVulnFound": true
                },
                "HTTP Header Info Disclosure": {},
                "Web Server details found": {
                    "serverdtsfound": true,
                    "Appdtsfound": true
                },
                "Password-submitted-without-ssl": {},
                "Insecure HTTP Transport": {
                    "insecureCount": 88990
                },
                "Insecure HTTP Method Transition": {
                    "InsecureHTTPMethod": {
                        "OriResBodylength": 88990
                    }
                },
                "Sensitive-information-cached": {
                    "sensitiveInfoCacheVulnFound": true
                },
                "Application-Error-Message": {},
                "Cookie Manipulation": {
                    "originalChecksum": "af2a0cfe14558625103f1d82e8078d2c",
                    "cookieMisconfiguration": true
                },
                "Weak Session ID": {},
                "Insecure-Flash-Embed-Param-Found": {},
                "Application Detection": {},
                "Credential Guessing": {},
                "Authentication Bypass": {},
                "Apache ETag header Found vulnerability": {},
                "VMware Server-side Template Injection (RCE) Vulnerability": {},
                "Apache OFBiz Authentication Bypass Vulnerability (CVE-2023-51467)": {},
                "ExtJs Arbitrary File Read": {},
                "Reveals Sensitive Information": {
                    "SenInfobodyFound": true
                },
                "SQL Injection": {},
                "Apache Struts": {},
                "Cross Site Scripting (XSS)": {},
                "Host Header Injection": {},
                "XPATH Injection": {},
                "OS Command Injection": {},
                "Cross-Site Tracing (XST)": {},
                "Cross-Site Tracing (XST) OOB": {},
                "User-Controllable-Tag-Parameter": {},
                "Unencoded character check": {},
                "Iframe Injection": {},
                "Link Injection": {},
                "Web cache poisoning attack": {},
                "Exchange Server SSRF attack": {},
                "Breach attack": {},
                "ESI Injection vulnerability": {},
                "Apache log4j vulnerability": {},
                "OS Command Inj OOB": {},
                "SQL Injection OOB": {},
                "Cross Site Scripting (XSS) OOB": {},
                "Host Header Injection OOB": {},
                "Server-Side Template Injection OOB": {},
                "Client-Side Template Injection OOB": {},
                "Spring Expression Resource Access RCE": {},
                "Code Injection OOB": {},
                "Link Injection OOB": {},
                "Iframe Injection OOB": {},
                "Text Injection": {},
                "WebSocket URL poisoning": {},
                "Form Action Hijacking": {},
                "Code Injection Attack": {},
                "Expression Language Injection": {},
                "DEBUG method enabled": {},
                "PROPFIND method enabled": {},
                "TRACE TRACK method enabled": {},
                "Options enabled": {},
                "HTTP Verb Tampering": {},
                "Cookie Checker": {
                    "sessionCookieScopeVulnerabilityFound": true,
                    "cookiePathOverlyBroadVulnerabilityFound": true
                }
            },
            "httpResponse": {
                "err": null,
                "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"original-5.body\"}",
                "headers": {
                    "date": "Sat, 01 Jun 2024 14:34:58 GMT",
                    "content-type": "text/html; charset=utf-8",
                    "content-length": "20867",
                    "connection": "close",
                    "content-encoding": "gzip",
                    "content-security-policy": "frame-ancestors 'self' https://microapps.google.com; report-uri https://www.practo.com/consumer-home/cspreport",
                    "etag": "W/\"15ba7-serd1CemNFu+DwsMUth43/5mZ58\"",
                    "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
                    "vary": "Accept-Encoding",
                    "x-content-type-options": "nosniff",
                    "x-frame-options": "SAMEORIGIN",
                    "x-powered-by": "Express",
                    "x-xss-protection": "1; mode=block",
                    "cf-cache-status": "DYNAMIC",
                    "server": "cloudflare",
                    "cf-ray": "88cfed718f734206-BOM",
                    "alt-svc": "h3=\":443\"; ma=86400"
                },
                "statusCode": 200,
                "statusMessage": "OK",
                "redirectsFollowed": 0,
                "redirects": []
            }
        },
        "href": "https://www.practo.com/",
        "area": "HTTPHeaders",
        "type": "value",
        "param": "X-Forwarded-Host",
        "vector": "\"<a href=\"http://was.indusface.com/###\" class='haikumsg1234'>clickme</a><!--",
        "encoding": "raw",
        "headers": {
            "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
            "accept-language": "en-US",
            "cookie": "__cfruid=391b4bcdfe3a1f3fc7fabd6640449f1e335a6053-1717252440; _fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _ga=GA1.2.527041583.**********; _gid=GA1.2.315403102.**********; _gat=1; _gat_fabric=1; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A8%2C%22s%22%3A**********%2C%22t%22%3A1717252489%7D; mp_85d643d7bc71611832663cd683666848_mixpanel=%7B%22distinct_id%22%3A%20%22%24device%3A18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24device_id%22%3A%20%2218fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%2C%22__mps%22%3A%20%7B%7D%2C%22__mpso%22%3A%20%7B%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%7D%2C%22__mpus%22%3A%20%7B%7D%2C%22__mpa%22%3A%20%7B%7D%2C%22__mpu%22%3A%20%7B%7D%2C%22__mpr%22%3A%20%5B%5D%2C%22__mpap%22%3A%20%5B%5D%7D; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252489.12.0.0; PHPSESSID=96fkcd63lbc35kjq09dp6llg37; availability_pla_client_id=225BEC25-57C0-AB3B-BBE5-157C3B44D0CB; __cfruid=64752c86a324f63889faf0c486b0a6b9b7c10043-1717252801",
            "sec-fetch-dest": "document",
            "sec-fetch-mode": "navigate",
            "sec-fetch-site": "none",
            "sec-fetch-user": "?1",
            "upgrade-insecure-requests": "1",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36",
            "x-forwarded-host": "<a href=\"http://was.indusface.com/\" class='haikumsg1234'>clickme</a>",
            "x-host": "<a href=\"http://was.indusface.com/\" class='haikumsg1234'>clickme</a>",
            "x-forwarded-for": "<a href=\"http://was.indusface.com/\" class='haikumsg1234'>clickme</a>",
            "x-forwarded-proto": "<a href=\"http://was.indusface.com/\" class='haikumsg1234'>clickme</a>",
            "x-forwarded-ssl": "<a href=\"http://was.indusface.com/\" class='haikumsg1234'>clickme</a>",
            "host": "www.practo.com",
            "accept-encoding": "gzip, deflate"
        },
        "httpRequest": {
            "method": "GET",
            "headers": {
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept-Language": "en-US",
                "Cookie": "__cfruid=391b4bcdfe3a1f3fc7fabd6640449f1e335a6053-1717252440; _fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _ga=GA1.2.527041583.**********; _gid=GA1.2.315403102.**********; _gat=1; _gat_fabric=1; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A8%2C%22s%22%3A**********%2C%22t%22%3A1717252489%7D; mp_85d643d7bc71611832663cd683666848_mixpanel=%7B%22distinct_id%22%3A%20%22%24device%3A18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24device_id%22%3A%20%2218fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%2C%22__mps%22%3A%20%7B%7D%2C%22__mpso%22%3A%20%7B%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%7D%2C%22__mpus%22%3A%20%7B%7D%2C%22__mpa%22%3A%20%7B%7D%2C%22__mpu%22%3A%20%7B%7D%2C%22__mpr%22%3A%20%5B%5D%2C%22__mpap%22%3A%20%5B%5D%7D; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252489.12.0.0",
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1",
                "Upgrade-Insecure-Requests": "1",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36",
                "X-Forwarded-Host": "<a href=\"http://was.indusface.com/\" class='haikumsg1234'>clickme</a>",
                "x-host": "<a href=\"http://was.indusface.com/\" class='haikumsg1234'>clickme</a>",
                "X-Forwarded-For": "<a href=\"http://was.indusface.com/\" class='haikumsg1234'>clickme</a>",
                "X-Forwarded-Proto": "<a href=\"http://was.indusface.com/\" class='haikumsg1234'>clickme</a>",
                "X-Forwarded-Ssl": "<a href=\"http://was.indusface.com/\" class='haikumsg1234'>clickme</a>"
            },
            "appTranaKey": "GET-practo.com/?",
            "crawlerBookmark": {
                "state": 0,
                "action": 3,
                "bookmarkId": "state=0|action=3",
                "actionList": {
                    "serliaziedName": "./datastructure\\action-list.js",
                    "args": ["bookmark", [{
                                "serliaziedName": "./datastructure\\action-list.js",
                                "args": ["initial actions", [{
                                            "serliaziedName": "./datastructure\\load-action.js",
                                            "args": ["[initial]", "https://www.practo.com/portalservermb/", "initial, mustRunAtInit: true"]
                                        }
                                    ], "seq", true]
                            }
                        ], "seq", false]
                },
                "rootActionCount": 1
            },
            "resourceType": "mainFrame",
            "crawlerKey": "GET-practo.com/?",
            "uri": "https://www.practo.com/",
            "haikuKey": "GET-practo.com/?",
            "haikuResourceType": "core",
            "scanlog_id": 325,
            "scanId": 325,
            "attackRequestId": 942
        },
        "httpResponse": {
            "headers": {
                "date": "Sat, 01 Jun 2024 14:40:01 GMT",
                "content-type": "text/html; charset=UTF-8",
                "transfer-encoding": "chunked",
                "connection": "close",
                "x-frame-options": "SAMEORIGIN",
                "referrer-policy": "same-origin",
                "cache-control": "max-age=15",
                "expires": "Sat, 01 Jun 2024 14:40:16 GMT",
                "server": "cloudflare",
                "cf-ray": "88cff4db0bdb85ee-BOM",
                "content-encoding": "gzip",
                "alt-svc": "h3=\":443\"; ma=86400"
            },
            "statusCode": 403,
            "statusMessage": "Forbidden",
            "httpVersion": "1.1",
            "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"attack-8.body\"}"
        }
    },
    "response": {
        "headers": {
            "date": "Sat, 01 Jun 2024 14:40:01 GMT",
            "content-type": "text/html; charset=UTF-8",
            "transfer-encoding": "chunked",
            "connection": "close",
            "x-frame-options": "SAMEORIGIN",
            "referrer-policy": "same-origin",
            "cache-control": "max-age=15",
            "expires": "Sat, 01 Jun 2024 14:40:16 GMT",
            "server": "cloudflare",
            "cf-ray": "88cff4db0bdb85ee-BOM",
            "content-encoding": "gzip",
            "alt-svc": "h3=\":443\"; ma=86400"
        },
        "statusCode": 403,
        "statusMessage": "Forbidden",
        "httpVersion": "1.1",
        "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"attack-8.body\"}"
    },
    "vulns": {
        "ID-waf-ips-found": {
            "foundBy": "WAF IPS Detected",
            "productionReady": true,
            "canAttackInReplayScan": false,
            "details": {
                "details": {
                    "serverHeader": null,
                    "header": "cf-ray",
                    "cookie": null,
                    "context": "you have been blocked"
                }
            },
            "autoPOC": [{
                    "type": "original",
                    "path": "httpRequest.headers",
                    "highlightType": "param",
                    "details": ["X-Forwarded-Host"],
                    "description": "orignal request header X-Forwarded-Host param value will be attack"
                }, {
                    "type": "attack",
                    "path": "httpRequest.headers",
                    "highlightType": "param",
                    "details": ["X-Forwarded-Host"],
                    "description": "attack request header X-Forwarded-Host param value tampered"
                }
            ]
        }
    }
} {
    "scanId": 325,
    "scanlogId": 325,
    "attack": {
        "attackCount": 1,
        "method": "GET",
        "name": "Text Injection",
        "hostname": "www.practo.com",
        "originalRequest": {
            "scanId": 325,
            "scanlog_id": 325,
            "scanner": "haiku",
            "httpRequest": {
                "method": "GET",
                "headers": {
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
                    "Accept-Encoding": "gzip, deflate, br",
                    "Accept-Language": "en-US",
                    "Cookie": "__cfruid=391b4bcdfe3a1f3fc7fabd6640449f1e335a6053-1717252440; _fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _ga=GA1.2.527041583.**********; _gid=GA1.2.315403102.**********; _gat=1; _gat_fabric=1; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A8%2C%22s%22%3A**********%2C%22t%22%3A1717252489%7D; mp_85d643d7bc71611832663cd683666848_mixpanel=%7B%22distinct_id%22%3A%20%22%24device%3A18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24device_id%22%3A%20%2218fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%2C%22__mps%22%3A%20%7B%7D%2C%22__mpso%22%3A%20%7B%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%7D%2C%22__mpus%22%3A%20%7B%7D%2C%22__mpa%22%3A%20%7B%7D%2C%22__mpu%22%3A%20%7B%7D%2C%22__mpr%22%3A%20%5B%5D%2C%22__mpap%22%3A%20%5B%5D%7D; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252489.12.0.0",
                    "Sec-Fetch-Dest": "document",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Site": "none",
                    "Sec-Fetch-User": "?1",
                    "Upgrade-Insecure-Requests": "1",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36"
                },
                "appTranaKey": "GET-practo.com/?",
                "crawlerBookmark": {
                    "state": 0,
                    "action": 3,
                    "bookmarkId": "state=0|action=3",
                    "actionList": {
                        "serliaziedName": "./datastructure\\action-list.js",
                        "args": ["bookmark", [{
                                    "serliaziedName": "./datastructure\\action-list.js",
                                    "args": ["initial actions", [{
                                                "serliaziedName": "./datastructure\\load-action.js",
                                                "args": ["[initial]", "https://www.practo.com/portalservermb/", "initial, mustRunAtInit: true"]
                                            }
                                        ], "seq", true]
                                }
                            ], "seq", false]
                    },
                    "rootActionCount": 1
                },
                "resourceType": "mainFrame",
                "crawlerKey": "GET-practo.com/?",
                "uri": "https://www.practo.com/",
                "haikuKey": "GET-practo.com/?",
                "haikuResourceType": "core",
                "scanlog_id": 325,
                "scanId": 325,
                "attackRequestId": 319
            },
            "requestId": 7,
            "haikuPriority": {
                "priority": 100,
                "requestParams": [],
                "nonEmptyValueCount": 0,
                "pathComponentsCount": 0
            },
            "pluginData": {
                "Remote File Inclusion (RFI)": {},
                "Cross Origin Resource Sharing": {},
                "Email Address Found": {
                    "emailidFound": true
                },
                "Possible Sensitive Directory or file": {},
                "Authentication details found": {},
                "Clickjacking - HTTP Header Check": {
                    "XpcdpHdrVulnFound": true
                },
                "HTTP Header Info Disclosure": {},
                "Web Server details found": {
                    "serverdtsfound": true,
                    "Appdtsfound": true
                },
                "Password-submitted-without-ssl": {},
                "Insecure HTTP Transport": {
                    "insecureCount": 88990
                },
                "Insecure HTTP Method Transition": {
                    "InsecureHTTPMethod": {
                        "OriResBodylength": 88990
                    }
                },
                "Sensitive-information-cached": {
                    "sensitiveInfoCacheVulnFound": true
                },
                "Application-Error-Message": {},
                "Cookie Manipulation": {
                    "originalChecksum": "af2a0cfe14558625103f1d82e8078d2c",
                    "cookieMisconfiguration": true
                },
                "Weak Session ID": {},
                "Insecure-Flash-Embed-Param-Found": {},
                "Application Detection": {},
                "Credential Guessing": {},
                "Authentication Bypass": {},
                "Apache ETag header Found vulnerability": {},
                "VMware Server-side Template Injection (RCE) Vulnerability": {},
                "Apache OFBiz Authentication Bypass Vulnerability (CVE-2023-51467)": {},
                "ExtJs Arbitrary File Read": {},
                "Reveals Sensitive Information": {
                    "SenInfobodyFound": true
                },
                "SQL Injection": {},
                "Apache Struts": {},
                "Cross Site Scripting (XSS)": {},
                "Host Header Injection": {},
                "XPATH Injection": {},
                "OS Command Injection": {},
                "Cross-Site Tracing (XST)": {},
                "Cross-Site Tracing (XST) OOB": {},
                "User-Controllable-Tag-Parameter": {},
                "Unencoded character check": {},
                "Iframe Injection": {},
                "Link Injection": {},
                "Web cache poisoning attack": {},
                "Exchange Server SSRF attack": {},
                "Breach attack": {},
                "ESI Injection vulnerability": {},
                "Apache log4j vulnerability": {},
                "OS Command Inj OOB": {},
                "SQL Injection OOB": {},
                "Cross Site Scripting (XSS) OOB": {},
                "Host Header Injection OOB": {},
                "Server-Side Template Injection OOB": {},
                "Client-Side Template Injection OOB": {},
                "Spring Expression Resource Access RCE": {},
                "Code Injection OOB": {},
                "Link Injection OOB": {},
                "Iframe Injection OOB": {},
                "Text Injection": {},
                "WebSocket URL poisoning": {},
                "Form Action Hijacking": {},
                "Code Injection Attack": {},
                "Expression Language Injection": {},
                "DEBUG method enabled": {},
                "PROPFIND method enabled": {},
                "TRACE TRACK method enabled": {},
                "Options enabled": {},
                "HTTP Verb Tampering": {},
                "Cookie Checker": {
                    "sessionCookieScopeVulnerabilityFound": true,
                    "cookiePathOverlyBroadVulnerabilityFound": true,
                    "httpOnlyVulnerabilityFound": true,
                    "secureVulnerabilityFound": true
                }
            },
            "httpResponse": {
                "err": null,
                "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"original-5.body\"}",
                "headers": {
                    "date": "Sat, 01 Jun 2024 14:34:58 GMT",
                    "content-type": "text/html; charset=utf-8",
                    "content-length": "20867",
                    "connection": "close",
                    "content-encoding": "gzip",
                    "content-security-policy": "frame-ancestors 'self' https://microapps.google.com; report-uri https://www.practo.com/consumer-home/cspreport",
                    "etag": "W/\"15ba7-serd1CemNFu+DwsMUth43/5mZ58\"",
                    "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
                    "vary": "Accept-Encoding",
                    "x-content-type-options": "nosniff",
                    "x-frame-options": "SAMEORIGIN",
                    "x-powered-by": "Express",
                    "x-xss-protection": "1; mode=block",
                    "cf-cache-status": "DYNAMIC",
                    "server": "cloudflare",
                    "cf-ray": "88cfed718f734206-BOM",
                    "alt-svc": "h3=\":443\"; ma=86400"
                },
                "statusCode": 200,
                "statusMessage": "OK",
                "redirectsFollowed": 0,
                "redirects": []
            }
        },
        "href": "https://www.practo.com/",
        "area": "HTTPHeaders",
        "type": "value",
        "param": "Cookie",
        "vector": " Site is moved to wastest.indusface.com kindly visit wastest.indusface.com. ",
        "encoding": "raw",
        "headers": {
            "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
            "accept-language": "en-US",
            "cookie": " Site is moved to wastest.indusface.com kindly visit wastest.indusface.com. ; PHPSESSID=96fkcd63lbc35kjq09dp6llg37; availability_pla_client_id=225BEC25-57C0-AB3B-BBE5-157C3B44D0CB; __cfruid=2c1793d96012239a95caca6d8719786bc049de07-1717252802",
            "sec-fetch-dest": "document",
            "sec-fetch-mode": "navigate",
            "sec-fetch-site": "none",
            "sec-fetch-user": "?1",
            "upgrade-insecure-requests": "1",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36",
            "host": "www.practo.com",
            "accept-encoding": "gzip, deflate"
        },
        "httpRequest": {
            "method": "GET",
            "headers": {
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept-Language": "en-US",
                "Cookie": " Site is moved to wastest.indusface.com kindly visit wastest.indusface.com. ",
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1",
                "Upgrade-Insecure-Requests": "1",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36"
            },
            "appTranaKey": "GET-practo.com/?",
            "crawlerBookmark": {
                "state": 0,
                "action": 3,
                "bookmarkId": "state=0|action=3",
                "actionList": {
                    "serliaziedName": "./datastructure\\action-list.js",
                    "args": ["bookmark", [{
                                "serliaziedName": "./datastructure\\action-list.js",
                                "args": ["initial actions", [{
                                            "serliaziedName": "./datastructure\\load-action.js",
                                            "args": ["[initial]", "https://www.practo.com/portalservermb/", "initial, mustRunAtInit: true"]
                                        }
                                    ], "seq", true]
                            }
                        ], "seq", false]
                },
                "rootActionCount": 1
            },
            "resourceType": "mainFrame",
            "crawlerKey": "GET-practo.com/?",
            "uri": "https://www.practo.com/",
            "haikuKey": "GET-practo.com/?",
            "haikuResourceType": "core",
            "scanlog_id": 325,
            "scanId": 325,
            "attackRequestId": 973
        },
        "httpResponse": {
            "headers": {
                "date": "Sat, 01 Jun 2024 14:40:02 GMT",
                "content-type": "text/html; charset=utf-8",
                "content-length": "20867",
                "connection": "close",
                "content-encoding": "gzip",
                "content-security-policy": "frame-ancestors 'self' https://microapps.google.com; report-uri https://www.practo.com/consumer-home/cspreport",
                "etag": "W/\"15ba7-serd1CemNFu+DwsMUth43/5mZ58\"",
                "set-cookie": ["pelUUID=9a4c9333-61ca-4408-a9a8-5f9c99cc748a; Path=/; Expires=Sun, 01 Jun 2025 14:40:02 GMT", "__cfruid=80c442c1a872f541d5c11906b5031fde123ec8ac-1717252802; path=/; domain=.practo.com; HttpOnly; Secure; SameSite=None"],
                "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
                "vary": "Accept-Encoding",
                "x-content-type-options": "nosniff",
                "x-frame-options": "SAMEORIGIN",
                "x-powered-by": "Express",
                "x-xss-protection": "1; mode=block",
                "cf-cache-status": "DYNAMIC",
                "server": "cloudflare",
                "cf-ray": "88cff4e01e4e3c70-BOM",
                "alt-svc": "h3=\":443\"; ma=86400"
            },
            "statusCode": 200,
            "statusMessage": "OK",
            "httpVersion": "1.1",
            "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"attack-9.body\"}"
        }
    },
    "response": {
        "headers": {
            "date": "Sat, 01 Jun 2024 14:40:02 GMT",
            "content-type": "text/html; charset=utf-8",
            "content-length": "20867",
            "connection": "close",
            "content-encoding": "gzip",
            "content-security-policy": "frame-ancestors 'self' https://microapps.google.com; report-uri https://www.practo.com/consumer-home/cspreport",
            "etag": "W/\"15ba7-serd1CemNFu+DwsMUth43/5mZ58\"",
            "set-cookie": ["pelUUID=9a4c9333-61ca-4408-a9a8-5f9c99cc748a; Path=/; Expires=Sun, 01 Jun 2025 14:40:02 GMT", "__cfruid=80c442c1a872f541d5c11906b5031fde123ec8ac-1717252802; path=/; domain=.practo.com; HttpOnly; Secure; SameSite=None"],
            "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
            "vary": "Accept-Encoding",
            "x-content-type-options": "nosniff",
            "x-frame-options": "SAMEORIGIN",
            "x-powered-by": "Express",
            "x-xss-protection": "1; mode=block",
            "cf-cache-status": "DYNAMIC",
            "server": "cloudflare",
            "cf-ray": "88cff4e01e4e3c70-BOM",
            "alt-svc": "h3=\":443\"; ma=86400"
        },
        "statusCode": 200,
        "statusMessage": "OK",
        "httpVersion": "1.1",
        "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"attack-9.body\"}"
    },
    "vulns": {
        "ID-cookie-httponly-not-set": {
            "foundBy": "Cookie Checker",
            "productionReady": true,
            "canAttackInReplayScan": true,
            "details": {
                "name": "Cookie-HttpOnly-Not-Set",
                "details": [{
                        "name": "Set-Cookie",
                        "value": "9a4c9333-61ca-4408-a9a8-5f9c99cc748a",
                        "httpOnlyMissing": true,
                        "fullCookie": "pelUUID=9a4c9333-61ca-4408-a9a8-5f9c99cc748a; Path=/; Expires=Sun, 01 Jun 2025 14:40:02 GMT"
                    }
                ]
            },
            "autoPOC": [{
                    "type": "original",
                    "path": "httpRequest.headers",
                    "highlightType": "param",
                    "details": ["Cookie"],
                    "description": "orignal request header Cookie param value will be attack"
                }, {
                    "type": "attack",
                    "path": "httpRequest.headers",
                    "highlightType": "param",
                    "details": ["Cookie"],
                    "description": "attack request header Cookie param value tampered"
                }
            ]
        },
        "ID-cookie-secure-not-set": {
            "foundBy": "Cookie Checker",
            "productionReady": true,
            "canAttackInReplayScan": true,
            "details": {
                "name": "Cookie-Secure-Not-Set",
                "details": [{
                        "name": "Set-Cookie",
                        "value": "9a4c9333-61ca-4408-a9a8-5f9c99cc748a",
                        "secureMissing": true,
                        "fullCookie": "pelUUID=9a4c9333-61ca-4408-a9a8-5f9c99cc748a; Path=/; Expires=Sun, 01 Jun 2025 14:40:02 GMT"
                    }
                ]
            },
            "autoPOC": [{
                    "type": "original",
                    "path": "httpRequest.headers",
                    "highlightType": "param",
                    "details": ["Cookie"],
                    "description": "orignal request header Cookie param value will be attack"
                }, {
                    "type": "attack",
                    "path": "httpRequest.headers",
                    "highlightType": "param",
                    "details": ["Cookie"],
                    "description": "attack request header Cookie param value tampered"
                }
            ]
        }
    }
} {
    "scanId": 325,
    "scanlogId": 325,
    "attack": {
        "attackCount": 1,
        "method": "GET",
        "name": "Iframe Injection OOB",
        "hostname": "www.practo.com",
        "originalRequest": {
            "scanId": 325,
            "scanlog_id": 325,
            "scanner": "haiku",
            "httpRequest": {
                "method": "GET",
                "headers": {
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
                    "Accept-Encoding": "gzip, deflate, br",
                    "Accept-Language": "en-US",
                    "Cookie": "__cfruid=391b4bcdfe3a1f3fc7fabd6640449f1e335a6053-1717252440; _fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _ga=GA1.2.527041583.**********; _gid=GA1.2.315403102.**********; _gat=1; _gat_fabric=1; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A8%2C%22s%22%3A**********%2C%22t%22%3A1717252489%7D; mp_85d643d7bc71611832663cd683666848_mixpanel=%7B%22distinct_id%22%3A%20%22%24device%3A18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24device_id%22%3A%20%2218fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%2C%22__mps%22%3A%20%7B%7D%2C%22__mpso%22%3A%20%7B%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%7D%2C%22__mpus%22%3A%20%7B%7D%2C%22__mpa%22%3A%20%7B%7D%2C%22__mpu%22%3A%20%7B%7D%2C%22__mpr%22%3A%20%5B%5D%2C%22__mpap%22%3A%20%5B%5D%7D; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252489.12.0.0",
                    "Sec-Fetch-Dest": "document",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Site": "none",
                    "Sec-Fetch-User": "?1",
                    "Upgrade-Insecure-Requests": "1",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36"
                },
                "appTranaKey": "GET-practo.com/?",
                "crawlerBookmark": {
                    "state": 0,
                    "action": 3,
                    "bookmarkId": "state=0|action=3",
                    "actionList": {
                        "serliaziedName": "./datastructure\\action-list.js",
                        "args": ["bookmark", [{
                                    "serliaziedName": "./datastructure\\action-list.js",
                                    "args": ["initial actions", [{
                                                "serliaziedName": "./datastructure\\load-action.js",
                                                "args": ["[initial]", "https://www.practo.com/portalservermb/", "initial, mustRunAtInit: true"]
                                            }
                                        ], "seq", true]
                                }
                            ], "seq", false]
                    },
                    "rootActionCount": 1
                },
                "resourceType": "mainFrame",
                "crawlerKey": "GET-practo.com/?",
                "uri": "https://www.practo.com/",
                "haikuKey": "GET-practo.com/?",
                "haikuResourceType": "core",
                "scanlog_id": 325,
                "scanId": 325,
                "attackRequestId": 319
            },
            "requestId": 7,
            "haikuPriority": {
                "priority": 100,
                "requestParams": [],
                "nonEmptyValueCount": 0,
                "pathComponentsCount": 0
            },
            "pluginData": {
                "Remote File Inclusion (RFI)": {},
                "Cross Origin Resource Sharing": {},
                "Email Address Found": {
                    "emailidFound": true
                },
                "Possible Sensitive Directory or file": {},
                "Authentication details found": {},
                "Clickjacking - HTTP Header Check": {
                    "XpcdpHdrVulnFound": true
                },
                "HTTP Header Info Disclosure": {},
                "Web Server details found": {
                    "serverdtsfound": true,
                    "Appdtsfound": true
                },
                "Password-submitted-without-ssl": {},
                "Insecure HTTP Transport": {
                    "insecureCount": 88990
                },
                "Insecure HTTP Method Transition": {
                    "InsecureHTTPMethod": {
                        "OriResBodylength": 88990
                    }
                },
                "Sensitive-information-cached": {
                    "sensitiveInfoCacheVulnFound": true
                },
                "Application-Error-Message": {},
                "Cookie Manipulation": {
                    "originalChecksum": "af2a0cfe14558625103f1d82e8078d2c",
                    "cookieMisconfiguration": true
                },
                "Weak Session ID": {},
                "Insecure-Flash-Embed-Param-Found": {},
                "Application Detection": {},
                "Credential Guessing": {},
                "Authentication Bypass": {},
                "Apache ETag header Found vulnerability": {},
                "VMware Server-side Template Injection (RCE) Vulnerability": {},
                "Apache OFBiz Authentication Bypass Vulnerability (CVE-2023-51467)": {},
                "ExtJs Arbitrary File Read": {},
                "Reveals Sensitive Information": {
                    "SenInfobodyFound": true
                },
                "SQL Injection": {},
                "Apache Struts": {},
                "Cross Site Scripting (XSS)": {},
                "Host Header Injection": {},
                "XPATH Injection": {},
                "OS Command Injection": {},
                "Cross-Site Tracing (XST)": {},
                "Cross-Site Tracing (XST) OOB": {},
                "User-Controllable-Tag-Parameter": {},
                "Unencoded character check": {},
                "Iframe Injection": {},
                "Link Injection": {},
                "Web cache poisoning attack": {},
                "Exchange Server SSRF attack": {},
                "Breach attack": {},
                "ESI Injection vulnerability": {},
                "Apache log4j vulnerability": {},
                "OS Command Inj OOB": {},
                "SQL Injection OOB": {},
                "Cross Site Scripting (XSS) OOB": {},
                "Host Header Injection OOB": {},
                "Server-Side Template Injection OOB": {},
                "Client-Side Template Injection OOB": {},
                "Spring Expression Resource Access RCE": {},
                "Code Injection OOB": {},
                "Link Injection OOB": {},
                "Iframe Injection OOB": {},
                "Text Injection": {},
                "WebSocket URL poisoning": {},
                "Form Action Hijacking": {},
                "Code Injection Attack": {},
                "Expression Language Injection": {},
                "DEBUG method enabled": {},
                "PROPFIND method enabled": {},
                "TRACE TRACK method enabled": {},
                "Options enabled": {},
                "HTTP Verb Tampering": {},
                "Cookie Checker": {
                    "sessionCookieScopeVulnerabilityFound": true,
                    "cookiePathOverlyBroadVulnerabilityFound": true,
                    "httpOnlyVulnerabilityFound": true,
                    "secureVulnerabilityFound": true
                }
            },
            "httpResponse": {
                "err": null,
                "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"original-5.body\"}",
                "headers": {
                    "date": "Sat, 01 Jun 2024 14:34:58 GMT",
                    "content-type": "text/html; charset=utf-8",
                    "content-length": "20867",
                    "connection": "close",
                    "content-encoding": "gzip",
                    "content-security-policy": "frame-ancestors 'self' https://microapps.google.com; report-uri https://www.practo.com/consumer-home/cspreport",
                    "etag": "W/\"15ba7-serd1CemNFu+DwsMUth43/5mZ58\"",
                    "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
                    "vary": "Accept-Encoding",
                    "x-content-type-options": "nosniff",
                    "x-frame-options": "SAMEORIGIN",
                    "x-powered-by": "Express",
                    "x-xss-protection": "1; mode=block",
                    "cf-cache-status": "DYNAMIC",
                    "server": "cloudflare",
                    "cf-ray": "88cfed718f734206-BOM",
                    "alt-svc": "h3=\":443\"; ma=86400"
                },
                "statusCode": 200,
                "statusMessage": "OK",
                "redirectsFollowed": 0,
                "redirects": []
            }
        },
        "href": "https://www.practo.com/",
        "area": "HTTPHeaders",
        "type": "value",
        "param": "Referer",
        "vector": "'\"-->'\"><iframe/src=\"http://ee8f3da2-0052-4111-9e79-68a86f8d8479.undefined.325.325.344609.haikuscan.indusfacefinder.in\"/class='haikumsg'></iframe>",
        "encoding": "raw",
        "headers": {
            "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
            "accept-language": "en-US",
            "cookie": "__cfruid=391b4bcdfe3a1f3fc7fabd6640449f1e335a6053-1717252440; _fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _ga=GA1.2.527041583.**********; _gid=GA1.2.315403102.**********; _gat=1; _gat_fabric=1; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A8%2C%22s%22%3A**********%2C%22t%22%3A1717252489%7D; mp_85d643d7bc71611832663cd683666848_mixpanel=%7B%22distinct_id%22%3A%20%22%24device%3A18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24device_id%22%3A%20%2218fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%2C%22__mps%22%3A%20%7B%7D%2C%22__mpso%22%3A%20%7B%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%7D%2C%22__mpus%22%3A%20%7B%7D%2C%22__mpa%22%3A%20%7B%7D%2C%22__mpu%22%3A%20%7B%7D%2C%22__mpr%22%3A%20%5B%5D%2C%22__mpap%22%3A%20%5B%5D%7D; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252489.12.0.0; PHPSESSID=96fkcd63lbc35kjq09dp6llg37; availability_pla_client_id=225BEC25-57C0-AB3B-BBE5-157C3B44D0CB; __cfruid=8d37cb336149be71f82f3f84bcca4b9dbe18fc8e-1717252799",
            "sec-fetch-dest": "document",
            "sec-fetch-mode": "navigate",
            "sec-fetch-site": "none",
            "sec-fetch-user": "?1",
            "upgrade-insecure-requests": "1",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36",
            "referer": "'\"-->'\"><iframe/src=\"http://ee8f3da2-0052-4111-9e79-68a86f8d8479.undefined.325.325.344609.haikuscan.indusfacefinder.in\"/class='haikumsg'></iframe>",
            "host": "www.practo.com",
            "accept-encoding": "gzip, deflate"
        },
        "httpRequest": {
            "method": "GET",
            "headers": {
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept-Language": "en-US",
                "Cookie": "__cfruid=391b4bcdfe3a1f3fc7fabd6640449f1e335a6053-1717252440; _fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _ga=GA1.2.527041583.**********; _gid=GA1.2.315403102.**********; _gat=1; _gat_fabric=1; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A8%2C%22s%22%3A**********%2C%22t%22%3A1717252489%7D; mp_85d643d7bc71611832663cd683666848_mixpanel=%7B%22distinct_id%22%3A%20%22%24device%3A18fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24device_id%22%3A%20%2218fd43938cd775-066e4ee1bc6fe1-b321f6c-e1000-18fd43938ce776%22%2C%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%2C%22__mps%22%3A%20%7B%7D%2C%22__mpso%22%3A%20%7B%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%7D%2C%22__mpus%22%3A%20%7B%7D%2C%22__mpa%22%3A%20%7B%7D%2C%22__mpu%22%3A%20%7B%7D%2C%22__mpr%22%3A%20%5B%5D%2C%22__mpap%22%3A%20%5B%5D%7D; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252489.12.0.0",
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1",
                "Upgrade-Insecure-Requests": "1",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36",
                "Referer": "'\"-->'\"><iframe/src=\"http://ee8f3da2-0052-4111-9e79-68a86f8d8479.undefined.325.325.344609.haikuscan.indusfacefinder.in\"/class='haikumsg'></iframe>"
            },
            "appTranaKey": "GET-practo.com/?",
            "crawlerBookmark": {
                "state": 0,
                "action": 3,
                "bookmarkId": "state=0|action=3",
                "actionList": {
                    "serliaziedName": "./datastructure\\action-list.js",
                    "args": ["bookmark", [{
                                "serliaziedName": "./datastructure\\action-list.js",
                                "args": ["initial actions", [{
                                            "serliaziedName": "./datastructure\\load-action.js",
                                            "args": ["[initial]", "https://www.practo.com/portalservermb/", "initial, mustRunAtInit: true"]
                                        }
                                    ], "seq", true]
                            }
                        ], "seq", false]
                },
                "rootActionCount": 1
            },
            "resourceType": "mainFrame",
            "crawlerKey": "GET-practo.com/?",
            "uri": "https://www.practo.com/",
            "haikuKey": "GET-practo.com/?",
            "haikuResourceType": "core",
            "scanlog_id": 325,
            "scanId": 325,
            "attackRequestId": 878
        },
        "uuid": "ee8f3da2-0052-4111-9e79-68a86f8d8479",
        "uploadAttackToS3": false,
        "httpResponse": {
            "headers": {
                "date": "Sat, 01 Jun 2024 14:40:04 GMT",
                "content-type": "text/html; charset=utf-8",
                "content-length": "20867",
                "connection": "close",
                "content-encoding": "gzip",
                "content-security-policy": "frame-ancestors 'self' https://microapps.google.com; report-uri https://www.practo.com/consumer-home/cspreport",
                "etag": "W/\"15ba7-serd1CemNFu+DwsMUth43/5mZ58\"",
                "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
                "vary": "Accept-Encoding",
                "x-content-type-options": "nosniff",
                "x-frame-options": "SAMEORIGIN",
                "x-powered-by": "Express",
                "x-xss-protection": "1; mode=block",
                "cf-cache-status": "DYNAMIC",
                "server": "cloudflare",
                "cf-ray": "88cff4ebedd14904-BOM",
                "alt-svc": "h3=\":443\"; ma=86400"
            },
            "statusCode": 200,
            "statusMessage": "OK",
            "httpVersion": "1.1",
            "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"attack-10.body\"}"
        }
    },
    "response": {
        "headers": {
            "date": "Sat, 01 Jun 2024 14:40:04 GMT",
            "content-type": "text/html; charset=utf-8",
            "content-length": "20867",
            "connection": "close",
            "content-encoding": "gzip",
            "content-security-policy": "frame-ancestors 'self' https://microapps.google.com; report-uri https://www.practo.com/consumer-home/cspreport",
            "etag": "W/\"15ba7-serd1CemNFu+DwsMUth43/5mZ58\"",
            "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
            "vary": "Accept-Encoding",
            "x-content-type-options": "nosniff",
            "x-frame-options": "SAMEORIGIN",
            "x-powered-by": "Express",
            "x-xss-protection": "1; mode=block",
            "cf-cache-status": "DYNAMIC",
            "server": "cloudflare",
            "cf-ray": "88cff4ebedd14904-BOM",
            "alt-svc": "h3=\":443\"; ma=86400"
        },
        "statusCode": 200,
        "statusMessage": "OK",
        "httpVersion": "1.1",
        "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"attack-10.body\"}"
    },
    "vulns": {
        "ID-slow-response-time": {
            "foundBy": "Slow Response Time",
            "productionReady": true,
            "canAttackInReplayScan": false,
            "details": {
                "allTimings": {
                    "socket": 1.2808999996632338,
                    "lookup": 1.6704000001773238,
                    "connect": 1029.3514999998733,
                    "response": 5399.60869999975,
                    "end": 5456.096199999563
                },
                "allTimingPhases": {
                    "wait": 1.2808999996632338,
                    "dns": 0.38950000051409006,
                    "tcp": 1027.681099999696,
                    "firstByte": 4370.257199999876,
                    "download": 56.487499999813735,
                    "total": 5456.096199999563
                }
            },
            "autoPOC": [{
                    "type": "original",
                    "path": "httpRequest.headers",
                    "highlightType": "param",
                    "details": ["Referer"],
                    "description": "orignal request header Referer param value will be attack"
                }, {
                    "type": "attack",
                    "path": "httpRequest.headers",
                    "highlightType": "param",
                    "details": ["Referer"],
                    "description": "attack request header Referer param value tampered"
                }
            ]
        }
    }
} {
    "scanId": 325,
    "scanlogId": 325,
    "attack": {
        "attackCount": 1,
        "method": "GET",
        "name": "Original Crawler Request",
        "hostname": "www.practo.com",
        "originalRequest": {
            "scanId": 325,
            "scanlog_id": 325,
            "scanner": "haiku",
            "httpRequest": {
                "method": "GET",
                "headers": {
                    "Accept": "application/json",
                    "Accept-Encoding": "gzip, deflate, br",
                    "Accept-Language": "en-US",
                    "Cookie": "_fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _gid=GA1.2.315403102.**********; _ga=GA1.2.527041583.**********; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; p_attr_info=%7B%22session_referrer%22%3A%22Direct%22%2C%22landing_page%22%3A%22Listing%22%7D; hl=en; __practo_sweep__ses.ab61=*; _clck=o68dwj%7C2%7Cfm9%7C0%7C1613; _ga_VFVCCQSS9F=GS1.2.**********.1.1.**********.20.0.0; user_session=0g242k8ldmi33of47797m862m1; consult-attribution={%22source%22:%22consumer-home%22%2C%22medium%22:%22web%22%2C%22campaign%22:%22top_symptoms%22}; _hjSessionUser_828794=eyJpZCI6IjA4NWU4MjFkLTM3MTgtNWU5NC1iNzJlLTdhODM2ODMzZjBmMyIsImNyZWF0ZWQiOjE3MTcyNTI1NzA4ODIsImV4aXN0aW5nIjp0cnVlfQ==; _hjSession_828794=eyJpZCI6IjIyYjgxYzNmLWIyZjctNDQ5MS04MTAzLTBkODI3YWFhYjM2ZiIsImMiOjE3MTcyNTI1NzA4ODQsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxLCJzcCI6MH0=; visited_city=bangalore; p_utm_tags=%7B%22utm_source%22%3A%22consumer%20home%22%2C%22utm_medium%22%3A%22dweb%22%7D; sessionId=ac77slj4jvtf5mpe7i2q47ag20; __cfruid=71af8edcb12ee14049ec1b04421feb1fc174ce41-1717252671; _clsk=tylzbx%7C1717252674761%7C9%7C1%7Cz.clarity.ms%2Fcollect; _ga_34LLDZZ06B=GS1.2.1717252680.1.0.1717252680.60.0.0; _cc_id=6bb810915a28029d28342db9ea72597c; panoramaId_expiry=1717339081786; _ga_3FL2X4WEE2=GS1.2.1717252724.1.0.1717252724.0.0.0; enableEC=true; isLoggedIn=false; _gat=1; __practo_sweep__id.ab61=79bb46f05ea748b6.1717252500.1.1717252756.1717252500.ff179507-a5a9-4681-9431-5e961fa97e8a; _uetsid=b1cd27f0202411efa28ead5ac5eb029e; _uetvid=b1cd5380202411ef9df345b7ecf60e53; _ga_CPQ4CDGC0P=GS1.2.1717252632.1.1.1717252784.40.0.0; ph_phc_wktKELppvd8LbWtB9gDh79wOrZbiMsmQge4SaSObYZs_posthog=%7B%22distinct_id%22%3A%22018fd43b-ace4-7318-b988-ff1648866bc0%22%2C%22%24sesid%22%3A%5B1717252787244%2C%22018fd43b-ae0d-7f44-992d-08b581567718%22%2C1717252632077%5D%7D; _ga_NNSR2MKTMP=GS1.2.1717252669.1.1.1717252788.0.0.0; _ga_08Y82JHL62=GS1.2.1717252499.1.1.1717252793.12.0.0; _ga_7RSD8FGHG5=GS1.2.1717252554.1.1.1717252799.60.0.0; _gat_fabric=1; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252802.51.0.0; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A77%2C%22s%22%3A**********%2C%22t%22%3A1717252804%7D; _gat_dchTracker=1; p_omni_data=%7B%22query_type%22%3A%22subspeciality%22%2C%22query_value%22%3A%22General%20Physician%22%2C%22query_value_id%22%3A60%2C%22location_type%22%3A%22city%22%2C%22location_value%22%3A%22Bangalore%22%2C%22location_value_id%22%3A1%7D",
                    "Referer": "https://www.practo.com/bangalore/general-physician?utm_source=consumer-home&utm_medium=dweb",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36"
                },
                "appTranaKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?ad_limit&city&disable_listing_expansion&enable_partner_listing&enable_sensodyne_campaign&is_procedure_cost_page&page&placement&platform&reach_version&show_new_reach_card&speciality&topaz&tracking_id&url_path&utm_medium&utm_source&with_ad&with_seo_data",
                "crawlerBookmark": {
                    "state": 1,
                    "action": 59,
                    "bookmarkId": "state=1|action=59",
                    "actionList": {
                        "serliaziedName": "./datastructure\\action-list.js",
                        "args": ["bookmark", [{
                                    "serliaziedName": "./datastructure\\action-list.js",
                                    "args": ["replay-need-to-add-context-id", [{
                                                "serliaziedName": "./datastructure\\action-list.js",
                                                "args": ["initial actions", [{
                                                            "serliaziedName": "./datastructure\\load-action.js",
                                                            "args": ["[initial]", "https://www.practo.com/", "initial, mustRunAtInit: true"]
                                                        }
                                                    ], "seq", true]
                                            }
                                        ], "seq", false]
                                }, {
                                    "serliaziedName": "./datastructure\\load-action.js",
                                    "args": ["//*[@id=\"root\"]/div/div/div[1]/div[1]/div[1]/div[3]/div/div[1]/div[5]/a", "https://www.practo.com/consult?product=nav&attribution=side_drawer", ""]
                                }
                            ], "seq", false]
                    },
                    "rootActionCount": 1
                },
                "resourceType": "xhr",
                "crawlerKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?ad_limit&city&disable_listing_expansion&enable_partner_listing&enable_sensodyne_campaign&is_procedure_cost_page&page&placement&platform&reach_version&show_new_reach_card&speciality&topaz&tracking_id&url_path&utm_medium&utm_source&with_ad&with_seo_data",
                "uri": "https://www.practo.com/marketplace-api/dweb/search/provider-seo/v2/?utm_medium=dweb&utm_source=consumer-home&url_path=%2Fbangalore%2Fgeneral-physician&page=2&reach_version=v4&ad_limit=2&platform=desktop_web&topaz=true&with_seo_data=true&city=bangalore&enable_partner_listing=true&speciality=general-physician&placement=DOCTOR_SEARCH&is_procedure_cost_page=false&show_new_reach_card=true&tracking_id=68ffc8ed-43be-4d41-bba0-9fa105979381&with_ad=true&disable_listing_expansion=false&enable_sensodyne_campaign=true",
                "haikuKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?utm_medium&utm_source&url_path&page&reach_version&ad_limit&platform&topaz&with_seo_data&city&enable_partner_listing&speciality&placement&is_procedure_cost_page&show_new_reach_card&tracking_id&with_ad&disable_listing_expansion&enable_sensodyne_campaign",
                "haikuResourceType": "core",
                "scanlog_id": 325,
                "scanId": 325,
                "attackRequestId": 1125
            },
            "requestId": 148,
            "haikuPriority": {
                "priority": 100,
                "requestParams": ["utm_medium", "utm_source", "url_path", "page", "reach_version", "ad_limit", "platform", "topaz", "with_seo_data", "city", "enable_partner_listing", "speciality", "placement", "is_procedure_cost_page", "show_new_reach_card", "tracking_id", "with_ad", "disable_listing_expansion", "enable_sensodyne_campaign"],
                "nonEmptyValueCount": 19,
                "pathComponentsCount": 5
            },
            "inProgessRequest": {},
            "pluginData": {
                "Remote File Inclusion (RFI)": {},
                "Cross Origin Resource Sharing": {},
                "Email Address Found": {},
                "Possible Sensitive Directory or file": {},
                "Authentication details found": {},
                "HTTP Header Info Disclosure": {},
                "Web Server details found": {
                    "serverdtsfound": true,
                    "Appdtsfound": true
                },
                "Password-submitted-without-ssl": {},
                "Insecure HTTP Transport": {
                    "insecureCount": 72738
                },
                "Insecure HTTP Method Transition": {
                    "InsecureHTTPMethod": {
                        "OriResBodylength": 72738
                    }
                },
                "Application-Error-Message": {},
                "Cookie Manipulation": {
                    "originalChecksum": "09562671c22d17dd698c3e71f70a8d55"
                },
                "Weak Session ID": {},
                "Application Detection": {},
                "Authentication Bypass": {},
                "Apache ETag header Found vulnerability": {},
                "Reveals Sensitive Information": {}
            }
        },
        "href": "https://www.practo.com/marketplace-api/dweb/search/provider-seo/v2/?utm_medium=dweb&utm_source=consumer-home&url_path=%2Fbangalore%2Fgeneral-physician&page=2&reach_version=v4&ad_limit=2&platform=desktop_web&topaz=true&with_seo_data=true&city=bangalore&enable_partner_listing=true&speciality=general-physician&placement=DOCTOR_SEARCH&is_procedure_cost_page=false&show_new_reach_card=true&tracking_id=68ffc8ed-43be-4d41-bba0-9fa105979381&with_ad=true&disable_listing_expansion=false&enable_sensodyne_campaign=true",
        "area": "original-crawler-request",
        "headers": {
            "accept": "application/json",
            "accept-language": "en-US",
            "cookie": "_fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _gid=GA1.2.315403102.**********; _ga=GA1.2.527041583.**********; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; p_attr_info=%7B%22session_referrer%22%3A%22Direct%22%2C%22landing_page%22%3A%22Listing%22%7D; hl=en; __practo_sweep__ses.ab61=*; _clck=o68dwj%7C2%7Cfm9%7C0%7C1613; _ga_VFVCCQSS9F=GS1.2.**********.1.1.**********.20.0.0; user_session=0g242k8ldmi33of47797m862m1; consult-attribution={%22source%22:%22consumer-home%22%2C%22medium%22:%22web%22%2C%22campaign%22:%22top_symptoms%22}; _hjSessionUser_828794=eyJpZCI6IjA4NWU4MjFkLTM3MTgtNWU5NC1iNzJlLTdhODM2ODMzZjBmMyIsImNyZWF0ZWQiOjE3MTcyNTI1NzA4ODIsImV4aXN0aW5nIjp0cnVlfQ==; _hjSession_828794=eyJpZCI6IjIyYjgxYzNmLWIyZjctNDQ5MS04MTAzLTBkODI3YWFhYjM2ZiIsImMiOjE3MTcyNTI1NzA4ODQsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxLCJzcCI6MH0=; visited_city=bangalore; p_utm_tags=%7B%22utm_source%22%3A%22consumer%20home%22%2C%22utm_medium%22%3A%22dweb%22%7D; sessionId=ac77slj4jvtf5mpe7i2q47ag20; __cfruid=71af8edcb12ee14049ec1b04421feb1fc174ce41-1717252671; _clsk=tylzbx%7C1717252674761%7C9%7C1%7Cz.clarity.ms%2Fcollect; _ga_34LLDZZ06B=GS1.2.1717252680.1.0.1717252680.60.0.0; _cc_id=6bb810915a28029d28342db9ea72597c; panoramaId_expiry=1717339081786; _ga_3FL2X4WEE2=GS1.2.1717252724.1.0.1717252724.0.0.0; enableEC=true; isLoggedIn=false; _gat=1; __practo_sweep__id.ab61=79bb46f05ea748b6.1717252500.1.1717252756.1717252500.ff179507-a5a9-4681-9431-5e961fa97e8a; _uetsid=b1cd27f0202411efa28ead5ac5eb029e; _uetvid=b1cd5380202411ef9df345b7ecf60e53; _ga_CPQ4CDGC0P=GS1.2.1717252632.1.1.1717252784.40.0.0; ph_phc_wktKELppvd8LbWtB9gDh79wOrZbiMsmQge4SaSObYZs_posthog=%7B%22distinct_id%22%3A%22018fd43b-ace4-7318-b988-ff1648866bc0%22%2C%22%24sesid%22%3A%5B1717252787244%2C%22018fd43b-ae0d-7f44-992d-08b581567718%22%2C1717252632077%5D%7D; _ga_NNSR2MKTMP=GS1.2.1717252669.1.1.1717252788.0.0.0; _ga_08Y82JHL62=GS1.2.1717252499.1.1.1717252793.12.0.0; _ga_7RSD8FGHG5=GS1.2.1717252554.1.1.1717252799.60.0.0; _gat_fabric=1; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252802.51.0.0; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A77%2C%22s%22%3A**********%2C%22t%22%3A1717252804%7D; _gat_dchTracker=1; p_omni_data=%7B%22query_type%22%3A%22subspeciality%22%2C%22query_value%22%3A%22General%20Physician%22%2C%22query_value_id%22%3A60%2C%22location_type%22%3A%22city%22%2C%22location_value%22%3A%22Bangalore%22%2C%22location_value_id%22%3A1%7D; PHPSESSID=96fkcd63lbc35kjq09dp6llg37; availability_pla_client_id=225BEC25-57C0-AB3B-BBE5-157C3B44D0CB; __cfruid=4c35635a17f5f153ada111a700945330a7286f84-1717252814; pelUUID=9a4c9333-61ca-4408-a9a8-5f9c99cc748a",
            "referer": "https://www.practo.com/bangalore/general-physician?utm_source=consumer-home&utm_medium=dweb",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36",
            "host": "www.practo.com",
            "accept-encoding": "gzip, deflate"
        },
        "httpRequest": {
            "method": "GET",
            "headers": {
                "Accept": "application/json",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept-Language": "en-US",
                "Cookie": "_fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _gid=GA1.2.315403102.**********; _ga=GA1.2.527041583.**********; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; p_attr_info=%7B%22session_referrer%22%3A%22Direct%22%2C%22landing_page%22%3A%22Listing%22%7D; hl=en; __practo_sweep__ses.ab61=*; _clck=o68dwj%7C2%7Cfm9%7C0%7C1613; _ga_VFVCCQSS9F=GS1.2.**********.1.1.**********.20.0.0; user_session=0g242k8ldmi33of47797m862m1; consult-attribution={%22source%22:%22consumer-home%22%2C%22medium%22:%22web%22%2C%22campaign%22:%22top_symptoms%22}; _hjSessionUser_828794=eyJpZCI6IjA4NWU4MjFkLTM3MTgtNWU5NC1iNzJlLTdhODM2ODMzZjBmMyIsImNyZWF0ZWQiOjE3MTcyNTI1NzA4ODIsImV4aXN0aW5nIjp0cnVlfQ==; _hjSession_828794=eyJpZCI6IjIyYjgxYzNmLWIyZjctNDQ5MS04MTAzLTBkODI3YWFhYjM2ZiIsImMiOjE3MTcyNTI1NzA4ODQsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxLCJzcCI6MH0=; visited_city=bangalore; p_utm_tags=%7B%22utm_source%22%3A%22consumer%20home%22%2C%22utm_medium%22%3A%22dweb%22%7D; sessionId=ac77slj4jvtf5mpe7i2q47ag20; __cfruid=71af8edcb12ee14049ec1b04421feb1fc174ce41-1717252671; _clsk=tylzbx%7C1717252674761%7C9%7C1%7Cz.clarity.ms%2Fcollect; _ga_34LLDZZ06B=GS1.2.1717252680.1.0.1717252680.60.0.0; _cc_id=6bb810915a28029d28342db9ea72597c; panoramaId_expiry=1717339081786; _ga_3FL2X4WEE2=GS1.2.1717252724.1.0.1717252724.0.0.0; enableEC=true; isLoggedIn=false; _gat=1; __practo_sweep__id.ab61=79bb46f05ea748b6.1717252500.1.1717252756.1717252500.ff179507-a5a9-4681-9431-5e961fa97e8a; _uetsid=b1cd27f0202411efa28ead5ac5eb029e; _uetvid=b1cd5380202411ef9df345b7ecf60e53; _ga_CPQ4CDGC0P=GS1.2.1717252632.1.1.1717252784.40.0.0; ph_phc_wktKELppvd8LbWtB9gDh79wOrZbiMsmQge4SaSObYZs_posthog=%7B%22distinct_id%22%3A%22018fd43b-ace4-7318-b988-ff1648866bc0%22%2C%22%24sesid%22%3A%5B1717252787244%2C%22018fd43b-ae0d-7f44-992d-08b581567718%22%2C1717252632077%5D%7D; _ga_NNSR2MKTMP=GS1.2.1717252669.1.1.1717252788.0.0.0; _ga_08Y82JHL62=GS1.2.1717252499.1.1.1717252793.12.0.0; _ga_7RSD8FGHG5=GS1.2.1717252554.1.1.1717252799.60.0.0; _gat_fabric=1; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252802.51.0.0; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A77%2C%22s%22%3A**********%2C%22t%22%3A1717252804%7D; _gat_dchTracker=1; p_omni_data=%7B%22query_type%22%3A%22subspeciality%22%2C%22query_value%22%3A%22General%20Physician%22%2C%22query_value_id%22%3A60%2C%22location_type%22%3A%22city%22%2C%22location_value%22%3A%22Bangalore%22%2C%22location_value_id%22%3A1%7D",
                "Referer": "https://www.practo.com/bangalore/general-physician?utm_source=consumer-home&utm_medium=dweb",
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "same-origin",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36"
            },
            "appTranaKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?ad_limit&city&disable_listing_expansion&enable_partner_listing&enable_sensodyne_campaign&is_procedure_cost_page&page&placement&platform&reach_version&show_new_reach_card&speciality&topaz&tracking_id&url_path&utm_medium&utm_source&with_ad&with_seo_data",
            "crawlerBookmark": {
                "state": 1,
                "action": 59,
                "bookmarkId": "state=1|action=59",
                "actionList": {
                    "serliaziedName": "./datastructure\\action-list.js",
                    "args": ["bookmark", [{
                                "serliaziedName": "./datastructure\\action-list.js",
                                "args": ["replay-need-to-add-context-id", [{
                                            "serliaziedName": "./datastructure\\action-list.js",
                                            "args": ["initial actions", [{
                                                        "serliaziedName": "./datastructure\\load-action.js",
                                                        "args": ["[initial]", "https://www.practo.com/", "initial, mustRunAtInit: true"]
                                                    }
                                                ], "seq", true]
                                        }
                                    ], "seq", false]
                            }, {
                                "serliaziedName": "./datastructure\\load-action.js",
                                "args": ["//*[@id=\"root\"]/div/div/div[1]/div[1]/div[1]/div[3]/div/div[1]/div[5]/a", "https://www.practo.com/consult?product=nav&attribution=side_drawer", ""]
                            }
                        ], "seq", false]
                },
                "rootActionCount": 1
            },
            "resourceType": "xhr",
            "crawlerKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?ad_limit&city&disable_listing_expansion&enable_partner_listing&enable_sensodyne_campaign&is_procedure_cost_page&page&placement&platform&reach_version&show_new_reach_card&speciality&topaz&tracking_id&url_path&utm_medium&utm_source&with_ad&with_seo_data",
            "uri": "https://www.practo.com/marketplace-api/dweb/search/provider-seo/v2/?utm_medium=dweb&utm_source=consumer-home&url_path=%2Fbangalore%2Fgeneral-physician&page=2&reach_version=v4&ad_limit=2&platform=desktop_web&topaz=true&with_seo_data=true&city=bangalore&enable_partner_listing=true&speciality=general-physician&placement=DOCTOR_SEARCH&is_procedure_cost_page=false&show_new_reach_card=true&tracking_id=68ffc8ed-43be-4d41-bba0-9fa105979381&with_ad=true&disable_listing_expansion=false&enable_sensodyne_campaign=true",
            "haikuKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?utm_medium&utm_source&url_path&page&reach_version&ad_limit&platform&topaz&with_seo_data&city&enable_partner_listing&speciality&placement&is_procedure_cost_page&show_new_reach_card&tracking_id&with_ad&disable_listing_expansion&enable_sensodyne_campaign",
            "haikuResourceType": "core",
            "scanlog_id": 325,
            "scanId": 325,
            "attackRequestId": 1125
        },
        "httpResponse": {
            "headers": {
                "date": "Sat, 01 Jun 2024 14:40:15 GMT",
                "content-type": "application/json; charset=utf-8",
                "content-length": "16172",
                "connection": "close",
                "cid": "03ea0f3bb45c4f3a01b0d95e3bfe083e",
                "content-encoding": "gzip",
                "etag": "W/\"11c2e-BlnvpS17xpvGKrm3JBXngzZ5TP0\"",
                "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
                "vary": "Accept-Encoding",
                "x-powered-by": "Express",
                "cf-cache-status": "DYNAMIC",
                "server": "cloudflare",
                "cf-ray": "88cff52f5903837b-BOM",
                "alt-svc": "h3=\":443\"; ma=86400"
            },
            "statusCode": 200,
            "statusMessage": "OK",
            "httpVersion": "1.1",
            "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"attack-11.body\"}"
        }
    },
    "response": {
        "headers": {
            "date": "Sat, 01 Jun 2024 14:40:15 GMT",
            "content-type": "application/json; charset=utf-8",
            "content-length": "16172",
            "connection": "close",
            "cid": "03ea0f3bb45c4f3a01b0d95e3bfe083e",
            "content-encoding": "gzip",
            "etag": "W/\"11c2e-BlnvpS17xpvGKrm3JBXngzZ5TP0\"",
            "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
            "vary": "Accept-Encoding",
            "x-powered-by": "Express",
            "cf-cache-status": "DYNAMIC",
            "server": "cloudflare",
            "cf-ray": "88cff52f5903837b-BOM",
            "alt-svc": "h3=\":443\"; ma=86400"
        },
        "statusCode": 200,
        "statusMessage": "OK",
        "httpVersion": "1.1",
        "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"attack-11.body\"}"
    },
    "vulns": {
        "ID-Web-Server-Version-Disclosure": {
            "foundBy": "Web Server details found",
            "productionReady": true,
            "canAttackInReplayScan": false,
            "details": {
                "result": "Server: cloudflare"
            },
            "autoPOC": []
        },
        "ID-info-disclosure-http-headers": {
            "foundBy": "Web Server details found",
            "productionReady": true,
            "canAttackInReplayScan": false,
            "details": [{
                    "result": "x-powered-by: Express"
                }
            ],
            "autoPOC": []
        }
    }
} {
    "scanId": 325,
    "scanlogId": 325,
    "attack": {
        "attackCount": 1,
        "method": "GET",
        "name": "Local File Inclusion (LFI)",
        "hostname": "www.practo.com",
        "originalRequest": {
            "scanId": 325,
            "scanlog_id": 325,
            "scanner": "haiku",
            "httpRequest": {
                "method": "GET",
                "headers": {
                    "Accept": "application/json",
                    "Accept-Encoding": "gzip, deflate, br",
                    "Accept-Language": "en-US",
                    "Cookie": "_fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _gid=GA1.2.315403102.**********; _ga=GA1.2.527041583.**********; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; p_attr_info=%7B%22session_referrer%22%3A%22Direct%22%2C%22landing_page%22%3A%22Listing%22%7D; hl=en; __practo_sweep__ses.ab61=*; _clck=o68dwj%7C2%7Cfm9%7C0%7C1613; _ga_VFVCCQSS9F=GS1.2.**********.1.1.**********.20.0.0; user_session=0g242k8ldmi33of47797m862m1; consult-attribution={%22source%22:%22consumer-home%22%2C%22medium%22:%22web%22%2C%22campaign%22:%22top_symptoms%22}; _hjSessionUser_828794=eyJpZCI6IjA4NWU4MjFkLTM3MTgtNWU5NC1iNzJlLTdhODM2ODMzZjBmMyIsImNyZWF0ZWQiOjE3MTcyNTI1NzA4ODIsImV4aXN0aW5nIjp0cnVlfQ==; _hjSession_828794=eyJpZCI6IjIyYjgxYzNmLWIyZjctNDQ5MS04MTAzLTBkODI3YWFhYjM2ZiIsImMiOjE3MTcyNTI1NzA4ODQsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxLCJzcCI6MH0=; visited_city=bangalore; p_utm_tags=%7B%22utm_source%22%3A%22consumer%20home%22%2C%22utm_medium%22%3A%22dweb%22%7D; sessionId=ac77slj4jvtf5mpe7i2q47ag20; __cfruid=71af8edcb12ee14049ec1b04421feb1fc174ce41-1717252671; _clsk=tylzbx%7C1717252674761%7C9%7C1%7Cz.clarity.ms%2Fcollect; _ga_34LLDZZ06B=GS1.2.1717252680.1.0.1717252680.60.0.0; _cc_id=6bb810915a28029d28342db9ea72597c; panoramaId_expiry=1717339081786; _ga_3FL2X4WEE2=GS1.2.1717252724.1.0.1717252724.0.0.0; enableEC=true; isLoggedIn=false; _gat=1; __practo_sweep__id.ab61=79bb46f05ea748b6.1717252500.1.1717252756.1717252500.ff179507-a5a9-4681-9431-5e961fa97e8a; _uetsid=b1cd27f0202411efa28ead5ac5eb029e; _uetvid=b1cd5380202411ef9df345b7ecf60e53; _ga_CPQ4CDGC0P=GS1.2.1717252632.1.1.1717252784.40.0.0; ph_phc_wktKELppvd8LbWtB9gDh79wOrZbiMsmQge4SaSObYZs_posthog=%7B%22distinct_id%22%3A%22018fd43b-ace4-7318-b988-ff1648866bc0%22%2C%22%24sesid%22%3A%5B1717252787244%2C%22018fd43b-ae0d-7f44-992d-08b581567718%22%2C1717252632077%5D%7D; _ga_NNSR2MKTMP=GS1.2.1717252669.1.1.1717252788.0.0.0; _ga_08Y82JHL62=GS1.2.1717252499.1.1.1717252793.12.0.0; _ga_7RSD8FGHG5=GS1.2.1717252554.1.1.1717252799.60.0.0; _gat_fabric=1; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252802.51.0.0; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A77%2C%22s%22%3A**********%2C%22t%22%3A1717252804%7D; _gat_dchTracker=1; p_omni_data=%7B%22query_type%22%3A%22subspeciality%22%2C%22query_value%22%3A%22General%20Physician%22%2C%22query_value_id%22%3A60%2C%22location_type%22%3A%22city%22%2C%22location_value%22%3A%22Bangalore%22%2C%22location_value_id%22%3A1%7D",
                    "Referer": "https://www.practo.com/bangalore/general-physician?utm_source=consumer-home&utm_medium=dweb",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36"
                },
                "appTranaKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?ad_limit&city&disable_listing_expansion&enable_partner_listing&enable_sensodyne_campaign&is_procedure_cost_page&page&placement&platform&reach_version&show_new_reach_card&speciality&topaz&tracking_id&url_path&utm_medium&utm_source&with_ad&with_seo_data",
                "crawlerBookmark": {
                    "state": 1,
                    "action": 59,
                    "bookmarkId": "state=1|action=59",
                    "actionList": {
                        "serliaziedName": "./datastructure\\action-list.js",
                        "args": ["bookmark", [{
                                    "serliaziedName": "./datastructure\\action-list.js",
                                    "args": ["replay-need-to-add-context-id", [{
                                                "serliaziedName": "./datastructure\\action-list.js",
                                                "args": ["initial actions", [{
                                                            "serliaziedName": "./datastructure\\load-action.js",
                                                            "args": ["[initial]", "https://www.practo.com/", "initial, mustRunAtInit: true"]
                                                        }
                                                    ], "seq", true]
                                            }
                                        ], "seq", false]
                                }, {
                                    "serliaziedName": "./datastructure\\load-action.js",
                                    "args": ["//*[@id=\"root\"]/div/div/div[1]/div[1]/div[1]/div[3]/div/div[1]/div[5]/a", "https://www.practo.com/consult?product=nav&attribution=side_drawer", ""]
                                }
                            ], "seq", false]
                    },
                    "rootActionCount": 1
                },
                "resourceType": "xhr",
                "crawlerKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?ad_limit&city&disable_listing_expansion&enable_partner_listing&enable_sensodyne_campaign&is_procedure_cost_page&page&placement&platform&reach_version&show_new_reach_card&speciality&topaz&tracking_id&url_path&utm_medium&utm_source&with_ad&with_seo_data",
                "uri": "https://www.practo.com/marketplace-api/dweb/search/provider-seo/v2/?utm_medium=dweb&utm_source=consumer-home&url_path=%2Fbangalore%2Fgeneral-physician&page=2&reach_version=v4&ad_limit=2&platform=desktop_web&topaz=true&with_seo_data=true&city=bangalore&enable_partner_listing=true&speciality=general-physician&placement=DOCTOR_SEARCH&is_procedure_cost_page=false&show_new_reach_card=true&tracking_id=68ffc8ed-43be-4d41-bba0-9fa105979381&with_ad=true&disable_listing_expansion=false&enable_sensodyne_campaign=true",
                "haikuKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?utm_medium&utm_source&url_path&page&reach_version&ad_limit&platform&topaz&with_seo_data&city&enable_partner_listing&speciality&placement&is_procedure_cost_page&show_new_reach_card&tracking_id&with_ad&disable_listing_expansion&enable_sensodyne_campaign",
                "haikuResourceType": "core",
                "scanlog_id": 325,
                "scanId": 325,
                "attackRequestId": 1125
            },
            "requestId": 148,
            "haikuPriority": {
                "priority": 100,
                "requestParams": ["utm_medium", "utm_source", "url_path", "page", "reach_version", "ad_limit", "platform", "topaz", "with_seo_data", "city", "enable_partner_listing", "speciality", "placement", "is_procedure_cost_page", "show_new_reach_card", "tracking_id", "with_ad", "disable_listing_expansion", "enable_sensodyne_campaign"],
                "nonEmptyValueCount": 19,
                "pathComponentsCount": 5
            },
            "pluginData": {
                "Remote File Inclusion (RFI)": {},
                "Cross Origin Resource Sharing": {},
                "Email Address Found": {},
                "Possible Sensitive Directory or file": {},
                "Authentication details found": {},
                "HTTP Header Info Disclosure": {},
                "Web Server details found": {
                    "serverdtsfound": true,
                    "Appdtsfound": true
                },
                "Password-submitted-without-ssl": {},
                "Insecure HTTP Transport": {
                    "insecureCount": 72738
                },
                "Insecure HTTP Method Transition": {
                    "InsecureHTTPMethod": {
                        "OriResBodylength": 72738
                    }
                },
                "Application-Error-Message": {},
                "Cookie Manipulation": {
                    "originalChecksum": "09562671c22d17dd698c3e71f70a8d55"
                },
                "Weak Session ID": {},
                "Application Detection": {},
                "Authentication Bypass": {},
                "Apache ETag header Found vulnerability": {},
                "Reveals Sensitive Information": {},
                "SQL Injection": {},
                "Server Side Javascript Injection": {},
                "Server Side Template Injection": {},
                "Local File Inclusion (LFI)": {},
                "Remote XSL Inclusion (RXSLI)": {},
                "Cross Site Scripting (XSS)": {},
                "XPATH Injection": {},
                "OS Command Injection": {},
                "PHP Nginx Command Injection": {},
                "Cross-Site Tracing (XST)": {},
                "Cross-Site Tracing (XST) OOB": {},
                "Http Response Splitting": {},
                "Manually Reported Vuln": {},
                "User-Controllable-Tag-Parameter": {},
                "Unencoded character check": {},
                "SSRF": {},
                "Uncontrolled Format String": {},
                "Iframe Injection": {},
                "Link Injection": {},
                "Breach attack": {},
                "ESI Injection vulnerability": {},
                "Apache log4j vulnerability": {},
                "OS Command Inj OOB": {},
                "SQL Injection OOB": {},
                "Cross Site Scripting (XSS) OOB": {},
                "SSRF OOB": {},
                "Server-Side Template Injection OOB": {},
                "Client-Side Template Injection OOB": {},
                "no captcha on login page": {
                    "": {}
                },
                "Code Injection OOB": {},
                "Cross-Site Flashing (XSF) OOB": {},
                "Link Injection OOB": {},
                "Iframe Injection OOB": {},
                "Text Injection": {},
                "WebSocket URL poisoning": {},
                "Form Action Hijacking": {},
                "Code Injection Attack": {},
                "Expression Language Injection": {},
                "Apache Struts": {},
                "Host Header Injection": {},
                "Web cache poisoning attack": {},
                "Exchange Server SSRF attack": {},
                "Host Header Injection OOB": {},
                "Spring Expression Resource Access RCE": {},
                "DEBUG method enabled": {},
                "PROPFIND method enabled": {},
                "TRACE TRACK method enabled": {},
                "Options enabled": {},
                "HTTP Verb Tampering": {},
                "DotNet Deserialization Found": {},
                "Apache httpd DOS": {},
                "vBulletin-RCE": {},
                "ASP NET Trace method enabled": {},
                "Webadmin": {
                    "php file found": {}
                },
                "Robot Txt File Found": {},
                "Sensitive Documentation Txt File Found": {},
                "CVS Web Repository": {},
                "Apache Server-status Page": {},
                "Possible Backup files": {},
                "Remote Administration Interface Found": {},
                "Predictable Resource Location": {},
                "Browsable Web Directory": {},
                "Apache mod-cgi LFI": {},
                "PRSSI vuln": {},
                "EPMM Authentication Bypass": {},
                "Jenkins Args4j Attack": {},
                "Cookie Checker": {
                    "httpOnlyVulnerabilityFound": true,
                    "secureVulnerabilityFound": true,
                    "sessionCookieScopeVulnerabilityFound": true,
                    "cookiePathOverlyBroadVulnerabilityFound": true
                },
                "Insecure-Flash-Embed-Param-Found": {},
                "Credential Guessing": {},
                "VMware Server-side Template Injection (RCE) Vulnerability": {},
                "Apache OFBiz Authentication Bypass Vulnerability (CVE-2023-51467)": {},
                "ExtJs Arbitrary File Read": {},
                "Clickjacking - HTTP Header Check": {},
                "Hidden-Form-Input-Field": {},
                "JSF ViewState Found": {}
            },
            "httpResponse": {
                "err": null,
                "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"original-12.body\"}",
                "headers": {
                    "date": "Sat, 01 Jun 2024 14:40:15 GMT",
                    "content-type": "application/json; charset=utf-8",
                    "content-length": "16172",
                    "connection": "close",
                    "cid": "03ea0f3bb45c4f3a01b0d95e3bfe083e",
                    "content-encoding": "gzip",
                    "etag": "W/\"11c2e-BlnvpS17xpvGKrm3JBXngzZ5TP0\"",
                    "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
                    "vary": "Accept-Encoding",
                    "x-powered-by": "Express",
                    "cf-cache-status": "DYNAMIC",
                    "server": "cloudflare",
                    "cf-ray": "88cff52f5903837b-BOM",
                    "alt-svc": "h3=\":443\"; ma=86400"
                },
                "statusCode": 200,
                "statusMessage": "OK",
                "redirectsFollowed": 0,
                "redirects": []
            }
        },
        "href": "https://www.practo.com/boot.ini/?utm_medium=dweb&utm_source=consumer-home&url_path=%2Fbangalore%2Fgeneral-physician&page=2&reach_version=v4&ad_limit=2&platform=desktop_web&topaz=true&with_seo_data=true&city=bangalore&enable_partner_listing=true&speciality=general-physician&placement=DOCTOR_SEARCH&is_procedure_cost_page=false&show_new_reach_card=true&tracking_id=68ffc8ed-43be-4d41-bba0-9fa105979381&with_ad=true&disable_listing_expansion=false&enable_sensodyne_campaign=true",
        "area": "BaseURI",
        "type": "value",
        "param": "/marketplace-api/dweb/search/provider-seo/v2",
        "vector": "../../../../../../../../../../boot.ini",
        "encoding": "uri",
        "headers": {
            "accept": "application/json",
            "accept-language": "en-US",
            "cookie": "_fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _gid=GA1.2.315403102.**********; _ga=GA1.2.527041583.**********; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; p_attr_info=%7B%22session_referrer%22%3A%22Direct%22%2C%22landing_page%22%3A%22Listing%22%7D; hl=en; __practo_sweep__ses.ab61=*; _clck=o68dwj%7C2%7Cfm9%7C0%7C1613; _ga_VFVCCQSS9F=GS1.2.**********.1.1.**********.20.0.0; user_session=0g242k8ldmi33of47797m862m1; consult-attribution={%22source%22:%22consumer-home%22%2C%22medium%22:%22web%22%2C%22campaign%22:%22top_symptoms%22}; _hjSessionUser_828794=eyJpZCI6IjA4NWU4MjFkLTM3MTgtNWU5NC1iNzJlLTdhODM2ODMzZjBmMyIsImNyZWF0ZWQiOjE3MTcyNTI1NzA4ODIsImV4aXN0aW5nIjp0cnVlfQ==; _hjSession_828794=eyJpZCI6IjIyYjgxYzNmLWIyZjctNDQ5MS04MTAzLTBkODI3YWFhYjM2ZiIsImMiOjE3MTcyNTI1NzA4ODQsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxLCJzcCI6MH0=; visited_city=bangalore; p_utm_tags=%7B%22utm_source%22%3A%22consumer%20home%22%2C%22utm_medium%22%3A%22dweb%22%7D; sessionId=ac77slj4jvtf5mpe7i2q47ag20; __cfruid=71af8edcb12ee14049ec1b04421feb1fc174ce41-1717252671; _clsk=tylzbx%7C1717252674761%7C9%7C1%7Cz.clarity.ms%2Fcollect; _ga_34LLDZZ06B=GS1.2.1717252680.1.0.1717252680.60.0.0; _cc_id=6bb810915a28029d28342db9ea72597c; panoramaId_expiry=1717339081786; _ga_3FL2X4WEE2=GS1.2.1717252724.1.0.1717252724.0.0.0; enableEC=true; isLoggedIn=false; _gat=1; __practo_sweep__id.ab61=79bb46f05ea748b6.1717252500.1.1717252756.1717252500.ff179507-a5a9-4681-9431-5e961fa97e8a; _uetsid=b1cd27f0202411efa28ead5ac5eb029e; _uetvid=b1cd5380202411ef9df345b7ecf60e53; _ga_CPQ4CDGC0P=GS1.2.1717252632.1.1.1717252784.40.0.0; ph_phc_wktKELppvd8LbWtB9gDh79wOrZbiMsmQge4SaSObYZs_posthog=%7B%22distinct_id%22%3A%22018fd43b-ace4-7318-b988-ff1648866bc0%22%2C%22%24sesid%22%3A%5B1717252787244%2C%22018fd43b-ae0d-7f44-992d-08b581567718%22%2C1717252632077%5D%7D; _ga_NNSR2MKTMP=GS1.2.1717252669.1.1.1717252788.0.0.0; _ga_08Y82JHL62=GS1.2.1717252499.1.1.1717252793.12.0.0; _ga_7RSD8FGHG5=GS1.2.1717252554.1.1.1717252799.60.0.0; _gat_fabric=1; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252802.51.0.0; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A77%2C%22s%22%3A**********%2C%22t%22%3A1717252804%7D; _gat_dchTracker=1; p_omni_data=%7B%22query_type%22%3A%22subspeciality%22%2C%22query_value%22%3A%22General%20Physician%22%2C%22query_value_id%22%3A60%2C%22location_type%22%3A%22city%22%2C%22location_value%22%3A%22Bangalore%22%2C%22location_value_id%22%3A1%7D; PHPSESSID=96fkcd63lbc35kjq09dp6llg37; availability_pla_client_id=225BEC25-57C0-AB3B-BBE5-157C3B44D0CB; __cfruid=f6cb4a73cc42292000e130042ae47e81f35e9b2f-1717253570; pelUUID=a2e984ec-6141-4008-aae1-e818356de440",
            "referer": "https://www.practo.com/bangalore/general-physician?utm_source=consumer-home&utm_medium=dweb",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36",
            "accept-encoding": "gzip, deflate",
            "host": "www.practo.com"
        },
        "httpRequest": {
            "method": "GET",
            "headers": {
                "Accept": "application/json",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept-Language": "en-US",
                "Cookie": "_fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _gid=GA1.2.315403102.**********; _ga=GA1.2.527041583.**********; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; p_attr_info=%7B%22session_referrer%22%3A%22Direct%22%2C%22landing_page%22%3A%22Listing%22%7D; hl=en; __practo_sweep__ses.ab61=*; _clck=o68dwj%7C2%7Cfm9%7C0%7C1613; _ga_VFVCCQSS9F=GS1.2.**********.1.1.**********.20.0.0; user_session=0g242k8ldmi33of47797m862m1; consult-attribution={%22source%22:%22consumer-home%22%2C%22medium%22:%22web%22%2C%22campaign%22:%22top_symptoms%22}; _hjSessionUser_828794=eyJpZCI6IjA4NWU4MjFkLTM3MTgtNWU5NC1iNzJlLTdhODM2ODMzZjBmMyIsImNyZWF0ZWQiOjE3MTcyNTI1NzA4ODIsImV4aXN0aW5nIjp0cnVlfQ==; _hjSession_828794=eyJpZCI6IjIyYjgxYzNmLWIyZjctNDQ5MS04MTAzLTBkODI3YWFhYjM2ZiIsImMiOjE3MTcyNTI1NzA4ODQsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxLCJzcCI6MH0=; visited_city=bangalore; p_utm_tags=%7B%22utm_source%22%3A%22consumer%20home%22%2C%22utm_medium%22%3A%22dweb%22%7D; sessionId=ac77slj4jvtf5mpe7i2q47ag20; __cfruid=71af8edcb12ee14049ec1b04421feb1fc174ce41-1717252671; _clsk=tylzbx%7C1717252674761%7C9%7C1%7Cz.clarity.ms%2Fcollect; _ga_34LLDZZ06B=GS1.2.1717252680.1.0.1717252680.60.0.0; _cc_id=6bb810915a28029d28342db9ea72597c; panoramaId_expiry=1717339081786; _ga_3FL2X4WEE2=GS1.2.1717252724.1.0.1717252724.0.0.0; enableEC=true; isLoggedIn=false; _gat=1; __practo_sweep__id.ab61=79bb46f05ea748b6.1717252500.1.1717252756.1717252500.ff179507-a5a9-4681-9431-5e961fa97e8a; _uetsid=b1cd27f0202411efa28ead5ac5eb029e; _uetvid=b1cd5380202411ef9df345b7ecf60e53; _ga_CPQ4CDGC0P=GS1.2.1717252632.1.1.1717252784.40.0.0; ph_phc_wktKELppvd8LbWtB9gDh79wOrZbiMsmQge4SaSObYZs_posthog=%7B%22distinct_id%22%3A%22018fd43b-ace4-7318-b988-ff1648866bc0%22%2C%22%24sesid%22%3A%5B1717252787244%2C%22018fd43b-ae0d-7f44-992d-08b581567718%22%2C1717252632077%5D%7D; _ga_NNSR2MKTMP=GS1.2.1717252669.1.1.1717252788.0.0.0; _ga_08Y82JHL62=GS1.2.1717252499.1.1.1717252793.12.0.0; _ga_7RSD8FGHG5=GS1.2.1717252554.1.1.1717252799.60.0.0; _gat_fabric=1; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252802.51.0.0; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A77%2C%22s%22%3A**********%2C%22t%22%3A1717252804%7D; _gat_dchTracker=1; p_omni_data=%7B%22query_type%22%3A%22subspeciality%22%2C%22query_value%22%3A%22General%20Physician%22%2C%22query_value_id%22%3A60%2C%22location_type%22%3A%22city%22%2C%22location_value%22%3A%22Bangalore%22%2C%22location_value_id%22%3A1%7D",
                "Referer": "https://www.practo.com/bangalore/general-physician?utm_source=consumer-home&utm_medium=dweb",
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "same-origin",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36"
            },
            "appTranaKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?ad_limit&city&disable_listing_expansion&enable_partner_listing&enable_sensodyne_campaign&is_procedure_cost_page&page&placement&platform&reach_version&show_new_reach_card&speciality&topaz&tracking_id&url_path&utm_medium&utm_source&with_ad&with_seo_data",
            "crawlerBookmark": {
                "state": 1,
                "action": 59,
                "bookmarkId": "state=1|action=59",
                "actionList": {
                    "serliaziedName": "./datastructure\\action-list.js",
                    "args": ["bookmark", [{
                                "serliaziedName": "./datastructure\\action-list.js",
                                "args": ["replay-need-to-add-context-id", [{
                                            "serliaziedName": "./datastructure\\action-list.js",
                                            "args": ["initial actions", [{
                                                        "serliaziedName": "./datastructure\\load-action.js",
                                                        "args": ["[initial]", "https://www.practo.com/", "initial, mustRunAtInit: true"]
                                                    }
                                                ], "seq", true]
                                        }
                                    ], "seq", false]
                            }, {
                                "serliaziedName": "./datastructure\\load-action.js",
                                "args": ["//*[@id=\"root\"]/div/div/div[1]/div[1]/div[1]/div[3]/div/div[1]/div[5]/a", "https://www.practo.com/consult?product=nav&attribution=side_drawer", ""]
                            }
                        ], "seq", false]
                },
                "rootActionCount": 1
            },
            "resourceType": "xhr",
            "crawlerKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?ad_limit&city&disable_listing_expansion&enable_partner_listing&enable_sensodyne_campaign&is_procedure_cost_page&page&placement&platform&reach_version&show_new_reach_card&speciality&topaz&tracking_id&url_path&utm_medium&utm_source&with_ad&with_seo_data",
            "uri": "https://www.practo.com/boot.ini/?utm_medium=dweb&utm_source=consumer-home&url_path=%2Fbangalore%2Fgeneral-physician&page=2&reach_version=v4&ad_limit=2&platform=desktop_web&topaz=true&with_seo_data=true&city=bangalore&enable_partner_listing=true&speciality=general-physician&placement=DOCTOR_SEARCH&is_procedure_cost_page=false&show_new_reach_card=true&tracking_id=68ffc8ed-43be-4d41-bba0-9fa105979381&with_ad=true&disable_listing_expansion=false&enable_sensodyne_campaign=true",
            "haikuKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?utm_medium&utm_source&url_path&page&reach_version&ad_limit&platform&topaz&with_seo_data&city&enable_partner_listing&speciality&placement&is_procedure_cost_page&show_new_reach_card&tracking_id&with_ad&disable_listing_expansion&enable_sensodyne_campaign",
            "haikuResourceType": "core",
            "scanlog_id": 325,
            "scanId": 325,
            "attackRequestId": 2904
        },
        "httpResponse": {
            "headers": {
                "date": "Sat, 01 Jun 2024 14:52:51 GMT",
                "content-type": "text/html; charset=UTF-8",
                "transfer-encoding": "chunked",
                "connection": "close",
                "cache-control": "private, must-revalidate",
                "cid": "cf06fb14ed0d2f210e469dcd9fc33c27",
                "content-encoding": "gzip",
                "expires": "-1",
                "pragma": "no-cache",
                "set-cookie": ["availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; expires=Wed, 18-Aug-2032 14:52:51 GMT; Max-Age=259200000; path=/", "modal_login=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/; domain=.practo.com"],
                "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
                "vary": "Accept-Encoding",
                "x-content-type-options": "nosniff",
                "x-xss-protection": "1; mode=block",
                "cf-cache-status": "DYNAMIC",
                "server": "cloudflare",
                "cf-ray": "88d007a21d9452af-CCU",
                "alt-svc": "h3=\":443\"; ma=86400"
            },
            "statusCode": 404,
            "statusMessage": "Not Found",
            "httpVersion": "1.1",
            "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"attack-13.body\"}"
        }
    },
    "response": {
        "headers": {
            "date": "Sat, 01 Jun 2024 14:52:51 GMT",
            "content-type": "text/html; charset=UTF-8",
            "transfer-encoding": "chunked",
            "connection": "close",
            "cache-control": "private, must-revalidate",
            "cid": "cf06fb14ed0d2f210e469dcd9fc33c27",
            "content-encoding": "gzip",
            "expires": "-1",
            "pragma": "no-cache",
            "set-cookie": ["availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; expires=Wed, 18-Aug-2032 14:52:51 GMT; Max-Age=259200000; path=/", "modal_login=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/; domain=.practo.com"],
            "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
            "vary": "Accept-Encoding",
            "x-content-type-options": "nosniff",
            "x-xss-protection": "1; mode=block",
            "cf-cache-status": "DYNAMIC",
            "server": "cloudflare",
            "cf-ray": "88d007a21d9452af-CCU",
            "alt-svc": "h3=\":443\"; ma=86400"
        },
        "statusCode": 404,
        "statusMessage": "Not Found",
        "httpVersion": "1.1",
        "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"attack-13.body\"}"
    },
    "vulns": {
        "ID-cookie-httponly-not-set": {
            "foundBy": "Cookie Checker",
            "productionReady": true,
            "canAttackInReplayScan": true,
            "details": {
                "name": "Cookie-HttpOnly-Not-Set",
                "details": [{
                        "name": "Set-Cookie",
                        "value": "730EE403-9259-5F72-FB54-A4D1F57035AF",
                        "httpOnlyMissing": true,
                        "fullCookie": "availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; expires=Wed, 18-Aug-2032 14:52:51 GMT; Max-Age=259200000; path=/"
                    }, {
                        "name": "Set-Cookie",
                        "value": "deleted",
                        "httpOnlyMissing": true,
                        "fullCookie": "modal_login=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/; domain=.practo.com"
                    }
                ]
            },
            "autoPOC": [{
                    "type": "original",
                    "path": "httpRequest.uri",
                    "highlightType": "text",
                    "details": ["/marketplace-api/dweb/search/provider-seo/v2"],
                    "description": "orignal request uri /marketplace-api/dweb/search/provider-seo/v2 param value will be attack"
                }, {
                    "type": "attack",
                    "path": "httpRequest.uri",
                    "highlightType": "text",
                    "details": ["/marketplace-api/dweb/search/provider-seo/v2/../../../../../../../../../../boot.ini"],
                    "description": "attack request uri /marketplace-api/dweb/search/provider-seo/v2 param value tampered"
                }
            ]
        },
        "ID-cookie-secure-not-set": {
            "foundBy": "Cookie Checker",
            "productionReady": true,
            "canAttackInReplayScan": true,
            "details": {
                "name": "Cookie-Secure-Not-Set",
                "details": [{
                        "name": "Set-Cookie",
                        "value": "730EE403-9259-5F72-FB54-A4D1F57035AF",
                        "secureMissing": true,
                        "fullCookie": "availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; expires=Wed, 18-Aug-2032 14:52:51 GMT; Max-Age=259200000; path=/"
                    }, {
                        "name": "Set-Cookie",
                        "value": "deleted",
                        "secureMissing": true,
                        "fullCookie": "modal_login=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/; domain=.practo.com"
                    }
                ]
            },
            "autoPOC": [{
                    "type": "original",
                    "path": "httpRequest.uri",
                    "highlightType": "text",
                    "details": ["/marketplace-api/dweb/search/provider-seo/v2"],
                    "description": "orignal request uri /marketplace-api/dweb/search/provider-seo/v2 param value will be attack"
                }, {
                    "type": "attack",
                    "path": "httpRequest.uri",
                    "highlightType": "text",
                    "details": ["/marketplace-api/dweb/search/provider-seo/v2/../../../../../../../../../../boot.ini"],
                    "description": "attack request uri /marketplace-api/dweb/search/provider-seo/v2 param value tampered"
                }
            ]
        },
        "ID-session-cookie-scoped-parent-domain": {
            "foundBy": "Cookie Checker",
            "productionReady": true,
            "canAttackInReplayScan": true,
            "details": {
                "name": "ID-session-cookie-scoped-parent-domain",
                "details": [{
                        "name": "Set-Cookie",
                        "value": ".practo.com",
                        "fullCookie": "modal_login=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/; domain=.practo.com"
                    }
                ]
            },
            "autoPOC": [{
                    "type": "original",
                    "path": "httpRequest.uri",
                    "highlightType": "text",
                    "details": ["/marketplace-api/dweb/search/provider-seo/v2"],
                    "description": "orignal request uri /marketplace-api/dweb/search/provider-seo/v2 param value will be attack"
                }, {
                    "type": "attack",
                    "path": "httpRequest.uri",
                    "highlightType": "text",
                    "details": ["/marketplace-api/dweb/search/provider-seo/v2/../../../../../../../../../../boot.ini"],
                    "description": "attack request uri /marketplace-api/dweb/search/provider-seo/v2 param value tampered"
                }
            ]
        },
        "ID-broad-cookie-path": {
            "foundBy": "Cookie Checker",
            "productionReady": true,
            "canAttackInReplayScan": true,
            "details": {
                "details": [{
                        "name": "Set-Cookie",
                        "value": "/",
                        "fullCookie": "availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; expires=Wed, 18-Aug-2032 14:52:51 GMT; Max-Age=259200000; path=/"
                    }, {
                        "name": "Set-Cookie",
                        "value": "/",
                        "fullCookie": "modal_login=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/; domain=.practo.com"
                    }
                ]
            },
            "autoPOC": [{
                    "type": "original",
                    "path": "httpRequest.uri",
                    "highlightType": "text",
                    "details": ["/marketplace-api/dweb/search/provider-seo/v2"],
                    "description": "orignal request uri /marketplace-api/dweb/search/provider-seo/v2 param value will be attack"
                }, {
                    "type": "attack",
                    "path": "httpRequest.uri",
                    "highlightType": "text",
                    "details": ["/marketplace-api/dweb/search/provider-seo/v2/../../../../../../../../../../boot.ini"],
                    "description": "attack request uri /marketplace-api/dweb/search/provider-seo/v2 param value tampered"
                }
            ]
        }
    }
} {
    "scanId": 325,
    "scanlogId": 325,
    "attack": {
        "attackCount": 1,
        "method": "GET",
        "name": "SSRF",
        "hostname": "www.practo.com",
        "originalRequest": {
            "scanId": 325,
            "scanlog_id": 325,
            "scanner": "haiku",
            "httpRequest": {
                "method": "GET",
                "headers": {
                    "Accept": "application/json",
                    "Accept-Encoding": "gzip, deflate, br",
                    "Accept-Language": "en-US",
                    "Cookie": "_fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _gid=GA1.2.315403102.**********; _ga=GA1.2.527041583.**********; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; p_attr_info=%7B%22session_referrer%22%3A%22Direct%22%2C%22landing_page%22%3A%22Listing%22%7D; hl=en; __practo_sweep__ses.ab61=*; _clck=o68dwj%7C2%7Cfm9%7C0%7C1613; _ga_VFVCCQSS9F=GS1.2.**********.1.1.**********.20.0.0; user_session=0g242k8ldmi33of47797m862m1; consult-attribution={%22source%22:%22consumer-home%22%2C%22medium%22:%22web%22%2C%22campaign%22:%22top_symptoms%22}; _hjSessionUser_828794=eyJpZCI6IjA4NWU4MjFkLTM3MTgtNWU5NC1iNzJlLTdhODM2ODMzZjBmMyIsImNyZWF0ZWQiOjE3MTcyNTI1NzA4ODIsImV4aXN0aW5nIjp0cnVlfQ==; _hjSession_828794=eyJpZCI6IjIyYjgxYzNmLWIyZjctNDQ5MS04MTAzLTBkODI3YWFhYjM2ZiIsImMiOjE3MTcyNTI1NzA4ODQsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxLCJzcCI6MH0=; visited_city=bangalore; p_utm_tags=%7B%22utm_source%22%3A%22consumer%20home%22%2C%22utm_medium%22%3A%22dweb%22%7D; sessionId=ac77slj4jvtf5mpe7i2q47ag20; __cfruid=71af8edcb12ee14049ec1b04421feb1fc174ce41-1717252671; _clsk=tylzbx%7C1717252674761%7C9%7C1%7Cz.clarity.ms%2Fcollect; _ga_34LLDZZ06B=GS1.2.1717252680.1.0.1717252680.60.0.0; _cc_id=6bb810915a28029d28342db9ea72597c; panoramaId_expiry=1717339081786; _ga_3FL2X4WEE2=GS1.2.1717252724.1.0.1717252724.0.0.0; enableEC=true; isLoggedIn=false; _gat=1; __practo_sweep__id.ab61=79bb46f05ea748b6.1717252500.1.1717252756.1717252500.ff179507-a5a9-4681-9431-5e961fa97e8a; _uetsid=b1cd27f0202411efa28ead5ac5eb029e; _uetvid=b1cd5380202411ef9df345b7ecf60e53; _ga_CPQ4CDGC0P=GS1.2.1717252632.1.1.1717252784.40.0.0; ph_phc_wktKELppvd8LbWtB9gDh79wOrZbiMsmQge4SaSObYZs_posthog=%7B%22distinct_id%22%3A%22018fd43b-ace4-7318-b988-ff1648866bc0%22%2C%22%24sesid%22%3A%5B1717252787244%2C%22018fd43b-ae0d-7f44-992d-08b581567718%22%2C1717252632077%5D%7D; _ga_NNSR2MKTMP=GS1.2.1717252669.1.1.1717252788.0.0.0; _ga_08Y82JHL62=GS1.2.1717252499.1.1.1717252793.12.0.0; _ga_7RSD8FGHG5=GS1.2.1717252554.1.1.1717252799.60.0.0; _gat_fabric=1; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252802.51.0.0; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A77%2C%22s%22%3A**********%2C%22t%22%3A1717252804%7D; _gat_dchTracker=1; p_omni_data=%7B%22query_type%22%3A%22subspeciality%22%2C%22query_value%22%3A%22General%20Physician%22%2C%22query_value_id%22%3A60%2C%22location_type%22%3A%22city%22%2C%22location_value%22%3A%22Bangalore%22%2C%22location_value_id%22%3A1%7D",
                    "Referer": "https://www.practo.com/bangalore/general-physician?utm_source=consumer-home&utm_medium=dweb",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36"
                },
                "appTranaKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?ad_limit&city&disable_listing_expansion&enable_partner_listing&enable_sensodyne_campaign&is_procedure_cost_page&page&placement&platform&reach_version&show_new_reach_card&speciality&topaz&tracking_id&url_path&utm_medium&utm_source&with_ad&with_seo_data",
                "crawlerBookmark": {
                    "state": 1,
                    "action": 59,
                    "bookmarkId": "state=1|action=59",
                    "actionList": {
                        "serliaziedName": "./datastructure\\action-list.js",
                        "args": ["bookmark", [{
                                    "serliaziedName": "./datastructure\\action-list.js",
                                    "args": ["replay-need-to-add-context-id", [{
                                                "serliaziedName": "./datastructure\\action-list.js",
                                                "args": ["initial actions", [{
                                                            "serliaziedName": "./datastructure\\load-action.js",
                                                            "args": ["[initial]", "https://www.practo.com/", "initial, mustRunAtInit: true"]
                                                        }
                                                    ], "seq", true]
                                            }
                                        ], "seq", false]
                                }, {
                                    "serliaziedName": "./datastructure\\load-action.js",
                                    "args": ["//*[@id=\"root\"]/div/div/div[1]/div[1]/div[1]/div[3]/div/div[1]/div[5]/a", "https://www.practo.com/consult?product=nav&attribution=side_drawer", ""]
                                }
                            ], "seq", false]
                    },
                    "rootActionCount": 1
                },
                "resourceType": "xhr",
                "crawlerKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?ad_limit&city&disable_listing_expansion&enable_partner_listing&enable_sensodyne_campaign&is_procedure_cost_page&page&placement&platform&reach_version&show_new_reach_card&speciality&topaz&tracking_id&url_path&utm_medium&utm_source&with_ad&with_seo_data",
                "uri": "https://www.practo.com/marketplace-api/dweb/search/provider-seo/v2/?utm_medium=dweb&utm_source=consumer-home&url_path=%2Fbangalore%2Fgeneral-physician&page=2&reach_version=v4&ad_limit=2&platform=desktop_web&topaz=true&with_seo_data=true&city=bangalore&enable_partner_listing=true&speciality=general-physician&placement=DOCTOR_SEARCH&is_procedure_cost_page=false&show_new_reach_card=true&tracking_id=68ffc8ed-43be-4d41-bba0-9fa105979381&with_ad=true&disable_listing_expansion=false&enable_sensodyne_campaign=true",
                "haikuKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?utm_medium&utm_source&url_path&page&reach_version&ad_limit&platform&topaz&with_seo_data&city&enable_partner_listing&speciality&placement&is_procedure_cost_page&show_new_reach_card&tracking_id&with_ad&disable_listing_expansion&enable_sensodyne_campaign",
                "haikuResourceType": "core",
                "scanlog_id": 325,
                "scanId": 325,
                "attackRequestId": 1125
            },
            "requestId": 148,
            "haikuPriority": {
                "priority": 100,
                "requestParams": ["utm_medium", "utm_source", "url_path", "page", "reach_version", "ad_limit", "platform", "topaz", "with_seo_data", "city", "enable_partner_listing", "speciality", "placement", "is_procedure_cost_page", "show_new_reach_card", "tracking_id", "with_ad", "disable_listing_expansion", "enable_sensodyne_campaign"],
                "nonEmptyValueCount": 19,
                "pathComponentsCount": 5
            },
            "pluginData": {
                "Remote File Inclusion (RFI)": {},
                "Cross Origin Resource Sharing": {},
                "Email Address Found": {},
                "Possible Sensitive Directory or file": {},
                "Authentication details found": {},
                "HTTP Header Info Disclosure": {},
                "Web Server details found": {
                    "serverdtsfound": true,
                    "Appdtsfound": true
                },
                "Password-submitted-without-ssl": {},
                "Insecure HTTP Transport": {
                    "insecureCount": 72738
                },
                "Insecure HTTP Method Transition": {
                    "InsecureHTTPMethod": {
                        "OriResBodylength": 72738
                    }
                },
                "Application-Error-Message": {},
                "Cookie Manipulation": {
                    "originalChecksum": "09562671c22d17dd698c3e71f70a8d55"
                },
                "Weak Session ID": {},
                "Application Detection": {},
                "Authentication Bypass": {},
                "Apache ETag header Found vulnerability": {},
                "Reveals Sensitive Information": {},
                "SQL Injection": {},
                "Server Side Javascript Injection": {},
                "Server Side Template Injection": {},
                "Local File Inclusion (LFI)": {},
                "Remote XSL Inclusion (RXSLI)": {},
                "Cross Site Scripting (XSS)": {},
                "XPATH Injection": {},
                "OS Command Injection": {},
                "PHP Nginx Command Injection": {},
                "Cross-Site Tracing (XST)": {},
                "Cross-Site Tracing (XST) OOB": {},
                "Http Response Splitting": {},
                "Manually Reported Vuln": {},
                "User-Controllable-Tag-Parameter": {},
                "Unencoded character check": {},
                "SSRF": {
                    "ssrfDetected": true
                },
                "Uncontrolled Format String": {},
                "Iframe Injection": {},
                "Link Injection": {},
                "Breach attack": {},
                "ESI Injection vulnerability": {},
                "Apache log4j vulnerability": {},
                "OS Command Inj OOB": {},
                "SQL Injection OOB": {},
                "Cross Site Scripting (XSS) OOB": {},
                "SSRF OOB": {},
                "Server-Side Template Injection OOB": {},
                "Client-Side Template Injection OOB": {},
                "no captcha on login page": {
                    "": {}
                },
                "Code Injection OOB": {},
                "Cross-Site Flashing (XSF) OOB": {},
                "Link Injection OOB": {},
                "Iframe Injection OOB": {},
                "Text Injection": {},
                "WebSocket URL poisoning": {},
                "Form Action Hijacking": {},
                "Code Injection Attack": {},
                "Expression Language Injection": {},
                "Apache Struts": {},
                "Host Header Injection": {},
                "Web cache poisoning attack": {},
                "Exchange Server SSRF attack": {},
                "Host Header Injection OOB": {},
                "Spring Expression Resource Access RCE": {},
                "DEBUG method enabled": {},
                "PROPFIND method enabled": {},
                "TRACE TRACK method enabled": {},
                "Options enabled": {},
                "HTTP Verb Tampering": {},
                "DotNet Deserialization Found": {},
                "Apache httpd DOS": {},
                "vBulletin-RCE": {},
                "ASP NET Trace method enabled": {},
                "Webadmin": {
                    "php file found": {}
                },
                "Robot Txt File Found": {},
                "Sensitive Documentation Txt File Found": {},
                "CVS Web Repository": {},
                "Apache Server-status Page": {},
                "Possible Backup files": {},
                "Remote Administration Interface Found": {},
                "Predictable Resource Location": {},
                "Browsable Web Directory": {},
                "Apache mod-cgi LFI": {},
                "PRSSI vuln": {},
                "EPMM Authentication Bypass": {},
                "Jenkins Args4j Attack": {},
                "Cookie Checker": {
                    "httpOnlyVulnerabilityFound": true,
                    "secureVulnerabilityFound": true,
                    "sessionCookieScopeVulnerabilityFound": true,
                    "cookiePathOverlyBroadVulnerabilityFound": true
                },
                "Insecure-Flash-Embed-Param-Found": {},
                "Credential Guessing": {},
                "VMware Server-side Template Injection (RCE) Vulnerability": {},
                "Apache OFBiz Authentication Bypass Vulnerability (CVE-2023-51467)": {},
                "ExtJs Arbitrary File Read": {},
                "Clickjacking - HTTP Header Check": {},
                "Hidden-Form-Input-Field": {},
                "JSF ViewState Found": {}
            },
            "httpResponse": {
                "err": null,
                "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"original-12.body\"}",
                "headers": {
                    "date": "Sat, 01 Jun 2024 14:40:15 GMT",
                    "content-type": "application/json; charset=utf-8",
                    "content-length": "16172",
                    "connection": "close",
                    "cid": "03ea0f3bb45c4f3a01b0d95e3bfe083e",
                    "content-encoding": "gzip",
                    "etag": "W/\"11c2e-BlnvpS17xpvGKrm3JBXngzZ5TP0\"",
                    "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
                    "vary": "Accept-Encoding",
                    "x-powered-by": "Express",
                    "cf-cache-status": "DYNAMIC",
                    "server": "cloudflare",
                    "cf-ray": "88cff52f5903837b-BOM",
                    "alt-svc": "h3=\":443\"; ma=86400"
                },
                "statusCode": 200,
                "statusMessage": "OK",
                "redirectsFollowed": 0,
                "redirects": []
            }
        },
        "href": "https://www.practo.com/marketplace-api/dweb/search/provider-seo/v2/?utm_medium=http%3A%2F%2F127.0.0.1%3A443&utm_source=consumer-home&url_path=%2Fbangalore%2Fgeneral-physician&page=2&reach_version=v4&ad_limit=2&platform=desktop_web&topaz=true&with_seo_data=true&city=bangalore&enable_partner_listing=true&speciality=general-physician&placement=DOCTOR_SEARCH&is_procedure_cost_page=false&show_new_reach_card=true&tracking_id=68ffc8ed-43be-4d41-bba0-9fa105979381&with_ad=true&disable_listing_expansion=false&enable_sensodyne_campaign=true",
        "area": "UriQueryParameters",
        "type": "value",
        "param": "utm_medium",
        "vector": "http://127.0.0.1:443",
        "encoding": "uri",
        "headers": {
            "accept": "application/json",
            "accept-language": "en-US",
            "cookie": "_fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _gid=GA1.2.315403102.**********; _ga=GA1.2.527041583.**********; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; p_attr_info=%7B%22session_referrer%22%3A%22Direct%22%2C%22landing_page%22%3A%22Listing%22%7D; hl=en; __practo_sweep__ses.ab61=*; _clck=o68dwj%7C2%7Cfm9%7C0%7C1613; _ga_VFVCCQSS9F=GS1.2.**********.1.1.**********.20.0.0; user_session=0g242k8ldmi33of47797m862m1; consult-attribution={%22source%22:%22consumer-home%22%2C%22medium%22:%22web%22%2C%22campaign%22:%22top_symptoms%22}; _hjSessionUser_828794=eyJpZCI6IjA4NWU4MjFkLTM3MTgtNWU5NC1iNzJlLTdhODM2ODMzZjBmMyIsImNyZWF0ZWQiOjE3MTcyNTI1NzA4ODIsImV4aXN0aW5nIjp0cnVlfQ==; _hjSession_828794=eyJpZCI6IjIyYjgxYzNmLWIyZjctNDQ5MS04MTAzLTBkODI3YWFhYjM2ZiIsImMiOjE3MTcyNTI1NzA4ODQsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxLCJzcCI6MH0=; visited_city=bangalore; p_utm_tags=%7B%22utm_source%22%3A%22consumer%20home%22%2C%22utm_medium%22%3A%22dweb%22%7D; sessionId=ac77slj4jvtf5mpe7i2q47ag20; __cfruid=71af8edcb12ee14049ec1b04421feb1fc174ce41-1717252671; _clsk=tylzbx%7C1717252674761%7C9%7C1%7Cz.clarity.ms%2Fcollect; _ga_34LLDZZ06B=GS1.2.1717252680.1.0.1717252680.60.0.0; _cc_id=6bb810915a28029d28342db9ea72597c; panoramaId_expiry=1717339081786; _ga_3FL2X4WEE2=GS1.2.1717252724.1.0.1717252724.0.0.0; enableEC=true; isLoggedIn=false; _gat=1; __practo_sweep__id.ab61=79bb46f05ea748b6.1717252500.1.1717252756.1717252500.ff179507-a5a9-4681-9431-5e961fa97e8a; _uetsid=b1cd27f0202411efa28ead5ac5eb029e; _uetvid=b1cd5380202411ef9df345b7ecf60e53; _ga_CPQ4CDGC0P=GS1.2.1717252632.1.1.1717252784.40.0.0; ph_phc_wktKELppvd8LbWtB9gDh79wOrZbiMsmQge4SaSObYZs_posthog=%7B%22distinct_id%22%3A%22018fd43b-ace4-7318-b988-ff1648866bc0%22%2C%22%24sesid%22%3A%5B1717252787244%2C%22018fd43b-ae0d-7f44-992d-08b581567718%22%2C1717252632077%5D%7D; _ga_NNSR2MKTMP=GS1.2.1717252669.1.1.1717252788.0.0.0; _ga_08Y82JHL62=GS1.2.1717252499.1.1.1717252793.12.0.0; _ga_7RSD8FGHG5=GS1.2.1717252554.1.1.1717252799.60.0.0; _gat_fabric=1; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252802.51.0.0; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A77%2C%22s%22%3A**********%2C%22t%22%3A1717252804%7D; _gat_dchTracker=1; p_omni_data=%7B%22query_type%22%3A%22subspeciality%22%2C%22query_value%22%3A%22General%20Physician%22%2C%22query_value_id%22%3A60%2C%22location_type%22%3A%22city%22%2C%22location_value%22%3A%22Bangalore%22%2C%22location_value_id%22%3A1%7D; PHPSESSID=96fkcd63lbc35kjq09dp6llg37; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; __cfruid=312dffe59e08bde2ccbcba4294105d8f3bc04e16-1717253570; pelUUID=a2e984ec-6141-4008-aae1-e818356de440",
            "referer": "https://www.practo.com/bangalore/general-physician?utm_source=consumer-home&utm_medium=dweb",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36",
            "host": "www.practo.com",
            "accept-encoding": "gzip, deflate"
        },
        "httpRequest": {
            "method": "GET",
            "headers": {
                "Accept": "application/json",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept-Language": "en-US",
                "Cookie": "_fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _gid=GA1.2.315403102.**********; _ga=GA1.2.527041583.**********; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; p_attr_info=%7B%22session_referrer%22%3A%22Direct%22%2C%22landing_page%22%3A%22Listing%22%7D; hl=en; __practo_sweep__ses.ab61=*; _clck=o68dwj%7C2%7Cfm9%7C0%7C1613; _ga_VFVCCQSS9F=GS1.2.**********.1.1.**********.20.0.0; user_session=0g242k8ldmi33of47797m862m1; consult-attribution={%22source%22:%22consumer-home%22%2C%22medium%22:%22web%22%2C%22campaign%22:%22top_symptoms%22}; _hjSessionUser_828794=eyJpZCI6IjA4NWU4MjFkLTM3MTgtNWU5NC1iNzJlLTdhODM2ODMzZjBmMyIsImNyZWF0ZWQiOjE3MTcyNTI1NzA4ODIsImV4aXN0aW5nIjp0cnVlfQ==; _hjSession_828794=eyJpZCI6IjIyYjgxYzNmLWIyZjctNDQ5MS04MTAzLTBkODI3YWFhYjM2ZiIsImMiOjE3MTcyNTI1NzA4ODQsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxLCJzcCI6MH0=; visited_city=bangalore; p_utm_tags=%7B%22utm_source%22%3A%22consumer%20home%22%2C%22utm_medium%22%3A%22dweb%22%7D; sessionId=ac77slj4jvtf5mpe7i2q47ag20; __cfruid=71af8edcb12ee14049ec1b04421feb1fc174ce41-1717252671; _clsk=tylzbx%7C1717252674761%7C9%7C1%7Cz.clarity.ms%2Fcollect; _ga_34LLDZZ06B=GS1.2.1717252680.1.0.1717252680.60.0.0; _cc_id=6bb810915a28029d28342db9ea72597c; panoramaId_expiry=1717339081786; _ga_3FL2X4WEE2=GS1.2.1717252724.1.0.1717252724.0.0.0; enableEC=true; isLoggedIn=false; _gat=1; __practo_sweep__id.ab61=79bb46f05ea748b6.1717252500.1.1717252756.1717252500.ff179507-a5a9-4681-9431-5e961fa97e8a; _uetsid=b1cd27f0202411efa28ead5ac5eb029e; _uetvid=b1cd5380202411ef9df345b7ecf60e53; _ga_CPQ4CDGC0P=GS1.2.1717252632.1.1.1717252784.40.0.0; ph_phc_wktKELppvd8LbWtB9gDh79wOrZbiMsmQge4SaSObYZs_posthog=%7B%22distinct_id%22%3A%22018fd43b-ace4-7318-b988-ff1648866bc0%22%2C%22%24sesid%22%3A%5B1717252787244%2C%22018fd43b-ae0d-7f44-992d-08b581567718%22%2C1717252632077%5D%7D; _ga_NNSR2MKTMP=GS1.2.1717252669.1.1.1717252788.0.0.0; _ga_08Y82JHL62=GS1.2.1717252499.1.1.1717252793.12.0.0; _ga_7RSD8FGHG5=GS1.2.1717252554.1.1.1717252799.60.0.0; _gat_fabric=1; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252802.51.0.0; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A77%2C%22s%22%3A**********%2C%22t%22%3A1717252804%7D; _gat_dchTracker=1; p_omni_data=%7B%22query_type%22%3A%22subspeciality%22%2C%22query_value%22%3A%22General%20Physician%22%2C%22query_value_id%22%3A60%2C%22location_type%22%3A%22city%22%2C%22location_value%22%3A%22Bangalore%22%2C%22location_value_id%22%3A1%7D",
                "Referer": "https://www.practo.com/bangalore/general-physician?utm_source=consumer-home&utm_medium=dweb",
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "same-origin",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36"
            },
            "appTranaKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?ad_limit&city&disable_listing_expansion&enable_partner_listing&enable_sensodyne_campaign&is_procedure_cost_page&page&placement&platform&reach_version&show_new_reach_card&speciality&topaz&tracking_id&url_path&utm_medium&utm_source&with_ad&with_seo_data",
            "crawlerBookmark": {
                "state": 1,
                "action": 59,
                "bookmarkId": "state=1|action=59",
                "actionList": {
                    "serliaziedName": "./datastructure\\action-list.js",
                    "args": ["bookmark", [{
                                "serliaziedName": "./datastructure\\action-list.js",
                                "args": ["replay-need-to-add-context-id", [{
                                            "serliaziedName": "./datastructure\\action-list.js",
                                            "args": ["initial actions", [{
                                                        "serliaziedName": "./datastructure\\load-action.js",
                                                        "args": ["[initial]", "https://www.practo.com/", "initial, mustRunAtInit: true"]
                                                    }
                                                ], "seq", true]
                                        }
                                    ], "seq", false]
                            }, {
                                "serliaziedName": "./datastructure\\load-action.js",
                                "args": ["//*[@id=\"root\"]/div/div/div[1]/div[1]/div[1]/div[3]/div/div[1]/div[5]/a", "https://www.practo.com/consult?product=nav&attribution=side_drawer", ""]
                            }
                        ], "seq", false]
                },
                "rootActionCount": 1
            },
            "resourceType": "xhr",
            "crawlerKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?ad_limit&city&disable_listing_expansion&enable_partner_listing&enable_sensodyne_campaign&is_procedure_cost_page&page&placement&platform&reach_version&show_new_reach_card&speciality&topaz&tracking_id&url_path&utm_medium&utm_source&with_ad&with_seo_data",
            "uri": "https://www.practo.com/marketplace-api/dweb/search/provider-seo/v2/?utm_medium=http%3A%2F%2F127.0.0.1%3A443&utm_source=consumer-home&url_path=%2Fbangalore%2Fgeneral-physician&page=2&reach_version=v4&ad_limit=2&platform=desktop_web&topaz=true&with_seo_data=true&city=bangalore&enable_partner_listing=true&speciality=general-physician&placement=DOCTOR_SEARCH&is_procedure_cost_page=false&show_new_reach_card=true&tracking_id=68ffc8ed-43be-4d41-bba0-9fa105979381&with_ad=true&disable_listing_expansion=false&enable_sensodyne_campaign=true",
            "haikuKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?utm_medium&utm_source&url_path&page&reach_version&ad_limit&platform&topaz&with_seo_data&city&enable_partner_listing&speciality&placement&is_procedure_cost_page&show_new_reach_card&tracking_id&with_ad&disable_listing_expansion&enable_sensodyne_campaign",
            "haikuResourceType": "core",
            "scanlog_id": 325,
            "scanId": 325,
            "attackRequestId": 2950
        },
        "httpResponse": {
            "headers": {
                "date": "Sat, 01 Jun 2024 14:52:56 GMT",
                "content-type": "application/json; charset=utf-8",
                "content-length": "16174",
                "connection": "close",
                "cid": "a83fc2f6db998702d09a3c53a749cb13",
                "content-encoding": "gzip",
                "etag": "W/\"11c2e-fcf75zvPIgX+lr35JITokIND0rw\"",
                "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
                "vary": "Accept-Encoding",
                "x-powered-by": "Express",
                "cf-cache-status": "DYNAMIC",
                "server": "cloudflare",
                "cf-ray": "88d007c02f9352b0-CCU",
                "alt-svc": "h3=\":443\"; ma=86400"
            },
            "statusCode": 200,
            "statusMessage": "OK",
            "httpVersion": "1.1",
            "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"attack-14.body\"}"
        }
    },
    "response": {
        "headers": {
            "date": "Sat, 01 Jun 2024 14:52:56 GMT",
            "content-type": "application/json; charset=utf-8",
            "content-length": "16174",
            "connection": "close",
            "cid": "a83fc2f6db998702d09a3c53a749cb13",
            "content-encoding": "gzip",
            "etag": "W/\"11c2e-fcf75zvPIgX+lr35JITokIND0rw\"",
            "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
            "vary": "Accept-Encoding",
            "x-powered-by": "Express",
            "cf-cache-status": "DYNAMIC",
            "server": "cloudflare",
            "cf-ray": "88d007c02f9352b0-CCU",
            "alt-svc": "h3=\":443\"; ma=86400"
        },
        "statusCode": 200,
        "statusMessage": "OK",
        "httpVersion": "1.1",
        "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"attack-14.body\"}"
    },
    "vulns": {
        "ID-ssrf": {
            "foundBy": "SSRF",
            "productionReady": false,
            "canAttackInReplayScan": true,
            "details": {
                "href": "https://www.practo.com/marketplace-api/dweb/search/provider-seo/v2/?utm_medium=http%3A%2F%2F127.0.0.1%3A443&utm_source=consumer-home&url_path=%2Fbangalore%2Fgeneral-physician&page=2&reach_version=v4&ad_limit=2&platform=desktop_web&topaz=true&with_seo_data=true&city=bangalore&enable_partner_listing=true&speciality=general-physician&placement=DOCTOR_SEARCH&is_procedure_cost_page=false&show_new_reach_card=true&tracking_id=68ffc8ed-43be-4d41-bba0-9fa105979381&with_ad=true&disable_listing_expansion=false&enable_sensodyne_campaign=true"
            },
            "autoPOC": [{
                    "type": "original",
                    "path": "httpRequest.uri",
                    "highlightType": "text",
                    "details": ["utm_medium=dweb"],
                    "description": "orignal request query utm_medium param value will be attack"
                }, {
                    "type": "attack",
                    "path": "httpRequest.uri",
                    "highlightType": "text",
                    "details": ["utm_medium=http%3A%2F%2F127.0.0.1%3A443"],
                    "description": "attack request query utm_medium param value tampered with vector http%3A%2F%2F127.0.0.1%3A443"
                }
            ]
        }
    }
} {
    "scanId": 325,
    "scanlogId": 325,
    "attack": {
        "attackCount": 1,
        "method": "GET",
        "name": "Unencoded character check",
        "hostname": "www.practo.com",
        "originalRequest": {
            "scanId": 325,
            "scanlog_id": 325,
            "scanner": "haiku",
            "httpRequest": {
                "method": "GET",
                "headers": {
                    "Accept": "application/json",
                    "Accept-Encoding": "gzip, deflate, br",
                    "Accept-Language": "en-US",
                    "Cookie": "_fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _gid=GA1.2.315403102.**********; _ga=GA1.2.527041583.**********; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; p_attr_info=%7B%22session_referrer%22%3A%22Direct%22%2C%22landing_page%22%3A%22Listing%22%7D; hl=en; __practo_sweep__ses.ab61=*; _clck=o68dwj%7C2%7Cfm9%7C0%7C1613; _ga_VFVCCQSS9F=GS1.2.**********.1.1.**********.20.0.0; user_session=0g242k8ldmi33of47797m862m1; consult-attribution={%22source%22:%22consumer-home%22%2C%22medium%22:%22web%22%2C%22campaign%22:%22top_symptoms%22}; _hjSessionUser_828794=eyJpZCI6IjA4NWU4MjFkLTM3MTgtNWU5NC1iNzJlLTdhODM2ODMzZjBmMyIsImNyZWF0ZWQiOjE3MTcyNTI1NzA4ODIsImV4aXN0aW5nIjp0cnVlfQ==; _hjSession_828794=eyJpZCI6IjIyYjgxYzNmLWIyZjctNDQ5MS04MTAzLTBkODI3YWFhYjM2ZiIsImMiOjE3MTcyNTI1NzA4ODQsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxLCJzcCI6MH0=; visited_city=bangalore; p_utm_tags=%7B%22utm_source%22%3A%22consumer%20home%22%2C%22utm_medium%22%3A%22dweb%22%7D; sessionId=ac77slj4jvtf5mpe7i2q47ag20; __cfruid=71af8edcb12ee14049ec1b04421feb1fc174ce41-1717252671; _clsk=tylzbx%7C1717252674761%7C9%7C1%7Cz.clarity.ms%2Fcollect; _ga_34LLDZZ06B=GS1.2.1717252680.1.0.1717252680.60.0.0; _cc_id=6bb810915a28029d28342db9ea72597c; panoramaId_expiry=1717339081786; _ga_3FL2X4WEE2=GS1.2.1717252724.1.0.1717252724.0.0.0; enableEC=true; isLoggedIn=false; _gat=1; __practo_sweep__id.ab61=79bb46f05ea748b6.1717252500.1.1717252756.1717252500.ff179507-a5a9-4681-9431-5e961fa97e8a; _uetsid=b1cd27f0202411efa28ead5ac5eb029e; _uetvid=b1cd5380202411ef9df345b7ecf60e53; _ga_CPQ4CDGC0P=GS1.2.1717252632.1.1.1717252784.40.0.0; ph_phc_wktKELppvd8LbWtB9gDh79wOrZbiMsmQge4SaSObYZs_posthog=%7B%22distinct_id%22%3A%22018fd43b-ace4-7318-b988-ff1648866bc0%22%2C%22%24sesid%22%3A%5B1717252787244%2C%22018fd43b-ae0d-7f44-992d-08b581567718%22%2C1717252632077%5D%7D; _ga_NNSR2MKTMP=GS1.2.1717252669.1.1.1717252788.0.0.0; _ga_08Y82JHL62=GS1.2.1717252499.1.1.1717252793.12.0.0; _ga_7RSD8FGHG5=GS1.2.1717252554.1.1.1717252799.60.0.0; _gat_fabric=1; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252802.51.0.0; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A77%2C%22s%22%3A**********%2C%22t%22%3A1717252804%7D; _gat_dchTracker=1; p_omni_data=%7B%22query_type%22%3A%22subspeciality%22%2C%22query_value%22%3A%22General%20Physician%22%2C%22query_value_id%22%3A60%2C%22location_type%22%3A%22city%22%2C%22location_value%22%3A%22Bangalore%22%2C%22location_value_id%22%3A1%7D",
                    "Referer": "https://www.practo.com/bangalore/general-physician?utm_source=consumer-home&utm_medium=dweb",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36"
                },
                "appTranaKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?ad_limit&city&disable_listing_expansion&enable_partner_listing&enable_sensodyne_campaign&is_procedure_cost_page&page&placement&platform&reach_version&show_new_reach_card&speciality&topaz&tracking_id&url_path&utm_medium&utm_source&with_ad&with_seo_data",
                "crawlerBookmark": {
                    "state": 1,
                    "action": 59,
                    "bookmarkId": "state=1|action=59",
                    "actionList": {
                        "serliaziedName": "./datastructure\\action-list.js",
                        "args": ["bookmark", [{
                                    "serliaziedName": "./datastructure\\action-list.js",
                                    "args": ["replay-need-to-add-context-id", [{
                                                "serliaziedName": "./datastructure\\action-list.js",
                                                "args": ["initial actions", [{
                                                            "serliaziedName": "./datastructure\\load-action.js",
                                                            "args": ["[initial]", "https://www.practo.com/", "initial, mustRunAtInit: true"]
                                                        }
                                                    ], "seq", true]
                                            }
                                        ], "seq", false]
                                }, {
                                    "serliaziedName": "./datastructure\\load-action.js",
                                    "args": ["//*[@id=\"root\"]/div/div/div[1]/div[1]/div[1]/div[3]/div/div[1]/div[5]/a", "https://www.practo.com/consult?product=nav&attribution=side_drawer", ""]
                                }
                            ], "seq", false]
                    },
                    "rootActionCount": 1
                },
                "resourceType": "xhr",
                "crawlerKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?ad_limit&city&disable_listing_expansion&enable_partner_listing&enable_sensodyne_campaign&is_procedure_cost_page&page&placement&platform&reach_version&show_new_reach_card&speciality&topaz&tracking_id&url_path&utm_medium&utm_source&with_ad&with_seo_data",
                "uri": "https://www.practo.com/marketplace-api/dweb/search/provider-seo/v2/?utm_medium=dweb&utm_source=consumer-home&url_path=%2Fbangalore%2Fgeneral-physician&page=2&reach_version=v4&ad_limit=2&platform=desktop_web&topaz=true&with_seo_data=true&city=bangalore&enable_partner_listing=true&speciality=general-physician&placement=DOCTOR_SEARCH&is_procedure_cost_page=false&show_new_reach_card=true&tracking_id=68ffc8ed-43be-4d41-bba0-9fa105979381&with_ad=true&disable_listing_expansion=false&enable_sensodyne_campaign=true",
                "haikuKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?utm_medium&utm_source&url_path&page&reach_version&ad_limit&platform&topaz&with_seo_data&city&enable_partner_listing&speciality&placement&is_procedure_cost_page&show_new_reach_card&tracking_id&with_ad&disable_listing_expansion&enable_sensodyne_campaign",
                "haikuResourceType": "core",
                "scanlog_id": 325,
                "scanId": 325,
                "attackRequestId": 1125
            },
            "requestId": 148,
            "haikuPriority": {
                "priority": 100,
                "requestParams": ["utm_medium", "utm_source", "url_path", "page", "reach_version", "ad_limit", "platform", "topaz", "with_seo_data", "city", "enable_partner_listing", "speciality", "placement", "is_procedure_cost_page", "show_new_reach_card", "tracking_id", "with_ad", "disable_listing_expansion", "enable_sensodyne_campaign"],
                "nonEmptyValueCount": 19,
                "pathComponentsCount": 5
            },
            "pluginData": {
                "Remote File Inclusion (RFI)": {},
                "Cross Origin Resource Sharing": {},
                "Email Address Found": {},
                "Possible Sensitive Directory or file": {},
                "Authentication details found": {},
                "HTTP Header Info Disclosure": {},
                "Web Server details found": {
                    "serverdtsfound": true,
                    "Appdtsfound": true
                },
                "Password-submitted-without-ssl": {},
                "Insecure HTTP Transport": {
                    "insecureCount": 72738
                },
                "Insecure HTTP Method Transition": {
                    "InsecureHTTPMethod": {
                        "OriResBodylength": 72738
                    }
                },
                "Application-Error-Message": {},
                "Cookie Manipulation": {
                    "originalChecksum": "09562671c22d17dd698c3e71f70a8d55"
                },
                "Weak Session ID": {},
                "Application Detection": {},
                "Authentication Bypass": {},
                "Apache ETag header Found vulnerability": {},
                "Reveals Sensitive Information": {},
                "SQL Injection": {},
                "Server Side Javascript Injection": {},
                "Server Side Template Injection": {},
                "Local File Inclusion (LFI)": {},
                "Remote XSL Inclusion (RXSLI)": {},
                "Cross Site Scripting (XSS)": {},
                "XPATH Injection": {},
                "OS Command Injection": {},
                "PHP Nginx Command Injection": {},
                "Cross-Site Tracing (XST)": {},
                "Cross-Site Tracing (XST) OOB": {},
                "Http Response Splitting": {},
                "Manually Reported Vuln": {},
                "User-Controllable-Tag-Parameter": {},
                "Unencoded character check": {
                    "CharFound": true
                },
                "SSRF": {
                    "ssrfDetected": true
                },
                "Uncontrolled Format String": {},
                "Iframe Injection": {},
                "Link Injection": {},
                "Breach attack": {},
                "ESI Injection vulnerability": {},
                "Apache log4j vulnerability": {},
                "OS Command Inj OOB": {},
                "SQL Injection OOB": {},
                "Cross Site Scripting (XSS) OOB": {},
                "SSRF OOB": {},
                "Server-Side Template Injection OOB": {},
                "Client-Side Template Injection OOB": {},
                "no captcha on login page": {
                    "": {}
                },
                "Code Injection OOB": {},
                "Cross-Site Flashing (XSF) OOB": {},
                "Link Injection OOB": {},
                "Iframe Injection OOB": {},
                "Text Injection": {},
                "WebSocket URL poisoning": {},
                "Form Action Hijacking": {},
                "Code Injection Attack": {},
                "Expression Language Injection": {},
                "Apache Struts": {},
                "Host Header Injection": {},
                "Web cache poisoning attack": {},
                "Exchange Server SSRF attack": {},
                "Host Header Injection OOB": {},
                "Spring Expression Resource Access RCE": {},
                "DEBUG method enabled": {},
                "PROPFIND method enabled": {},
                "TRACE TRACK method enabled": {},
                "Options enabled": {},
                "HTTP Verb Tampering": {},
                "DotNet Deserialization Found": {},
                "Apache httpd DOS": {},
                "vBulletin-RCE": {},
                "ASP NET Trace method enabled": {},
                "Webadmin": {
                    "php file found": {}
                },
                "Robot Txt File Found": {},
                "Sensitive Documentation Txt File Found": {},
                "CVS Web Repository": {},
                "Apache Server-status Page": {},
                "Possible Backup files": {},
                "Remote Administration Interface Found": {},
                "Predictable Resource Location": {},
                "Browsable Web Directory": {},
                "Apache mod-cgi LFI": {},
                "PRSSI vuln": {},
                "EPMM Authentication Bypass": {},
                "Jenkins Args4j Attack": {},
                "Cookie Checker": {
                    "httpOnlyVulnerabilityFound": true,
                    "secureVulnerabilityFound": true,
                    "sessionCookieScopeVulnerabilityFound": true,
                    "cookiePathOverlyBroadVulnerabilityFound": true
                },
                "Insecure-Flash-Embed-Param-Found": {},
                "Credential Guessing": {},
                "VMware Server-side Template Injection (RCE) Vulnerability": {},
                "Apache OFBiz Authentication Bypass Vulnerability (CVE-2023-51467)": {},
                "ExtJs Arbitrary File Read": {},
                "Clickjacking - HTTP Header Check": {},
                "Hidden-Form-Input-Field": {},
                "JSF ViewState Found": {}
            },
            "httpResponse": {
                "err": null,
                "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"original-12.body\"}",
                "headers": {
                    "date": "Sat, 01 Jun 2024 14:40:15 GMT",
                    "content-type": "application/json; charset=utf-8",
                    "content-length": "16172",
                    "connection": "close",
                    "cid": "03ea0f3bb45c4f3a01b0d95e3bfe083e",
                    "content-encoding": "gzip",
                    "etag": "W/\"11c2e-BlnvpS17xpvGKrm3JBXngzZ5TP0\"",
                    "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
                    "vary": "Accept-Encoding",
                    "x-powered-by": "Express",
                    "cf-cache-status": "DYNAMIC",
                    "server": "cloudflare",
                    "cf-ray": "88cff52f5903837b-BOM",
                    "alt-svc": "h3=\":443\"; ma=86400"
                },
                "statusCode": 200,
                "statusMessage": "OK",
                "redirectsFollowed": 0,
                "redirects": []
            }
        },
        "href": "https://www.practo.com/marketplace-api/dweb/search/provider-seo/v2/?utm_medium=dweb&utm_source=%3Cxss%3Dhaikutest(1)%3E&url_path=%2Fbangalore%2Fgeneral-physician&page=2&reach_version=v4&ad_limit=2&platform=desktop_web&topaz=true&with_seo_data=true&city=bangalore&enable_partner_listing=true&speciality=general-physician&placement=DOCTOR_SEARCH&is_procedure_cost_page=false&show_new_reach_card=true&tracking_id=68ffc8ed-43be-4d41-bba0-9fa105979381&with_ad=true&disable_listing_expansion=false&enable_sensodyne_campaign=true",
        "area": "UriQueryParameters",
        "type": "value",
        "param": "utm_source",
        "vector": "<xss=haikutest(1)>",
        "encoding": "uri",
        "headers": {
            "accept": "application/json",
            "accept-language": "en-US",
            "cookie": "_fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _gid=GA1.2.315403102.**********; _ga=GA1.2.527041583.**********; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; p_attr_info=%7B%22session_referrer%22%3A%22Direct%22%2C%22landing_page%22%3A%22Listing%22%7D; hl=en; __practo_sweep__ses.ab61=*; _clck=o68dwj%7C2%7Cfm9%7C0%7C1613; _ga_VFVCCQSS9F=GS1.2.**********.1.1.**********.20.0.0; user_session=0g242k8ldmi33of47797m862m1; consult-attribution={%22source%22:%22consumer-home%22%2C%22medium%22:%22web%22%2C%22campaign%22:%22top_symptoms%22}; _hjSessionUser_828794=eyJpZCI6IjA4NWU4MjFkLTM3MTgtNWU5NC1iNzJlLTdhODM2ODMzZjBmMyIsImNyZWF0ZWQiOjE3MTcyNTI1NzA4ODIsImV4aXN0aW5nIjp0cnVlfQ==; _hjSession_828794=eyJpZCI6IjIyYjgxYzNmLWIyZjctNDQ5MS04MTAzLTBkODI3YWFhYjM2ZiIsImMiOjE3MTcyNTI1NzA4ODQsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxLCJzcCI6MH0=; visited_city=bangalore; p_utm_tags=%7B%22utm_source%22%3A%22consumer%20home%22%2C%22utm_medium%22%3A%22dweb%22%7D; sessionId=ac77slj4jvtf5mpe7i2q47ag20; __cfruid=71af8edcb12ee14049ec1b04421feb1fc174ce41-1717252671; _clsk=tylzbx%7C1717252674761%7C9%7C1%7Cz.clarity.ms%2Fcollect; _ga_34LLDZZ06B=GS1.2.1717252680.1.0.1717252680.60.0.0; _cc_id=6bb810915a28029d28342db9ea72597c; panoramaId_expiry=1717339081786; _ga_3FL2X4WEE2=GS1.2.1717252724.1.0.1717252724.0.0.0; enableEC=true; isLoggedIn=false; _gat=1; __practo_sweep__id.ab61=79bb46f05ea748b6.1717252500.1.1717252756.1717252500.ff179507-a5a9-4681-9431-5e961fa97e8a; _uetsid=b1cd27f0202411efa28ead5ac5eb029e; _uetvid=b1cd5380202411ef9df345b7ecf60e53; _ga_CPQ4CDGC0P=GS1.2.1717252632.1.1.1717252784.40.0.0; ph_phc_wktKELppvd8LbWtB9gDh79wOrZbiMsmQge4SaSObYZs_posthog=%7B%22distinct_id%22%3A%22018fd43b-ace4-7318-b988-ff1648866bc0%22%2C%22%24sesid%22%3A%5B1717252787244%2C%22018fd43b-ae0d-7f44-992d-08b581567718%22%2C1717252632077%5D%7D; _ga_NNSR2MKTMP=GS1.2.1717252669.1.1.1717252788.0.0.0; _ga_08Y82JHL62=GS1.2.1717252499.1.1.1717252793.12.0.0; _ga_7RSD8FGHG5=GS1.2.1717252554.1.1.1717252799.60.0.0; _gat_fabric=1; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252802.51.0.0; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A77%2C%22s%22%3A**********%2C%22t%22%3A1717252804%7D; _gat_dchTracker=1; p_omni_data=%7B%22query_type%22%3A%22subspeciality%22%2C%22query_value%22%3A%22General%20Physician%22%2C%22query_value_id%22%3A60%2C%22location_type%22%3A%22city%22%2C%22location_value%22%3A%22Bangalore%22%2C%22location_value_id%22%3A1%7D; PHPSESSID=96fkcd63lbc35kjq09dp6llg37; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; __cfruid=6b03aa054c46ae8975507548b389755322f1fd56-1717253835; pelUUID=a2e984ec-6141-4008-aae1-e818356de440",
            "referer": "https://www.practo.com/bangalore/general-physician?utm_source=consumer-home&utm_medium=dweb",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36",
            "host": "www.practo.com",
            "accept-encoding": "gzip, deflate"
        },
        "httpRequest": {
            "method": "GET",
            "headers": {
                "Accept": "application/json",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept-Language": "en-US",
                "Cookie": "_fbp=fb.1.1717252440910.**********; google_one_tap_attempt=1; _gcl_au=1.1.**********.**********; _gid=GA1.2.315403102.**********; _ga=GA1.2.527041583.**********; WZRK_G=72255c01167541c6bef49d46aed8c328; pelUUID=18fd438c81e116a-06e6e3d1b38409-b321f6c-e1000-18fd438c81f1547; PHPSESSID=ac77slj4jvtf5mpe7i2q47ag20; availability_pla_client_id=730EE403-9259-5F72-FB54-A4D1F57035AF; initialReferrer=; p_attr_info=%7B%22session_referrer%22%3A%22Direct%22%2C%22landing_page%22%3A%22Listing%22%7D; hl=en; __practo_sweep__ses.ab61=*; _clck=o68dwj%7C2%7Cfm9%7C0%7C1613; _ga_VFVCCQSS9F=GS1.2.**********.1.1.**********.20.0.0; user_session=0g242k8ldmi33of47797m862m1; consult-attribution={%22source%22:%22consumer-home%22%2C%22medium%22:%22web%22%2C%22campaign%22:%22top_symptoms%22}; _hjSessionUser_828794=eyJpZCI6IjA4NWU4MjFkLTM3MTgtNWU5NC1iNzJlLTdhODM2ODMzZjBmMyIsImNyZWF0ZWQiOjE3MTcyNTI1NzA4ODIsImV4aXN0aW5nIjp0cnVlfQ==; _hjSession_828794=eyJpZCI6IjIyYjgxYzNmLWIyZjctNDQ5MS04MTAzLTBkODI3YWFhYjM2ZiIsImMiOjE3MTcyNTI1NzA4ODQsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxLCJzcCI6MH0=; visited_city=bangalore; p_utm_tags=%7B%22utm_source%22%3A%22consumer%20home%22%2C%22utm_medium%22%3A%22dweb%22%7D; sessionId=ac77slj4jvtf5mpe7i2q47ag20; __cfruid=71af8edcb12ee14049ec1b04421feb1fc174ce41-1717252671; _clsk=tylzbx%7C1717252674761%7C9%7C1%7Cz.clarity.ms%2Fcollect; _ga_34LLDZZ06B=GS1.2.1717252680.1.0.1717252680.60.0.0; _cc_id=6bb810915a28029d28342db9ea72597c; panoramaId_expiry=1717339081786; _ga_3FL2X4WEE2=GS1.2.1717252724.1.0.1717252724.0.0.0; enableEC=true; isLoggedIn=false; _gat=1; __practo_sweep__id.ab61=79bb46f05ea748b6.1717252500.1.1717252756.1717252500.ff179507-a5a9-4681-9431-5e961fa97e8a; _uetsid=b1cd27f0202411efa28ead5ac5eb029e; _uetvid=b1cd5380202411ef9df345b7ecf60e53; _ga_CPQ4CDGC0P=GS1.2.1717252632.1.1.1717252784.40.0.0; ph_phc_wktKELppvd8LbWtB9gDh79wOrZbiMsmQge4SaSObYZs_posthog=%7B%22distinct_id%22%3A%22018fd43b-ace4-7318-b988-ff1648866bc0%22%2C%22%24sesid%22%3A%5B1717252787244%2C%22018fd43b-ae0d-7f44-992d-08b581567718%22%2C1717252632077%5D%7D; _ga_NNSR2MKTMP=GS1.2.1717252669.1.1.1717252788.0.0.0; _ga_08Y82JHL62=GS1.2.1717252499.1.1.1717252793.12.0.0; _ga_7RSD8FGHG5=GS1.2.1717252554.1.1.1717252799.60.0.0; _gat_fabric=1; _ga_CHP6SFVZ2Y=GS1.2.**********.1.1.1717252802.51.0.0; WZRK_S_8W6-695-WK5Z=%7B%22p%22%3A77%2C%22s%22%3A**********%2C%22t%22%3A1717252804%7D; _gat_dchTracker=1; p_omni_data=%7B%22query_type%22%3A%22subspeciality%22%2C%22query_value%22%3A%22General%20Physician%22%2C%22query_value_id%22%3A60%2C%22location_type%22%3A%22city%22%2C%22location_value%22%3A%22Bangalore%22%2C%22location_value_id%22%3A1%7D",
                "Referer": "https://www.practo.com/bangalore/general-physician?utm_source=consumer-home&utm_medium=dweb",
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "same-origin",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Electron/11.5.0 Safari/537.36"
            },
            "appTranaKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?ad_limit&city&disable_listing_expansion&enable_partner_listing&enable_sensodyne_campaign&is_procedure_cost_page&page&placement&platform&reach_version&show_new_reach_card&speciality&topaz&tracking_id&url_path&utm_medium&utm_source&with_ad&with_seo_data",
            "crawlerBookmark": {
                "state": 1,
                "action": 59,
                "bookmarkId": "state=1|action=59",
                "actionList": {
                    "serliaziedName": "./datastructure\\action-list.js",
                    "args": ["bookmark", [{
                                "serliaziedName": "./datastructure\\action-list.js",
                                "args": ["replay-need-to-add-context-id", [{
                                            "serliaziedName": "./datastructure\\action-list.js",
                                            "args": ["initial actions", [{
                                                        "serliaziedName": "./datastructure\\load-action.js",
                                                        "args": ["[initial]", "https://www.practo.com/", "initial, mustRunAtInit: true"]
                                                    }
                                                ], "seq", true]
                                        }
                                    ], "seq", false]
                            }, {
                                "serliaziedName": "./datastructure\\load-action.js",
                                "args": ["//*[@id=\"root\"]/div/div/div[1]/div[1]/div[1]/div[3]/div/div[1]/div[5]/a", "https://www.practo.com/consult?product=nav&attribution=side_drawer", ""]
                            }
                        ], "seq", false]
                },
                "rootActionCount": 1
            },
            "resourceType": "xhr",
            "crawlerKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?ad_limit&city&disable_listing_expansion&enable_partner_listing&enable_sensodyne_campaign&is_procedure_cost_page&page&placement&platform&reach_version&show_new_reach_card&speciality&topaz&tracking_id&url_path&utm_medium&utm_source&with_ad&with_seo_data",
            "uri": "https://www.practo.com/marketplace-api/dweb/search/provider-seo/v2/?utm_medium=dweb&utm_source=%3Cxss%3Dhaikutest(1)%3E&url_path=%2Fbangalore%2Fgeneral-physician&page=2&reach_version=v4&ad_limit=2&platform=desktop_web&topaz=true&with_seo_data=true&city=bangalore&enable_partner_listing=true&speciality=general-physician&placement=DOCTOR_SEARCH&is_procedure_cost_page=false&show_new_reach_card=true&tracking_id=68ffc8ed-43be-4d41-bba0-9fa105979381&with_ad=true&disable_listing_expansion=false&enable_sensodyne_campaign=true",
            "haikuKey": "GET-practo.com/marketplace-api/dweb/search/provider-seo/v2?utm_medium&utm_source&url_path&page&reach_version&ad_limit&platform&topaz&with_seo_data&city&enable_partner_listing&speciality&placement&is_procedure_cost_page&show_new_reach_card&tracking_id&with_ad&disable_listing_expansion&enable_sensodyne_campaign",
            "haikuResourceType": "core",
            "scanlog_id": 325,
            "scanId": 325,
            "attackRequestId": 3113
        },
        "httpResponse": {
            "headers": {
                "date": "Sat, 01 Jun 2024 14:57:16 GMT",
                "content-type": "application/json; charset=utf-8",
                "transfer-encoding": "chunked",
                "connection": "close",
                "cid": "a071970c44231cd7d364820f85d2c1d2",
                "content-encoding": "gzip",
                "etag": "W/\"11c60-lvnqyxGn4G3kNCXA9vjqqPuTpK0\"",
                "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
                "vary": "Accept-Encoding",
                "x-powered-by": "Express",
                "cf-cache-status": "DYNAMIC",
                "server": "cloudflare",
                "cf-ray": "88d00e198aa43ac3-BOM",
                "alt-svc": "h3=\":443\"; ma=86400"
            },
            "statusCode": 200,
            "statusMessage": "OK",
            "httpVersion": "1.1",
            "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"attack-15.body\"}"
        }
    },
    "response": {
        "headers": {
            "date": "Sat, 01 Jun 2024 14:57:16 GMT",
            "content-type": "application/json; charset=utf-8",
            "transfer-encoding": "chunked",
            "connection": "close",
            "cid": "a071970c44231cd7d364820f85d2c1d2",
            "content-encoding": "gzip",
            "etag": "W/\"11c60-lvnqyxGn4G3kNCXA9vjqqPuTpK0\"",
            "strict-transport-security": "max-age=63072000; includeSubDomains; preload",
            "vary": "Accept-Encoding",
            "x-powered-by": "Express",
            "cf-cache-status": "DYNAMIC",
            "server": "cloudflare",
            "cf-ray": "88d00e198aa43ac3-BOM",
            "alt-svc": "h3=\":443\"; ma=86400"
        },
        "statusCode": 200,
        "statusMessage": "OK",
        "httpVersion": "1.1",
        "body": "{\"prefix\":\"scanner/httpResponse/325\",\"name\":\"attack-15.body\"}"
    },
    "vulns": {
        "ID-unencoded-characters": {
            "foundBy": "Unencoded character check",
            "productionReady": true,
            "canAttackInReplayScan": true,
            "details": {
                "result": "<xss=haikutest(1)>"
            },
            "autoPOC": [{
                    "type": "original",
                    "path": "httpRequest.uri",
                    "highlightType": "text",
                    "details": ["utm_source=consumer-home"],
                    "description": "orignal request query utm_source param value will be attack"
                }, {
                    "type": "attack",
                    "path": "httpRequest.uri",
                    "highlightType": "text",
                    "details": ["utm_source=%3Cxss%3Dhaikutest(1)%3E"],
                    "description": "attack request query utm_source param value tampered with vector %3Cxss%3Dhaikutest(1)%3E"
                }
            ]
        }
    }
}
