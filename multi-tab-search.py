import webbrowser

def search_cve_on_nvd(cve_list):
    base_url = 'https://nvd.nist.gov/vuln/detail/'
    for cve in cve_list:
        search_url = base_url + cve
        webbrowser.open_new_tab(search_url)

if __name__ == "__main__":
    cve_input = """
CVE-2024-38348
CVE-2024-37802
CVE-2024-3558
CVE-2024-34994
CVE-2024-5676
CVE-2024-36116
CVE-2024-38358
CVE-2024-38357
CVE-2024-38356
CVE-2024-3561
CVE-2024-36117
CVE-2024-36115
CVE-2024-34993
CVE-2024-37626
CVE-2024-6214
CVE-2024-6191
CVE-2024-37674
CVE-2024-6216
CVE-2024-6218
CVE-2024-6213
CVE-2024-6217
CVE-2024-6215
CVE-2024-6184
CVE-2024-6190
CVE-2024-6192
CVE-2024-6186
CVE-2024-6185
CVE-2024-31586
CVE-2024-6196
CVE-2024-6193
CVE-2024-6194
CVE-2024-6195
CVE-2024-6187
CVE-2024-5182
CVE-2024-30848
CVE-2024-29390
CVE-2021-47621
CVE-2024-6253
CVE-2023-37898
CVE-2023-38506
CVE-2023-39517
CVE-2024-6267
CVE-2024-6268
CVE-2024-34452
CVE-2024-5443
CVE-2024-6269
CVE-2024-21516
CVE-2024-21515
CVE-2024-21514
CVE-2024-21518
CVE-2024-21517
CVE-2024-6273
CVE-2023-49793
CVE-2024-37679
CVE-2024-34312
CVE-2024-34313
CVE-2024-37680
CVE-2024-4748
CVE-2024-37678
CVE-2024-6308
CVE-2024-38894
CVE-2024-38896
CVE-2024-36819

"""
    cve_list = cve_input.strip().split('\n')
    search_cve_on_nvd(cve_list)
