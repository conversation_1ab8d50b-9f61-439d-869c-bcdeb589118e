{"resultsPerPage": 15, "startIndex": 0, "totalResults": 15, "format": "NVD_CVE", "version": "2.0", "timestamp": "2023-03-08T17:22:01.377", "vulnerabilities": [{"cve": {"id": "CVE-2022-27596", "sourceIdentifier": "<EMAIL>", "descriptions": [{"lang": "en", "value": "A vulner later"}], "metrics": {"cvssMetricV31": [{"source": "<EMAIL>", "type": "Primary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "attackVector": "NETWORK", "scope": "UNCHANGED", "confidentialityImpact": "HIGH", "integrityImpact": "HIGH", "availabilityImpact": "HIGH", "baseScore": 9.8, "baseSeverity": "CRITICAL"}, "exploitabilityScore": 3.9, "impactScore": 5.9}, {"source": "<EMAIL>", "type": "Secondary", "cvssData": {"version": "3.1", "baseSeverity": "CRITICAL"}, "exploitabilityScore": 3.9, "impactScore": 5.9}]}, "weaknesses": [{"source": "<EMAIL>", "type": "Primary", "description": [{"lang": "en", "value": "CWE-89"}]}, {"source": "<EMAIL>", "type": "Secondary", "description": [{"lang": "en", "value": "CWE-89"}]}], "configurations": [{"nodes": [{"operator": "OR", "negate": false, "cpeMatch": [{"vulnerable": true, "criteria": "cpe:2.3:o:qnap:qts:*:*:*:*:*:*:*:*", "versionStartIncluding": "5.0.1", "versionEndExcluding": "5.0.1.2234", "matchCriteriaId": "EDB678B3-A51E-4F60-B049-FED59A368B9B"}, {"vulnerable": true, "criteria": "cpe:2.3:o:qnap:quts_hero:*:*:*:*:*:*:*:*", "versionStartIncluding": "h5.0.1", "versionEndExcluding": "h5.0.1.2248", "matchCriteriaId": "0B1CD08D-792B-45D9-8CCA-5222EE75A870"}]}]}], "references": [{"url": "https://www.qnap.com/en/security-advisory/qsa-23-01", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}}, {"cve": {"id": "CVE-2023-24612", "sourceIdentifier": "<EMAIL>", "published": "2023-01-30T03:15:09.557", "lastModified": "2023-02-07T19:22:09.070", "vulnStatus": "Analyzed", "descriptions": [{"lang": "en", "value": "The PdfBook extension through 2.0.5 before b07b6a64 for MediaWiki allows command injection via an option."}], "metrics": {"cvssMetricV31": [{"source": "<EMAIL>", "type": "Primary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "attackVector": "NETWORK", "attackComplexity": "LOW", "privilegesRequired": "NONE", "userInteraction": "NONE", "scope": "UNCHANGED", "confidentialityImpact": "HIGH", "integrityImpact": "HIGH", "availabilityImpact": "HIGH", "baseScore": 9.8, "baseSeverity": "CRITICAL"}, "exploitabilityScore": 3.9, "impactScore": 5.9}]}, "weaknesses": [{"source": "<EMAIL>", "type": "Primary", "description": [{"lang": "en", "value": "CWE-77"}]}], "configurations": [{"nodes": [{"operator": "OR", "negate": false, "cpeMatch": [{"vulnerable": true, "criteria": "cpe:2.3:a:pdfbook_project:pdfbook:*:*:*:*:*:mediawiki:*:*", "versionEndIncluding": "2.0.5", "matchCriteriaId": "345E9C6F-4838-487F-99A2-DA98A79330ED"}]}]}], "references": [{"url": "https://gitlab.com/organicdesign/PdfBook/-/merge_requests/6", "source": "<EMAIL>", "tags": ["Patch", "Third Party Advisory"]}]}}, {"cve": {"id": "CVE-2022-46358", "sourceIdentifier": "<EMAIL>", "published": "2023-01-30T08:15:08.143", "lastModified": "2023-02-07T16:28:52.190", "vulnStatus": "Analyzed", "descriptions": [{"lang": "en", "value": "Potentosure."}], "metrics": {"cvssMetricV31": [{"source": "<EMAIL>", "type": "Primary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:H", "attackVector": "LOCAL", "attackComplexity": "LOW", "privilegesRequired": "LOW", "userInteraction": "NONE", "scope": "CHANGED", "confidentialityImpact": "HIGH", "integrityImpact": "HIGH", "availabilityImpact": "HIGH", "baseScore": 8.8, "baseSeverity": "HIGH"}, "exploitabilityScore": 2.0, "impactScore": 6.0}]}, "weaknesses": [{"source": "<EMAIL>", "type": "Primary", "description": [{"lang": "en", "value": "NVD-CWE-noinfo"}]}], "configurations": [{"nodes": [{"operator": "OR", "negate": false, "cpeMatch": [{"vulnerable": true, "criteria": "cpe:2.3:a:hp:security_manager:*:*:*:*:*:*:*:*", "versionEndExcluding": "3.9", "matchCriteriaId": "269DDB99-D097-4CA2-BDD4-29074A409464"}]}]}], "references": [{"url": "https://support.hp.com/us-en/document/ish_7334353-7334378-16", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}}, {"cve": {"id": "CVE-2022-46359", "sourceIdentifier": "<EMAIL>", "published": "2023-01-30T08:15:08.200", "lastModified": "2023-02-07T16:29:57.773", "vulnStatus": "Analyzed", "descriptions": [{"lang": "en", "value": "Potentialosure."}], "metrics": {"cvssMetricV31": [{"source": "<EMAIL>", "type": "Primary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:H", "userInteraction": "NONE", "scope": "CHANGED", "confidentialityImpact": "HIGH", "integrityImpact": "HIGH", "availabilityImpact": "HIGH", "baseScore": 8.8, "baseSeverity": "HIGH"}, "exploitabilityScore": 2.0, "impactScore": 6.0}]}, "weaknesses": [{"source": "<EMAIL>", "type": "Primary", "description": [{"lang": "en", "value": "NVD-CWE-noinfo"}]}], "configurations": [{"nodes": [{"operator": "OR", "negate": false, "cpeMatch": [{"vulnerable": true, "criteria": "cpe:2.3:a:hp:security_manager:*:*:*:*:*:*:*:*", "versionEndExcluding": "3.9", "matchCriteriaId": "269DDB99-D097-4CA2-BDD4-29074A409464"}]}]}], "references": [{"url": "https://support.hp.com/us-en/document/ish_7334353-7334378-16", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}}]}