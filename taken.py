import re

def process_response(response_body, args_regexp):
    # Initialize empty lists for user details, token details, and session details
    userdetails = []
    tokendetails = []
    sessiondetails = []

    # Define the regular expressions to match the form tags and input tags
    form_regexp = re.compile(r'< ?form.+method ?= ?("|\'|)GET\1.*?>[\w\W]+?< ?\/form ?>', re.IGNORECASE)
    input_regexp = re.compile(r'< ?input[\w\W]+?>', re.IGNORECASE)

    # Find all form tags in the response body
    form_tags = form_regexp.findall(response_body)
    
    for tag in form_tags:
        if tag and len(tag) > 20 and args_regexp.search(tag):
            # Find all input tags in the form
            input_tags = input_regexp.findall(tag)
            
            for stag in input_tags:
                argname_match = re.search(r'name ?= ?("|\'|)[\w\W]+?\1', stag, re.IGNORECASE)
                
                if argname_match:
                    try:
                        val_match = re.search(r'value ?= ?("|\'|)[\w\W]+?\1', stag, re.IGNORECASE)
                        
                        if val_match:
                            val = val_match.group().split('=')[1].replace('"', '').replace("'", "")
                            
                            if val and not re.search(r'\*', val):
                                if args_regexp.search(argname_match.group()):
                                    userdetails.append({'result': stag})
                                
                                if re.search(r'session', argname_match.group(), re.IGNORECASE):
                                    sessiondetails.append({'result': stag})
                                
                                if re.search(r'token', argname_match.group(), re.IGNORECASE):
                                    tokendetails.append({'result': stag})
                                    
                    except Exception as e:
                        return
    
    if tokendetails:
        print(tokendetails)

    return userdetails, sessiondetails, tokendetails

# Example usage
args_regexp = re.compile(r'some_regex')  # Replace with your actual regex

# Step 1: Read response body from text file
with open('response_body.txt', 'r') as file:
    response_body = file.read()

# Step 2: Process the response body
userdetails, sessiondetails, tokendetails = process_response(response_body, args_regexp)

# Print the results
print("User Details:", userdetails)
print("Session Details:", sessiondetails)
print("Token Details:", tokendetails)
