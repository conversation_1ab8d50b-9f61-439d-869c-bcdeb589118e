import re

def extract_plugin_names(js_file_path):
    with open(js_file_path, 'r', encoding='utf-8') as file:
        content = file.read()

    # Extract Plugins block
    plugins_start = re.search(r'Plugins\s*:\s*{', content)
    if not plugins_start:
        print("Plugins block not found.")
        return []

    start_idx = plugins_start.end()
    brace_count = 1
    idx = start_idx

    # Track closing brace of Plugins block
    while brace_count > 0 and idx < len(content):
        if content[idx] == '{':
            brace_count += 1
        elif content[idx] == '}':
            brace_count -= 1
        idx += 1

    plugins_text = content[start_idx:idx - 1]

    # Find each plugin block
    plugin_pattern = re.compile(r"'([^']+)'\s*:\s*{(.*?)}\s*(,|\n|$)", re.DOTALL)
    plugins = plugin_pattern.findall(plugins_text)

    matching_plugins = []

    for plugin_name, plugin_body, _ in plugins:
        # Flatten and lower the entire plugin block
        body_clean = re.sub(r'\s+', '', plugin_body.lower())
        if 'canattackinapiskan:true' in body_clean or 'processattackresponseinapiskan:true' in body_clean:
            matching_plugins.append(plugin_name)

    return matching_plugins


# File path on desktop
js_file_path = r'C:\Users\<USER>\Desktop\network.js'

if __name__ == "__main__":
    matched_plugins = extract_plugin_names(js_file_path)

    print("\n=== Matched Plugin Names ===\n")
    for plugin in matched_plugins:
        print(plugin)

    print(f"\nTotal Matched: {len(matched_plugins)}")
