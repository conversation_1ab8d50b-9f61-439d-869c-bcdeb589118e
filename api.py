from flask import Flask, request, jsonify

app = Flask(__name__)

@app.route('/api/user/info', methods=['POST'])
def user_info():
    data = request.get_json(force=True)
    user_id = data.get('userId')
    is_admin = data.get('isAdmin')

    # Type checking logic
    if not isinstance(user_id, int):
        return jsonify({'error': 'userId must be an integer'}), 400
    if not isinstance(is_admin, bool):
        return jsonify({'error': 'isAdmin must be a boolean'}), 400

    return jsonify({
        'message': 'Valid types received',
        'userId': user_id,
        'isAdmin': is_admin
    }), 200

@app.route('/api/user/<user_id>', methods=['GET'])
def get_user(user_id):
    if not user_id.isdigit():
        return jsonify({'error': 'User ID must be numeric'}), 400

    return jsonify({
        'message': f'User info for ID {user_id}'
    }), 200

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
