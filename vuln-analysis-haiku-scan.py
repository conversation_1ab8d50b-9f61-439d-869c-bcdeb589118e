import pandas as pd
import json

# File path to your JSON data
json_file_path = 'data.json'
excel_file_path = 'result-vul.xlsx'

try:
    # Read the entire file as a single string
    with open(json_file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    decoder = json.JSONDecoder()
    all_data = []
    while content:
        obj, idx = decoder.raw_decode(content)
        all_data.append(obj)
        content = content[idx:].lstrip()

    # Combine all vulnerabilities into a single list
    all_vulnerabilities = []
    for data in all_data:
        if 'vulns' in data:
            all_vulnerabilities.extend(data['vulns'])
    
    # Convert the combined list of vulnerabilities to a pandas DataFrame
    df = pd.DataFrame(all_vulnerabilities)

    # Write the DataFrame to an Excel file using the OpenPyXL engine
    df.to_excel(excel_file_path, index=False, engine='openpyxl')
    print("Data has been written to", excel_file_path)

except Exception as e:
    print("An error occurred:", e)