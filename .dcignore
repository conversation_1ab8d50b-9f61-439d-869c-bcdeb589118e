# Write glob rules for ignored files.
# Check syntax on https://deepcode.freshdesk.com/support/solutions/articles/60000531055-how-can-i-ignore-files-or-directories-
# Check examples on https://github.com/github/gitignore

# Hidden directories
.*/

# Julia
deps/downloads/
deps/usr/
docs/build/
docs/site/

# CakePHP
/vendor/*
/tmp/cache/models/*
!/tmp/cache/models/empty
/tmp/cache/persistent/*
!/tmp/cache/persistent/empty
/tmp/cache/views/*
!/tmp/cache/views/empty
/tmp/sessions/*
!/tmp/sessions/empty
/tmp/tests/*
!/tmp/tests/empty
/logs/*
!/logs/empty
/app/tmp/*
/vendors/*

# KiCad
*~
_autosave-*
fp-info-cache

# Dart
build/
doc/api/

# PlayFramework
bin/
/db
/lib/
/logs/
/modules
/project/project
/project/target
/target
tmp/
test-result
/dist/

# Zephir
ext/build/
ext/modules/
ext/Makefile*
ext/config*
ext/autom4te*
ext/install-sh
ext/missing
ext/mkinstalldirs
ext/libtool

# RhodesRhomobile
rholog-*
sim-*
bin/libs
bin/RhoBundle
bin/tmp
bin/target

# AppEngine
appengine-generated/

# Textpattern
rpc/
sites/site*/admin/
sites/site*/private/
sites/site*/public/admin/
sites/site*/public/setup/
sites/site*/public/theme/
textpattern/

# ExpressionEngine
images/avatars/
images/captchas/
images/smileys/
images/member_photos/
images/signature_attachments/
images/pm_attachments/
sized/
thumbs/
_thumbs/
*/expressionengine/cache/*

# CMake
CMakeFiles
CMakeScripts
Testing
Makefile
_deps

# Qt
Makefile*
*build-*

# Yeoman
node_modules/
bower_components/
build/
dist/

# ExtJs
build/
ext/

# R
/*.Rcheck/
*_cache/
/cache/
docs/
po/*~

# Python
__pycache__/
build/
develop-eggs/
dist/
downloads/
eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
MANIFEST
htmlcov/
cover/
instance/
docs/_build/
target/
profile_default/
__pypackages__/
celerybeat-schedule
env/
venv/
ENV/
env.bak/
venv.bak/
/site
cython_debug/

# Magento
/media/*
!/media/customer
/media/customer/*
!/media/dhl
/media/dhl/*
!/media/downloadable
/media/downloadable/*
!/media/xmlconnect
/media/xmlconnect/*
!/media/xmlconnect/custom
/media/xmlconnect/custom/*
!/media/xmlconnect/original
/media/xmlconnect/original/*
!/media/xmlconnect/system
/media/xmlconnect/system/*
/var/*
!/var/package
/var/package/*

# CodeIgniter
*/config/development
*/cache/*
application/logs/*
/vendor/

# Haskell
dist
dist-*
cabal-dev

# ArchLinuxPackages
pkg/

# Elm
elm-stuff
repl-temp-*

# Lithium
libraries/*
resources/tmp/*

# Erlang
rel/example_project
deps
_build/
_checkouts/

# ForceDotCom
Referenced Packages

# Plone
bin/
build/
develop-eggs/
downloads/
eggs/
fake-eggs/
parts/
dist/
var/

# AppceleratorTitanium
build/

# ChefCookbook
/cookbooks
bin/*

# Objective-C
xcuserdata/
build/
DerivedData/
Carthage/Build/
fastlane/test_output
iOSInjectionProject/

# Opa
_build
_tracks
opa-debug-js

# Smalltalk
/package-cache
/play-cache
/play-stash
/github-cache

# SeamGen
/bootstrap/data
/bootstrap/tmp
/classes/
/dist/
/exploded-archives/
/test-build/
/test-output/
/test-report/
/target/

# Xilinx
*_synth_*
*/*/bd/*/hdl
*/*/*/bd/*/hdl
*/*/bd/*/ip/*/*/
*/*/*/bd/*/ip/*/*/
hw_handoff
ipshared

# Strapi
Icon
*~
$RECYCLE.BIN/
*#
nbproject
lib-cov
pids
logs
results
build
node_modules
testApp
coverage
cypress/screenshots
cypress/videos
dist
packages/strapi-generate-new/files/public/

# Snap
parts/
prime/
stage/

# Logtalk
lgt_tmp/
logtalk_tester_logs/
logtalk_doclet_logs/

# SPFx
logs
node_modules
dist
lib
solution
temp
coverage
bin
obj

# Vue
docs/_book
test/

# NWjs
locales/
pnacl/

# Cordova
/platforms

# Bazel
/bazel-*

# AltiumDesigner
History
__Previews
Project Logs*
Project Outputs*

# ThinkPHP
/Application/Runtime/

# Jigsaw
build_*

# Bitrix
/bitrix/*
!/bitrix/templates
!/bitrix/components
/bitrix/components/bitrix
!/bitrix/gadgets
/bitrix/gadgets/bitrix
!/bitrix/php_interface/
/upload/

# CodeSniffer
/wpcs/*

# Pimcore
/pimcore
/website/var/assets/*
/website/var/backup/*
/website/var/cache/*
/website/var/classes/Object*
!/website/var/classes/objectbricks
/website/var/config/Geo*
/website/var/config/object/*
/website/var/config/portal/*
/website/var/config/sqlreport/*
/website/var/email/*
/website/var/recyclebin/*
/website/var/search/*
/website/var/system/*
/website/var/tmp/*
/website/var/versions/asset/*
/website/var/versions/document/*
/website/var/versions/object/*
/website/var/user-image/*

# Magento1
/media/*
!/media/customer
/media/customer/*
!/media/dhl
/media/dhl/*
!/media/downloadable
/media/downloadable/*
!/media/xmlconnect
/media/xmlconnect/*
!/media/xmlconnect/custom
/media/xmlconnect/custom/*
!/media/xmlconnect/original
/media/xmlconnect/original/*
!/media/xmlconnect/system
/media/xmlconnect/system/*
/var/*
!/var/package
/var/package/*

# Magento2
/sitemap
/pub/sitemap
/app/config_sandbox
/app/code/Magento/TestModule*
/pub/media/attribute/*
/pub/media/analytics/*
/pub/media/catalog/*
/pub/media/customer/*
/pub/media/downloadable/*
/pub/media/favicon/*
/pub/media/import/*
/pub/media/logo/*
/pub/media/theme/*
/pub/media/theme_customization/*
/pub/media/wysiwyg/*
/pub/media/tmp/*
/pub/media/captcha/*
/pub/static/*
/var/*
/vendor/*
/generated/*

# Drupal7
files/
sites/*/files
sites/*/private
sites/*/translations
/includes
/misc
/modules
/profiles
/scripts
/themes

# InforCMS
[Mm]odel/[Dd]eployment
!Model/Portal/*/SupportFiles/[Bb]in/
!Model/Portal/PortalTemplates/*/SupportFiles/[Bb]in

# Kentico
!CMS/CMSAdminControls/*/
!CMS/CMSModules/System/*/
!CMS/App_Data/CIRepository/**
CMS/App_Data/AzureCache
CMS/App_Data/AzureTemp
CMS/App_Data/CMSTemp
CMS/App_Data/Persistent
CMS/CMSSiteUtils/Export
CMS/CMSSiteUtils/Import
CMS/App_Data/CMSModules/SmartSearch/**
!CMS/App_Data/CMSModules/SmartSearch/*/
!CMS/App_Data/CMSModules/SmartSearch/_StopWords/**
!CMS/App_Data/CMSModules/SmartSearch/_Synonyms/**
CMS/App_Data/DancingGoat
CMS/App_Data/Templates/CommunitySite
CMS/App_Data/Templates/CorporateSite
CMS/App_Data/Templates/DancingGoat
CMS/App_Data/Templates/EcommerceSite
CMS/App_Data/Templates/IntranetPortal
CMS/App_Data/Templates/PersonalSite
CMS/App_Themes/CommunitySite
CMS/App_Themes/CorporateSite
CMS/App_Themes/EcommerceSite
CMS/App_Themes/IntranetPortal*
CMS/App_Themes/PersonalSite
CMS/CMSTemplates/CorporateSite
CMS/CommunitySite
CMS/CorporateSite
CMS/DancingGoat
CMS/EcommerceSite
CMS/IntranetPortal
CMS/PersonalSite

# ROS2
install/
log/
build/
qtcreator-*
*~
COLCON_IGNORE
AMENT_IGNORE

# Splunk
local

# Racket
compiled/

# JupyterNotebooks
profile_default/

# Nikola
cache/
output/

# Red
quick-test/runnable/
system/tests/source/units/auto-tests/
tests/source/units/auto-tests/

# AtmelStudio
[Dd]ebug/
[Rr]elease/

# IAR_EWARM
EWARM/**/Obj
EWARM/**/List
EWARM/**/Exe
EWARM/settings

# esp-idf
build/
sdkconfig

# Phoenix
/tmp
/node_modules
/assets/node_modules
/priv/static/
/installer/_build
/installer/tmp
/installer/doc
/installer/deps

# JBoss4
/server/all/data
/server/all/log
/server/all/tmp
/server/all/work
/server/default/data
/server/default/log
/server/default/tmp
/server/default/work
/server/minimal/data
/server/minimal/log
/server/minimal/tmp
/server/minimal/work

# JBoss6
/server/all/data
/server/all/log
/server/all/tmp
/server/all/work
/server/default/data
/server/default/log
/server/default/tmp
/server/default/work
/server/minimal/data
/server/minimal/log
/server/minimal/tmp
/server/minimal/work
/server/jbossweb-standalone/data
/server/jbossweb-standalone/log
/server/jbossweb-standalone/tmp
/server/jbossweb-standalone/work
/server/standard/data
/server/standard/log
/server/standard/tmp
/server/standard/work

# Hugo
/public/
/resources/_gen/

# Puppet
pkg/*
spec/fixtures/*
coverage/*
vendor/*

# Kotlin
hs_err_pid*

# Composer
/vendor/

# Android
bin/
gen/
out/
build/
proguard/
captures/
freeline/
fastlane/screenshots
fastlane/test_output
lint/intermediates/
lint/generated/
lint/outputs/
lint/tmp/

# Nim
nimcache/
nimblecache/
htmldocs/

# Actionscript
bin-debug/
bin-release/
[Oo]bj/
[Bb]in/

# Maven
target/

# Agda
MAlonzo/**

# Unity
/[Ll]ibrary/
/[Tt]emp/
/[Oo]bj/
/[Bb]uild/
/[Bb]uilds/
/[Ll]ogs/
/[Uu]ser[Ss]ettings/
/[Mm]emoryCaptures/
/[Aa]ssets/Plugins/Editor/JetBrains*
ExportedObj/
/[Aa]ssets/[Ss]treamingAssets/aa/*

# GWT
war/gwt_bree/
gwt-unitCache/
war/WEB-INF/deploy/
war/WEB-INF/classes/
www-test/

# VirtualEnv
[Bb]in
[Ii]nclude
[Ll]ib
[Ll]ib64
[Ll]ocal
[Ss]cripts

# SBT
dist/*
target/
lib_managed/
project/boot/
project/plugins/project/

# PSoCCreator
Debug/
Release/
Export/
*/codegentemp
*/Generated_Source

# TextMate
tmtags

# MonoDevelop
test-results/

# SublimeText
Package Control.cache/
Package Control.ca-certs/

# Dreamweaver
_notes
_compareTemp
configs/

# NetBeans
**/nbproject/private/
build/
nbbuild/
dist/
nbdist/

# Windows
$RECYCLE.BIN/

# MATLAB
helpsearch*/
slprj/
sccprj/
codegen/
octave-workspace

# Octave
helpsearch*/
slprj/
sccprj/
codegen/
octave-workspace

# FlexBuilder
bin/
bin-debug/
bin-release/

# Xcode
xcuserdata/
build/
DerivedData/

# Lazarus
backup/
lib/
*.app/

# CVS
/CVS/*
**/CVS/*

# Eclipse
bin/
tmp/

# Momentics
x86/
arm/
arm-p/

# Linux
*~

# Virtuoso
lvsRunDir/*
drcRunDir/*

# Emacs
*~
#*#
auto-save-list
tramp
*_archive
/eshell/history
/eshell/lastdir
/elpa/
/auto/
dist/
/server/

# EiffelStudio
EIFGENs

# macOS
Icon

Network Trash Folder
Temporary Items

# XilinxISE
iseconfig/
xlnx_auto_0_xdb/
xst/
_ngo/
_xmsgs/

# CodeKit
/min

# SynopsysVCS
simv
simv.daidir/
simv.db.dir/
simv.vdb/
urgReport/
DVEfiles/

# Tags
TAGS
!TAGS/
tags
!tags/
GTAGS
GRTAGS
GPATH
GSYMS

# ModelSim
[_@]*
wlf*
cov*/
transcript*

# JetBrains
cmake-build-*/
out/

# WebMethods
**/IntegrationServer/datastore/
**/IntegrationServer/db/
**/IntegrationServer/DocumentStore/
**/IntegrationServer/lib/
**/IntegrationServer/logs/
**/IntegrationServer/replicate/
**/IntegrationServer/sdk/
**/IntegrationServer/support/
**/IntegrationServer/update/
**/IntegrationServer/userFtpRoot/
**/IntegrationServer/web/
**/IntegrationServer/WmRepository4/
**/IntegrationServer/XAStore/
**/IntegrationServer/packages/Wm*/

# Vim
*~
tags

# Calabash
rerun/
reports/
screenshots/
test-servers/
vendor

# JDeveloper
temp/
classes/
deploy/
javadoc/

# Drupal
/sites/*/files
/sites/*/public
/sites/*/private
/sites/*/files-public
/sites/*/files-private
/sites/*/translations
/sites/*/tmp
/sites/*/cache
/sites/simpletest
/core
/vendor

# UnrealEngine
Binaries/*
Plugins/*/Binaries/*
Build/*
!Build/*/
Build/*/**
Saved/*
Intermediate/*
Plugins/*/Intermediate/*
DerivedDataCache/*

# Symfony
/app/cache/*
/app/logs/*
/app/spool/*
/var/cache/*
/var/logs/*
/var/sessions/*
/var/log/*
/bin/*
!bin/console
!bin/symfony_requirements
/vendor/
/web/bundles/
/web/uploads/
/build/
**/Entity/*~

# JBoss
jboss/server/all/tmp/**/*
jboss/server/all/data/**/*
jboss/server/all/work/**/*
jboss/server/default/tmp/**/*
jboss/server/default/data/**/*
jboss/server/default/work/**/*
jboss/server/minimal/tmp/**/*
jboss/server/minimal/data/**/*
jboss/server/minimal/work/**/*

# SugarCRM
/cache/*
/custom/history/
/custom/modulebuilder/
/custom/working/
/custom/modules/*/Ext/
/custom/application/Ext/
/upload/*
/upload_backup/

# Leiningen
/lib/
/classes/
/target/
/checkouts/

# OpenCart
download/
image/data/
image/cache/
system/cache/
system/logs/
system/storage/
vqmod/logs/*
vqmod/vqcache/*

# VVVV
bin/

# Gradle
**/build/

# FuelPHP
/fuel/vendor
/docs/
/fuel/app/logs/*/*/*
/fuel/app/cache/*/*

# Autotools
/ar-lib
/mdate-sh
/py-compile
/test-driver
/ylwrap
/compile
/configure
/depcomp
/install-sh
/missing
/stamp-h1
Makefile

# Delphi
__history/
__recovery/
modules/

# GitBook
node_modules
_book

# Prestashop
/cache/*
!/cache/push/activity
!/cache/push/trends
/download/*
/img/*
!/img/jquery-ui
!/img/scenes
/upload/*
/vendor/*
/docs/phpdoc-sf/
/admin-dev/autoupgrade/*
/admin-dev/backups/*
/admin-dev/import/*
/admin-dev/export/*
themes/*/cache/*
config/xml/*
config/themes/*
modules/*
override/*
themes/*/
!themes/classic
!themes/_core
!themes/_libraries
bower_components/
node_modules/
php-cs-fixer
translations/*
mails/*
!mails/themes/
!mails/_partials/
themes/default-bootstrap/lang/*
themes/default-bootstrap/mails/*
!themes/default-bootstrap/mails/en/
themes/default-bootstrap/modules/*/mails/*
!themes/default-bootstrap/modules/*/mails/en
/bin/
/app/Resources/translations/*
!/app/Resources/translations/default
/build/
/var/*
!/var/cache
/var/cache/*
!/var/logs
/var/logs/*
!/var/sessions
/var/sessions/*
/vendor/
/web/bundles/

# Xojo
Builds*
Debug*/Debug* Libs

# WordPress
!wp-content/
wp-content/*
!wp-content/mu-plugins/
!wp-content/plugins/
!wp-content/themes/
wp-content/themes/twenty*/
node_modules/

# ROS
devel/
logs/
build/
bin/
lib/
msg_gen/
srv_gen/
build_isolated/
devel_isolated/
/cfg/cpp/
qtcreator-*
/planning/cfg
/planning/docs
*~
CATKIN_IGNORE

# Scala
hs_err_pid*

# VisualStudio
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/
Generated Files/
[Tt]est[Rr]esult*/
[Dd]ebugPS/
[Rr]eleasePS/
BenchmarkDotNet.Artifacts/
artifacts/
_Chutzpah*
ipch/
$tf/
_ReSharper*/
_TeamCity*
_NCrunch_*
nCrunchTemp_*
AutoTest.Net/
[Ee]xpress/
DocProject/buildhelp/
DocProject/Help/Html2
DocProject/Help/html
publish/
PublishScripts/
**/[Pp]ackages/*
!**/[Pp]ackages/build/
csx/
ecf/
rcf/
AppPackages/
BundleArtifacts/
!?*.[Cc]ache/
ClientBin/
~$*
*~
Generated_Code/
_UpgradeReport_Files/
Backup*/
ServiceFabricBackup/
FakesAssemblies/
node_modules/
**/*.HTMLClient/GeneratedArtifacts
**/*.DesktopClient/GeneratedArtifacts
**/*.Server/GeneratedArtifacts
_Pvt_Extensions
paket-files/
__pycache__/
OpenCover/
ASALocalRun/
healthchecksdb
MigrationBackup/

# Qooxdoo
cache
cache-downloads
inspector
api

# Concrete5
error_log
files/cache/*
files/tmp/*
/application/files/*
/updates/*

# Grails
/web-app/WEB-INF/classes
/test/reports
/logs
/plugins
/web-app/plugins
/target

# Stella
obj/

# Processing
applet
out

# Rails
/public/system
/coverage/
/spec/tmp
/log/*
/tmp/*
/vendor/bundle
/vendor/assets/bower_components
node_modules/
/public/packs
/public/packs-test
/public/assets
/storage/*
/public/uploads

# PureScript
bower_components
node_modules
output

# Laravel
/vendor/
node_modules/
app/storage/
public/storage
public/hot
public_html/storage
public_html/hot

# TwinCAT3
_Boot/
_CompileInfo/
_Libraries/

# Elixir
/_build
/cover
/deps
/doc

# TurboGears2
data/*
dist
build

# Godot
data_*/

# Java
hs_err_pid*

# Swift
xcuserdata/
build/
DerivedData/
Carthage/Build/
Dependencies/
fastlane/test_output
iOSInjectionProject/

# Lilypond
*~

# Joomla
/administrator/cache/*
/administrator/components/com_actionlogs/*
/administrator/components/com_admin/*
/administrator/components/com_ajax/*
/administrator/components/com_associations/*
/administrator/components/com_banners/*
/administrator/components/com_cache/*
/administrator/components/com_categories/*
/administrator/components/com_checkin/*
/administrator/components/com_config/*
/administrator/components/com_contact/*
/administrator/components/com_content/*
/administrator/components/com_contenthistory/*
/administrator/components/com_cpanel/*
/administrator/components/com_fields/*
/administrator/components/com_finder/*
/administrator/components/com_installer/*
/administrator/components/com_joomlaupdate/*
/administrator/components/com_languages/*
/administrator/components/com_login/*
/administrator/components/com_media/*
/administrator/components/com_menus/*
/administrator/components/com_messages/*
/administrator/components/com_modules/*
/administrator/components/com_newsfeeds/*
/administrator/components/com_plugins/*
/administrator/components/com_postinstall/*
/administrator/components/com_privacy/*
/administrator/components/com_redirect/*
/administrator/components/com_search/*
/administrator/components/com_tags/*
/administrator/components/com_templates/*
/administrator/components/com_users/*
/administrator/help/*
/administrator/includes/*
/administrator/language/overrides/*
/administrator/logs/*
/administrator/modules/mod_custom/*
/administrator/modules/mod_feed/*
/administrator/modules/mod_latest/*
/administrator/modules/mod_latestactions/*
/administrator/modules/mod_logged/*
/administrator/modules/mod_login/*
/administrator/modules/mod_menu/*
/administrator/modules/mod_multilangstatus/*
/administrator/modules/mod_online/*
/administrator/modules/mod_popular/*
/administrator/modules/mod_privacy_dashboard/*
/administrator/modules/mod_quickicon/*
/administrator/modules/mod_sampledata/*
/administrator/modules/mod_stats_admin/*
/administrator/modules/mod_status/*
/administrator/modules/mod_submenu/*
/administrator/modules/mod_title/*
/administrator/modules/mod_toolbar/*
/administrator/modules/mod_unread/*
/administrator/modules/mod_version/*
/administrator/templates/hathor/*
/administrator/templates/isis/*
/administrator/templates/system/*
/bin/*
/cache/*
/cli/*
/components/com_ajax/*
/components/com_banners/*
/components/com_config/*
/components/com_contact/*
/components/com_content/*
/components/com_contenthistory/*
/components/com_fields/*
/components/com_finder/*
/components/com_mailto/*
/components/com_media/*
/components/com_menus/*
/components/com_modules/*
/components/com_newsfeeds/*
/components/com_privacy/*
/components/com_search/*
/components/com_tags/*
/components/com_users/*
/components/com_wrapper/*
/images/banners/*
/images/headers/*
/images/sampledata/*
/images/joomla*
/includes/*
/installation/*
/language/overrides/*
/layouts/joomla/*
/layouts/libraries/*
/layouts/plugins/*
/libraries/cms/*
/libraries/fof/*
/libraries/idna_convert/*
/libraries/joomla/*
/libraries/legacy/*
/libraries/php-encryption/*
/libraries/phpass/*
/libraries/phpmailer/*
/libraries/phputf8/*
/libraries/simplepie/*
/libraries/vendor/*
/media/cms/*
/media/com_associations/*
/media/com_contact/*
/media/com_content/*
/media/com_contenthistory/*
/media/com_fields/*
/media/com_finder/*
/media/com_joomlaupdate/*
/media/com_menus/*
/media/com_modules/*
/media/com_wrapper/*
/media/contacts/*
/media/editors/*
/media/jui/*
/media/mailto/*
/media/media/*
/media/mod_languages/*
/media/mod_sampledata/*
/media/overrider/*
/media/plg_captcha_recaptcha/*
/media/plg_captcha_recaptcha_invisible/*
/media/plg_quickicon_extensionupdate/*
/media/plg_quickicon_joomlaupdate/*
/media/plg_quickicon_privacycheck/*
/media/plg_system_highlight/*
/media/plg_system_stats/*
/media/plg_twofactorauth_totp/*
/media/system/*
/modules/mod_articles_archive/*
/modules/mod_articles_categories/*
/modules/mod_articles_category/*
/modules/mod_articles_latest/*
/modules/mod_articles_news/*
/modules/mod_articles_popular/*
/modules/mod_banners/*
/modules/mod_breadcrumbs/*
/modules/mod_custom/*
/modules/mod_feed/*
/modules/mod_finder/*
/modules/mod_footer/*
/modules/mod_languages/*
/modules/mod_login/*
/modules/mod_menu/*
/modules/mod_random_image/*
/modules/mod_related_items/*
/modules/mod_search/*
/modules/mod_stats/*
/modules/mod_syndicate/*
/modules/mod_tags_popular/*
/modules/mod_tags_similar/*
/modules/mod_users_latest/*
/modules/mod_whosonline/*
/modules/mod_wrapper/*
/plugins/actionlog/joomla/*
/plugins/authentication/cookie/*
/plugins/authentication/example/*
/plugins/authentication/gmail/*
/plugins/authentication/joomla/*
/plugins/authentication/ldap/*
/plugins/captcha/recaptcha/*
/plugins/captcha/recaptcha_invisible/*
/plugins/content/confirmconsent/*
/plugins/content/contact/*
/plugins/content/emailcloak/*
/plugins/content/example/*
/plugins/content/fields/*
/plugins/content/finder/*
/plugins/content/geshi/*
/plugins/content/joomla/*
/plugins/content/loadmodule/*
/plugins/content/pagebreak/*
/plugins/content/pagenavigation/*
/plugins/content/vote/*
/plugins/editors/codemirror/*
/plugins/editors/none/*
/plugins/editors/tinymce/*
/plugins/editors-xtd/article/*
/plugins/editors-xtd/contact/*
/plugins/editors-xtd/fields/*
/plugins/editors-xtd/image/*
/plugins/editors-xtd/menu/*
/plugins/editors-xtd/module/*
/plugins/editors-xtd/pagebreak/*
/plugins/editors-xtd/readmore/*
/plugins/extension/example/*
/plugins/extension/joomla/*
/plugins/fields/calendar/*
/plugins/fields/checkboxes/*
/plugins/fields/color/*
/plugins/fields/editor/*
/plugins/fields/imagelist/*
/plugins/fields/integer/*
/plugins/fields/list/*
/plugins/fields/media/*
/plugins/fields/radio/*
/plugins/fields/repeatable/*
/plugins/fields/sql/*
/plugins/fields/text/*
/plugins/fields/textarea/*
/plugins/fields/url/*
/plugins/fields/user/*
/plugins/fields/usergrouplist/*
/plugins/finder/categories/*
/plugins/finder/contacts/*
/plugins/finder/content/*
/plugins/finder/newsfeeds/*
/plugins/finder/tags/*
/plugins/installer/folderinstaller/*
/plugins/installer/packageinstaller/*
/plugins/installer/urlinstaller/*
/plugins/privacy/actionlogs/*
/plugins/privacy/consents/*
/plugins/privacy/contact/*
/plugins/privacy/content/*
/plugins/privacy/message/*
/plugins/privacy/user/*
/plugins/quickicon/extensionupdate/*
/plugins/quickicon/joomlaupdate/*
/plugins/quickicon/phpversioncheck/*
/plugins/quickicon/privacycheck/*
/plugins/sampledata/blog/*
/plugins/search/categories/*
/plugins/search/contacts/*
/plugins/search/content/*
/plugins/search/newsfeeds/*
/plugins/search/tags/*
/plugins/search/weblinks/*
/plugins/system/actionlogs/*
/plugins/system/cache/*
/plugins/system/debug/*
/plugins/system/fields/*
/plugins/system/highlight/*
/plugins/system/languagecode/*
/plugins/system/languagefilter/*
/plugins/system/log/*
/plugins/system/logout/*
/plugins/system/logrotation/*
/plugins/system/p3p/*
/plugins/system/privacyconsent/*
/plugins/system/redirect/*
/plugins/system/remember/*
/plugins/system/sef/*
/plugins/system/sessiongc/*
/plugins/system/stats/*
/plugins/system/updatenotification/*
/plugins/twofactorauth/totp/*
/plugins/twofactorauth/yubikey/*
/plugins/user/contactcreator/*
/plugins/user/example/*
/plugins/user/joomla/*
/plugins/user/profile/*
/plugins/user/terms/*
/templates/beez3/*
/templates/protostar/*
/templates/system/*
/tmp/*

# SymphonyCMS
manifest/cache/
manifest/logs/
manifest/tmp/
symphony/
workspace/uploads/

# ZendFramework
vendor/
data/logs/
data/cache/
data/sessions/
data/tmp/
temp/
data/DoctrineORMModule/Proxy/
data/DoctrineORMModule/cache/
demos/
extras/documentation

# C
*.dSYM/

# Node
logs
pids
lib-cov
coverage
bower_components
build/Release
node_modules/
jspm_packages/
web_modules/
out
dist

# LemonStand
/config/*
/controllers/*
/init/*
/logs/*
/phproad/*
/temp/*
/uploaded/*
/installer_files/*
/modules/backend/*
/modules/blog/*
/modules/cms/*
/modules/core/*
/modules/session/*
/modules/shop/*
/modules/system/*
/modules/users/*

# Waf
waf-*-*/
waf3-*-*/

# TeX
latex.out/
*-gnuplottex-*
*-tikzDictionary
_minted*
sympy-plots-for-*.tex/
pythontex-files-*/
TSWLatexianTemp*
*~[0-9]*

# Rust
debug/
target/

# Yii
assets/*
protected/runtime/*
themes/classic/views/

# Mercury
Mercury/

# Scrivener
*/QuickLook/

# Clojure
/lib/
/classes/
/target/
/checkouts/

# Phalcon
/cache/
/config/development/

# Typo3
/fileadmin/user_upload/
/fileadmin/_temp_/
/fileadmin/_processed_/
/uploads/
/typo3conf/temp_CACHED*
/typo3conf/ENABLE_INSTALL_TOOL
/FIRST_INSTALL
/typo3
/Packages
/typo3temp/

# JENKINS_HOME
!/jobs
jobs/**
!jobs/**/
builds
indexing
jobs/**/*workspace

# MetaProgrammingSystem
classes_gen
source_gen
test_gen

# CraftCMS
/craft/storage/*
!/craft/storage/rebrand

# CFWheels
plugins/**/*
files
db/sql
javascripts/bundles
stylesheets/bundles

# OCaml
_build/
_opam/

# Ruby
/coverage/
/InstalledFiles
/pkg/
/spec/reports/
/test/tmp/
/test/version_tmp/
/tmp/
build/
build-iPhoneOS/
build-iPhoneSimulator/
/_yardoc/
/doc/
/rdoc/
/vendor/bundle
/lib/bundler/man/

# Perl
!Build/
cover_db/
_build/
Build
inc/
/blib/
/_eumm/
/Makefile
/pm_to_blib

# Elisp
*~

# Jekyll
_site/

# D
docs/

# Packer
packer_cache/

# Umbraco
**/App_Data/Logs/
**/App_Data/[Pp]review/
**/App_Data/TEMP/
**/App_Data/NuGetBackup/
!**/App_Data/[Pp]ackages/*
!**/[Uu]mbraco/[Dd]eveloper/[Pp]ackages/*
!**/[Uu]mbraco/[Vv]iews/[Pp]ackages/*
**/App_Data/cache/

# Kohana
application/cache/*
application/logs/*

# Nanoc
output/
tmp/nanoc/
