from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload

def generate_random_data():
    """ Generate random title, description, tags, and category for the video """
    # Simple examples for generating random data, can be improved with more sophisticated logic
    title = f"Awesome Short Video {random.randint(1, 1000)}"
    description = "This is a randomly generated description for a YouTube Short."
    tags = ['video', 'fun', 'short', 'random', 'youtube']
    category_id = '22'  # Choosing '22' for People & Blogs, for example
    return title, description, tags, category_id
def upload_youtube_short(video_path):
    """Upload video as YouTube Short"""
    scopes = ["https://www.googleapis.com/auth/youtube.upload"]
    flow = InstalledAppFlow.from_client_secrets_file("video.json", scopes)
    
    # Change here: use run_local_server instead of run_console
    credentials = flow.run_local_server(port=0)
    
    youtube = build('youtube', 'v3', credentials=credentials)

    title, description, tags, category_id = generate_random_data()

    body = {
        'snippet': {
            'title': title,
            'description': description,
            'tags': tags,
            'categoryId': category_id
        },
        'status': {
            'privacyStatus': 'public'
        }
    }

    media = MediaFileUpload(video_path, chunksize=-1, resumable=True)
    request = youtube.videos().insert(
        part=",".join(body.keys()),
        body=body,
        media_body=media
    )

    response = request.execute()
    print(f'Video ID: {response["id"]} uploaded as a Short successfully')

def main():
    video_path = r"D:\RD\code-ET\data\1.mp4"
    upload_youtube_short(video_path)

if __name__ == "__main__":
    main()
