import json
import pandas as pd

#//https://nvd.nist.gov/vuln/data-feeds**//
with open(r'D:\RD\Jun2023\nvdcve-1.1-2023.json', encoding="utf8") as f:
#with open('test.json', encoding="utf8") as f:
    data = json.load(f)
    

#print(data["CVE_Items"])
list_cve =[]
data1= (data["CVE_Items"])   # list
temp = []
for i in data1:
    data2 = i["cve"]
    refre= str(data2["references"]["reference_data"])
 
    if "'Exploit'" in refre:
        #print(refre)
        #print(data2['CVE_data_meta']['ID'] + "=Yes" )
        temp.append(data2['CVE_data_meta']['ID'])
        #print(temp)
        

clmn_cve= 'CVE'
clmn_poc='POC'

#excel_file = pd.ExcelFile("result-oct.xlsx")
#df_exel = pd.read_excel(excel_file, "IndusVulnList_2022")
df_exel = pd.read_excel("D:\RD\july2023\et.xlsx")

cv_id_list = df_exel["CVE"].tolist()#adding into  list
print(len(cv_id_list))
POC_list = df_exel["POC"].tolist()#adding into  list
print(len(POC_list))

for i,cv_id in enumerate(cv_id_list):
	
        cv_id = cv_id.strip()
        if cv_id in temp:
                POC_list[i]="Yes"
                print("found", cv_id)

df_exel["POC"] = POC_list
df_exel.to_excel(r'D:\RD\2024\april\test.xlsx')	

print("December POC result")




"""#print(type(refre))


""""""data3= data2['CVE_data_meta']['ID']
print(data3)


def get_cve():
data1= (data["CVE_Items"])
for i in data1:
data2 = i
data3= data2['cve']['CVE_data_meta']['ID']
print(data3)
"""