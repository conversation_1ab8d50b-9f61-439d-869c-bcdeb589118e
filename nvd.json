{"resultsPerPage": 15, "startIndex": 0, "totalResults": 15, "format": "NVD_CVE", "version": "2.0", "timestamp": "2023-03-08T17:22:01.377", "vulnerabilities": [{"cve": {"id": "CVE-2022-27596", "sourceIdentifier": "<EMAIL>", "published": "2023-01-30T02:15:08.463", "lastModified": "2023-02-17T21:28:50.930", "vulnStatus": "Analyzed", "descriptions": [{"lang": "en", "value": "A vulnerability has been reported to affect QNAP device running QuTS hero, QTS. If exploited, this vulnerability allows remote attackers to inject malicious code. We have already fixed this vulnerability in the following versions of QuTS hero, QTS: QuTS hero h5.0.1.2248 build 20221215 and later QTS 5.0.1.2234 build 20221201 and later"}], "metrics": {"cvssMetricV31": [{"source": "<EMAIL>", "type": "Primary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "attackVector": "NETWORK", "attackComplexity": "LOW", "privilegesRequired": "NONE", "userInteraction": "NONE", "scope": "UNCHANGED", "confidentialityImpact": "HIGH", "integrityImpact": "HIGH", "availabilityImpact": "HIGH", "baseScore": 9.8, "baseSeverity": "CRITICAL"}, "exploitabilityScore": 3.9, "impactScore": 5.9}, {"source": "<EMAIL>", "type": "Secondary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "attackVector": "NETWORK", "attackComplexity": "LOW", "privilegesRequired": "NONE", "userInteraction": "NONE", "scope": "UNCHANGED", "confidentialityImpact": "HIGH", "integrityImpact": "HIGH", "availabilityImpact": "HIGH", "baseScore": 9.8, "baseSeverity": "CRITICAL"}, "exploitabilityScore": 3.9, "impactScore": 5.9}]}, "weaknesses": [{"source": "<EMAIL>", "type": "Primary", "description": [{"lang": "en", "value": "CWE-89"}]}, {"source": "<EMAIL>", "type": "Secondary", "description": [{"lang": "en", "value": "CWE-89"}]}], "configurations": [{"nodes": [{"operator": "OR", "negate": false, "cpeMatch": [{"vulnerable": true, "criteria": "cpe:2.3:o:qnap:qts:*:*:*:*:*:*:*:*", "versionStartIncluding": "5.0.1", "versionEndExcluding": "5.0.1.2234", "matchCriteriaId": "EDB678B3-A51E-4F60-B049-FED59A368B9B"}, {"vulnerable": true, "criteria": "cpe:2.3:o:qnap:quts_hero:*:*:*:*:*:*:*:*", "versionStartIncluding": "h5.0.1", "versionEndExcluding": "h5.0.1.2248", "matchCriteriaId": "0B1CD08D-792B-45D9-8CCA-5222EE75A870"}]}]}], "references": [{"url": "https://www.qnap.com/en/security-advisory/qsa-23-01", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}}, {"cve": {"id": "CVE-2023-24612", "sourceIdentifier": "<EMAIL>", "published": "2023-01-30T03:15:09.557", "lastModified": "2023-02-07T19:22:09.070", "vulnStatus": "Analyzed", "descriptions": [{"lang": "en", "value": "The PdfBook extension through 2.0.5 before b07b6a64 for MediaWiki allows command injection via an option."}], "metrics": {"cvssMetricV31": [{"source": "<EMAIL>", "type": "Primary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", "attackVector": "NETWORK", "attackComplexity": "LOW", "privilegesRequired": "NONE", "userInteraction": "NONE", "scope": "UNCHANGED", "confidentialityImpact": "HIGH", "integrityImpact": "HIGH", "availabilityImpact": "HIGH", "baseScore": 9.8, "baseSeverity": "CRITICAL"}, "exploitabilityScore": 3.9, "impactScore": 5.9}]}, "weaknesses": [{"source": "<EMAIL>", "type": "Primary", "description": [{"lang": "en", "value": "CWE-77"}]}], "configurations": [{"nodes": [{"operator": "OR", "negate": false, "cpeMatch": [{"vulnerable": true, "criteria": "cpe:2.3:a:pdfbook_project:pdfbook:*:*:*:*:*:mediawiki:*:*", "versionEndIncluding": "2.0.5", "matchCriteriaId": "345E9C6F-4838-487F-99A2-DA98A79330ED"}]}]}], "references": [{"url": "https://gitlab.com/organicdesign/PdfBook/-/merge_requests/6", "source": "<EMAIL>", "tags": ["Patch", "Third Party Advisory"]}]}}, {"cve": {"id": "CVE-2022-48303", "sourceIdentifier": "<EMAIL>", "published": "2023-01-30T04:15:08.030", "lastModified": "2023-02-07T19:16:29.570", "vulnStatus": "Analyzed", "descriptions": [{"lang": "en", "value": "GNU Tar through 1.34 has a one-byte out-of-bounds read that results in use of uninitialized memory for a conditional jump. Exploitation to change the flow of control has not been demonstrated. The issue occurs in from_header in list.c via a V7 archive in which mtime has approximately 11 whitespace characters."}], "metrics": {"cvssMetricV31": [{"source": "<EMAIL>", "type": "Primary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H", "attackVector": "LOCAL", "attackComplexity": "LOW", "privilegesRequired": "NONE", "userInteraction": "REQUIRED", "scope": "UNCHANGED", "confidentialityImpact": "HIGH", "integrityImpact": "HIGH", "availabilityImpact": "HIGH", "baseScore": 7.8, "baseSeverity": "HIGH"}, "exploitabilityScore": 1.8, "impactScore": 5.9}]}, "weaknesses": [{"source": "<EMAIL>", "type": "Primary", "description": [{"lang": "en", "value": "CWE-125"}]}], "configurations": [{"nodes": [{"operator": "OR", "negate": false, "cpeMatch": [{"vulnerable": true, "criteria": "cpe:2.3:a:gnu:tar:*:*:*:*:*:*:*:*", "versionEndIncluding": "1.34", "matchCriteriaId": "07E94814-967C-4B81-9137-6DA8E51F81D2"}]}]}], "references": [{"url": "https://savannah.gnu.org/bugs/?62387", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}, {"url": "https://savannah.gnu.org/patch/?10307", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}]}}, {"cve": {"id": "CVE-2022-25936", "sourceIdentifier": "<EMAIL>", "published": "2023-01-30T05:15:10.057", "lastModified": "2023-02-07T19:14:20.857", "vulnStatus": "Analyzed", "descriptions": [{"lang": "en", "value": "Versions of the package servst before 2.0.3 are vulnerable to Directory Traversal due to improper sanitization of the filePath variable."}], "metrics": {"cvssMetricV31": [{"source": "<EMAIL>", "type": "Primary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N", "attackVector": "NETWORK", "attackComplexity": "LOW", "privilegesRequired": "NONE", "userInteraction": "NONE", "scope": "UNCHANGED", "confidentialityImpact": "HIGH", "integrityImpact": "NONE", "availabilityImpact": "NONE", "baseScore": 7.5, "baseSeverity": "HIGH"}, "exploitabilityScore": 3.9, "impactScore": 3.6}, {"source": "<EMAIL>", "type": "Secondary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N", "attackVector": "NETWORK", "attackComplexity": "LOW", "privilegesRequired": "NONE", "userInteraction": "NONE", "scope": "UNCHANGED", "confidentialityImpact": "HIGH", "integrityImpact": "NONE", "availabilityImpact": "NONE", "baseScore": 7.5, "baseSeverity": "HIGH"}, "exploitabilityScore": 3.9, "impactScore": 3.6}]}, "weaknesses": [{"source": "<EMAIL>", "type": "Primary", "description": [{"lang": "en", "value": "CWE-22"}]}], "configurations": [{"nodes": [{"operator": "OR", "negate": false, "cpeMatch": [{"vulnerable": true, "criteria": "cpe:2.3:a:servst_project:servst:*:*:*:*:*:node.js:*:*", "versionEndExcluding": "2.0.3", "matchCriteriaId": "349C05F8-CBA6-4ACA-A98C-7A58D69C8DDC"}]}]}], "references": [{"url": "https://gist.github.com/lirantal/691d02d607753d54856f9335f9a1692f", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/andrepolischuk/servst/commit/f7cae5d2d7c64c86bc512e1e50614240396ef114", "source": "<EMAIL>", "tags": ["Patch", "Third Party Advisory"]}, {"url": "https://security.snyk.io/vuln/SNYK-JS-SERVST-3244896", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}}, {"cve": {"id": "CVE-2022-25967", "sourceIdentifier": "<EMAIL>", "published": "2023-01-30T05:15:10.177", "lastModified": "2023-02-07T19:13:42.123", "vulnStatus": "Analyzed", "descriptions": [{"lang": "en", "value": "Versions of the package eta before 2.0.0 are vulnerable to Remote Code Execution (RCE) by overwriting template engine configuration variables with view options received from The Express render API. **Note:** This is exploitable only for users who are rendering templates with user-defined data."}], "metrics": {"cvssMetricV31": [{"source": "<EMAIL>", "type": "Primary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H", "attackVector": "NETWORK", "attackComplexity": "LOW", "privilegesRequired": "LOW", "userInteraction": "NONE", "scope": "UNCHANGED", "confidentialityImpact": "HIGH", "integrityImpact": "HIGH", "availabilityImpact": "HIGH", "baseScore": 8.8, "baseSeverity": "HIGH"}, "exploitabilityScore": 2.8, "impactScore": 5.9}, {"source": "<EMAIL>", "type": "Secondary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:H/A:H", "attackVector": "NETWORK", "attackComplexity": "HIGH", "privilegesRequired": "NONE", "userInteraction": "NONE", "scope": "UNCHANGED", "confidentialityImpact": "HIGH", "integrityImpact": "HIGH", "availabilityImpact": "HIGH", "baseScore": 8.1, "baseSeverity": "HIGH"}, "exploitabilityScore": 2.2, "impactScore": 5.9}]}, "weaknesses": [{"source": "<EMAIL>", "type": "Primary", "description": [{"lang": "en", "value": "NVD-CWE-noinfo"}]}], "configurations": [{"nodes": [{"operator": "OR", "negate": false, "cpeMatch": [{"vulnerable": true, "criteria": "cpe:2.3:a:eta.js:eta:*:*:*:*:*:node.js:*:*", "versionEndExcluding": "2.0.0", "matchCriteriaId": "1E6958D1-1731-4523-BE7B-F4BBED80549B"}]}]}], "references": [{"url": "https://github.com/eta-dev/eta/blob/9c8e4263d3a559444a3881a85c1607bf344d0b28/src/compile-string.ts%23L21", "source": "<EMAIL>", "tags": ["Broken Link"]}, {"url": "https://github.com/eta-dev/eta/blob/9c8e4263d3a559444a3881a85c1607bf344d0b28/src/file-handlers.ts%23L182", "source": "<EMAIL>", "tags": ["Broken Link"]}, {"url": "https://github.com/eta-dev/eta/commit/5651392462ee0ff19d77c8481081a99e5b9138dd", "source": "<EMAIL>", "tags": ["Patch", "Third Party Advisory"]}, {"url": "https://security.snyk.io/vuln/SNYK-JS-ETA-2936803", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}}, {"cve": {"id": "CVE-2023-24622", "sourceIdentifier": "<EMAIL>", "published": "2023-01-30T05:15:10.307", "lastModified": "2023-02-07T19:10:35.633", "vulnStatus": "Analyzed", "descriptions": [{"lang": "en", "value": "isInList in the safeurl-python package before 1.2 for Python has an insufficiently restrictive regular expression for external domains, leading to SSRF."}], "metrics": {"cvssMetricV31": [{"source": "<EMAIL>", "type": "Primary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N", "attackVector": "NETWORK", "attackComplexity": "LOW", "privilegesRequired": "NONE", "userInteraction": "NONE", "scope": "UNCHANGED", "confidentialityImpact": "NONE", "integrityImpact": "LOW", "availabilityImpact": "NONE", "baseScore": 5.3, "baseSeverity": "MEDIUM"}, "exploitabilityScore": 3.9, "impactScore": 1.4}]}, "weaknesses": [{"source": "<EMAIL>", "type": "Primary", "description": [{"lang": "en", "value": "CWE-918"}]}], "configurations": [{"nodes": [{"operator": "OR", "negate": false, "cpeMatch": [{"vulnerable": true, "criteria": "cpe:2.3:a:includesecurity:safeurl-python:1.0:*:*:*:*:*:*:*", "matchCriteriaId": "702E64C2-193E-47C1-96C0-B7E2CBFDF5E4"}]}]}], "references": [{"url": "https://github.com/IncludeSecurity/safeurl-python/security/advisories/GHSA-jgh8-vchw-q3g7", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}}, {"cve": {"id": "CVE-2023-24623", "sourceIdentifier": "<EMAIL>", "published": "2023-01-30T05:15:10.373", "lastModified": "2023-02-07T15:04:20.140", "vulnStatus": "Analyzed", "descriptions": [{"lang": "en", "value": "Paranoidhttp before 0.3.0 allows SSRF because [::] is equivalent to the 127.0.0.1 address, but does not match the filter for private addresses."}], "metrics": {"cvssMetricV31": [{"source": "<EMAIL>", "type": "Primary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N", "attackVector": "NETWORK", "attackComplexity": "LOW", "privilegesRequired": "NONE", "userInteraction": "NONE", "scope": "UNCHANGED", "confidentialityImpact": "NONE", "integrityImpact": "HIGH", "availabilityImpact": "NONE", "baseScore": 7.5, "baseSeverity": "HIGH"}, "exploitabilityScore": 3.9, "impactScore": 3.6}]}, "weaknesses": [{"source": "<EMAIL>", "type": "Primary", "description": [{"lang": "en", "value": "CWE-918"}]}], "configurations": [{"nodes": [{"operator": "OR", "negate": false, "cpeMatch": [{"vulnerable": true, "criteria": "cpe:2.3:a:paranoidhttp_project:paranoidhttp:*:*:*:*:*:*:*:*", "versionEndExcluding": "0.3.0", "matchCriteriaId": "F349DBA3-D1CA-41D5-8065-B6519D42D094"}]}]}], "references": [{"url": "https://github.com/hakobe/paranoidhttp/blob/master/CHANGELOG.md#v030-2023-01-19", "source": "<EMAIL>", "tags": ["Release Notes", "Third Party Advisory"]}, {"url": "https://github.com/hakobe/paranoidhttp/commit/07f671da14ce63a80f4e52432b32e8d178d75fd3", "source": "<EMAIL>", "tags": ["Patch", "Third Party Advisory"]}, {"url": "https://github.com/hakobe/paranoidhttp/compare/v0.2.0...v0.3.0", "source": "<EMAIL>", "tags": ["Patch", "Third Party Advisory"]}]}}, {"cve": {"id": "CVE-2023-22322", "sourceIdentifier": "<EMAIL>", "published": "2023-01-30T07:15:09.237", "lastModified": "2023-02-06T19:55:36.983", "vulnStatus": "Analyzed", "descriptions": [{"lang": "en", "value": "Improper restriction of XML external entity reference (XXE) vulnerability exists in OMRON CX-Motion Pro ********* and earlier. If a user opens a specially crafted project file created by an attacker, sensitive information in the file system where CX-Motion Pro is installed may be disclosed."}], "metrics": {"cvssMetricV31": [{"source": "<EMAIL>", "type": "Primary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:U/C:H/I:N/A:N", "attackVector": "LOCAL", "attackComplexity": "LOW", "privilegesRequired": "NONE", "userInteraction": "REQUIRED", "scope": "UNCHANGED", "confidentialityImpact": "HIGH", "integrityImpact": "NONE", "availabilityImpact": "NONE", "baseScore": 5.5, "baseSeverity": "MEDIUM"}, "exploitabilityScore": 1.8, "impactScore": 3.6}]}, "weaknesses": [{"source": "<EMAIL>", "type": "Primary", "description": [{"lang": "en", "value": "CWE-611"}]}], "configurations": [{"nodes": [{"operator": "OR", "negate": false, "cpeMatch": [{"vulnerable": true, "criteria": "cpe:2.3:a:omron:cx-motion_pro:*:*:*:*:*:*:*:*", "versionEndExcluding": "*********", "matchCriteriaId": "37398382-4AB8-4A93-A243-D5E0DC74D302"}]}]}], "references": [{"url": "https://jvn.jp/en/vu/JVNVU94200979/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}}, {"cve": {"id": "CVE-2023-22324", "sourceIdentifier": "<EMAIL>", "published": "2023-01-30T07:15:09.767", "lastModified": "2023-02-06T19:55:03.167", "vulnStatus": "Analyzed", "descriptions": [{"lang": "en", "value": "SQL injection vulnerability in the CONPROSYS HMI System (CHS) Ver.3.5.0 and earlier allows a remote authenticated attacker to execute an arbitrary SQL command. As a result, information stored in the database may be obtained."}], "metrics": {"cvssMetricV31": [{"source": "<EMAIL>", "type": "Primary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N", "attackVector": "NETWORK", "attackComplexity": "LOW", "privilegesRequired": "LOW", "userInteraction": "NONE", "scope": "UNCHANGED", "confidentialityImpact": "HIGH", "integrityImpact": "NONE", "availabilityImpact": "NONE", "baseScore": 6.5, "baseSeverity": "MEDIUM"}, "exploitabilityScore": 2.8, "impactScore": 3.6}]}, "weaknesses": [{"source": "<EMAIL>", "type": "Primary", "description": [{"lang": "en", "value": "CWE-89"}]}], "configurations": [{"nodes": [{"operator": "OR", "negate": false, "cpeMatch": [{"vulnerable": true, "criteria": "cpe:2.3:a:contec:conprosys_hmi_system:*:*:*:*:*:*:*:*", "versionEndExcluding": "3.5.1", "matchCriteriaId": "5CD57955-96C3-4807-8BA1-0DAC528D345C"}]}]}], "references": [{"url": "https://jvn.jp/en/vu/JVNVU97195023/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://www.contec.com/api/downloadlogger?download=/-/media/Contec/jp/support/security-info/contec_security_chs_230124_en.pdf", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}, {"url": "https://www.contec.com/download/contract/contract4/?itemid=ea8039aa-3434-4999-9ab6-897aa690210c&downloaditemid=866d7d3c-aae9-438d-87f3-17aa040df90b", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}}, {"cve": {"id": "CVE-2023-22332", "sourceIdentifier": "<EMAIL>", "published": "2023-01-30T07:15:10.003", "lastModified": "2023-02-06T19:54:28.683", "vulnStatus": "Analyzed", "descriptions": [{"lang": "en", "value": "Information disclosure vulnerability exists in Pgpool-II 4.4.0 to 4.4.1 (4.4 series), 4.3.0 to 4.3.4 (4.3 series), 4.2.0 to 4.2.11 (4.2 series), 4.1.0 to 4.1.14 (4.1 series), 4.0.0 to 4.0.21 (4.0 series), All versions of 3.7 series, All versions of 3.6 series, All versions of 3.5 series, All versions of 3.4 series, and All versions of 3.3 series. A specific database user's authentication information may be obtained by another database user. As a result, the information stored in the database may be altered and/or database may be suspended by a remote attacker who successfully logged in the product with the obtained credentials."}], "metrics": {"cvssMetricV31": [{"source": "<EMAIL>", "type": "Primary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N", "attackVector": "NETWORK", "attackComplexity": "LOW", "privilegesRequired": "LOW", "userInteraction": "NONE", "scope": "UNCHANGED", "confidentialityImpact": "HIGH", "integrityImpact": "NONE", "availabilityImpact": "NONE", "baseScore": 6.5, "baseSeverity": "MEDIUM"}, "exploitabilityScore": 2.8, "impactScore": 3.6}]}, "weaknesses": [{"source": "<EMAIL>", "type": "Primary", "description": [{"lang": "en", "value": "CWE-312"}]}], "configurations": [{"nodes": [{"operator": "OR", "negate": false, "cpeMatch": [{"vulnerable": true, "criteria": "cpe:2.3:a:pgpool:pgpool-ii:*:*:*:*:*:*:*:*", "versionStartIncluding": "3.3.0", "versionEndIncluding": "3.7.12", "matchCriteriaId": "AC6F9DF2-27FB-43BE-B4EB-5296C01BD28E"}, {"vulnerable": true, "criteria": "cpe:2.3:a:pgpool:pgpool-ii:*:*:*:*:*:*:*:*", "versionStartIncluding": "4.0.0", "versionEndExcluding": "4.0.22", "matchCriteriaId": "AEFBFF5E-DE69-4F94-B4BD-53C8C91CA850"}, {"vulnerable": true, "criteria": "cpe:2.3:a:pgpool:pgpool-ii:*:*:*:*:*:*:*:*", "versionStartIncluding": "4.1.0", "versionEndExcluding": "4.1.15", "matchCriteriaId": "1800AB14-AF70-4D4D-8E3D-FCFC7790F1FC"}, {"vulnerable": true, "criteria": "cpe:2.3:a:pgpool:pgpool-ii:*:*:*:*:*:*:*:*", "versionStartIncluding": "4.2.0", "versionEndExcluding": "4.2.12", "matchCriteriaId": "3D3373F7-66DD-4A05-B7AF-8ABEAF99F4F9"}, {"vulnerable": true, "criteria": "cpe:2.3:a:pgpool:pgpool-ii:*:*:*:*:*:*:*:*", "versionStartIncluding": "4.3.0", "versionEndExcluding": "4.3.5", "matchCriteriaId": "AA369670-DE4B-478A-87C8-57A60929B885"}, {"vulnerable": true, "criteria": "cpe:2.3:a:pgpool:pgpool-ii:*:*:*:*:*:*:*:*", "versionStartIncluding": "4.4.0", "versionEndExcluding": "4.4.2", "matchCriteriaId": "7A6FBCE3-2494-4B70-A094-289DE7AC6D64"}]}]}], "references": [{"url": "https://jvn.jp/en/jp/JVN72418815/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://www.pgpool.net/mediawiki/index.php/Main_Page#News", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}}, {"cve": {"id": "CVE-2023-22333", "sourceIdentifier": "<EMAIL>", "published": "2023-01-30T07:15:10.227", "lastModified": "2023-02-07T17:01:45.707", "vulnStatus": "Analyzed", "descriptions": [{"lang": "en", "value": "Cross-site scripting vulnerability in EasyMail 2.00.130 and earlier allows a remote unauthenticated attacker to inject an arbitrary script."}], "metrics": {"cvssMetricV31": [{"source": "<EMAIL>", "type": "Primary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N", "attackVector": "NETWORK", "attackComplexity": "LOW", "privilegesRequired": "NONE", "userInteraction": "REQUIRED", "scope": "CHANGED", "confidentialityImpact": "LOW", "integrityImpact": "LOW", "availabilityImpact": "NONE", "baseScore": 6.1, "baseSeverity": "MEDIUM"}, "exploitabilityScore": 2.8, "impactScore": 2.7}]}, "weaknesses": [{"source": "<EMAIL>", "type": "Primary", "description": [{"lang": "en", "value": "CWE-79"}]}], "configurations": [{"nodes": [{"operator": "OR", "negate": false, "cpeMatch": [{"vulnerable": true, "criteria": "cpe:2.3:a:mubag:easymail:*:*:*:*:*:*:*:*", "versionEndIncluding": "2.00.130", "matchCriteriaId": "AEC0F2FA-C107-466F-AE20-670CBEFF6EBD"}]}]}], "references": [{"url": "https://jvn.jp/en/jp/JVN05288621/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://www.mubag.com/download/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}}, {"cve": {"id": "CVE-2022-46356", "sourceIdentifier": "<EMAIL>", "published": "2023-01-30T08:15:07.957", "lastModified": "2023-02-07T16:51:29.243", "vulnStatus": "Analyzed", "descriptions": [{"lang": "en", "value": "Potential vulnerabilities have been identified in HP Security Manager which may allow escalation of privilege, arbitrary code execution, and information disclosure."}], "metrics": {"cvssMetricV31": [{"source": "<EMAIL>", "type": "Primary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:H", "attackVector": "LOCAL", "attackComplexity": "LOW", "privilegesRequired": "LOW", "userInteraction": "NONE", "scope": "CHANGED", "confidentialityImpact": "HIGH", "integrityImpact": "HIGH", "availabilityImpact": "HIGH", "baseScore": 8.8, "baseSeverity": "HIGH"}, "exploitabilityScore": 2.0, "impactScore": 6.0}]}, "weaknesses": [{"source": "<EMAIL>", "type": "Primary", "description": [{"lang": "en", "value": "NVD-CWE-noinfo"}]}], "configurations": [{"nodes": [{"operator": "OR", "negate": false, "cpeMatch": [{"vulnerable": true, "criteria": "cpe:2.3:a:hp:security_manager:*:*:*:*:*:*:*:*", "versionEndExcluding": "3.9", "matchCriteriaId": "269DDB99-D097-4CA2-BDD4-29074A409464"}]}]}], "references": [{"url": "https://support.hp.com/us-en/document/ish_7334353-7334378-16", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}}, {"cve": {"id": "CVE-2022-46357", "sourceIdentifier": "<EMAIL>", "published": "2023-01-30T08:15:08.087", "lastModified": "2023-02-07T16:51:57.913", "vulnStatus": "Analyzed", "descriptions": [{"lang": "en", "value": "Potential vulnerabilities have been identified in HP Security Manager which may allow escalation of privilege, arbitrary code execution, and information disclosure."}], "metrics": {"cvssMetricV31": [{"source": "<EMAIL>", "type": "Primary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:H", "attackVector": "LOCAL", "attackComplexity": "LOW", "privilegesRequired": "LOW", "userInteraction": "NONE", "scope": "CHANGED", "confidentialityImpact": "HIGH", "integrityImpact": "HIGH", "availabilityImpact": "HIGH", "baseScore": 8.8, "baseSeverity": "HIGH"}, "exploitabilityScore": 2.0, "impactScore": 6.0}]}, "weaknesses": [{"source": "<EMAIL>", "type": "Primary", "description": [{"lang": "en", "value": "NVD-CWE-noinfo"}]}], "configurations": [{"nodes": [{"operator": "OR", "negate": false, "cpeMatch": [{"vulnerable": true, "criteria": "cpe:2.3:a:hp:security_manager:*:*:*:*:*:*:*:*", "versionEndExcluding": "3.9", "matchCriteriaId": "269DDB99-D097-4CA2-BDD4-29074A409464"}]}]}], "references": [{"url": "https://support.hp.com/us-en/document/ish_7334353-7334378-16", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}}, {"cve": {"id": "CVE-2022-46358", "sourceIdentifier": "<EMAIL>", "published": "2023-01-30T08:15:08.143", "lastModified": "2023-02-07T16:28:52.190", "vulnStatus": "Analyzed", "descriptions": [{"lang": "en", "value": "Potential vulnerabilities have been identified in HP Security Manager which may allow escalation of privilege, arbitrary code execution, and information disclosure."}], "metrics": {"cvssMetricV31": [{"source": "<EMAIL>", "type": "Primary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:H", "attackVector": "LOCAL", "attackComplexity": "LOW", "privilegesRequired": "LOW", "userInteraction": "NONE", "scope": "CHANGED", "confidentialityImpact": "HIGH", "integrityImpact": "HIGH", "availabilityImpact": "HIGH", "baseScore": 8.8, "baseSeverity": "HIGH"}, "exploitabilityScore": 2.0, "impactScore": 6.0}]}, "weaknesses": [{"source": "<EMAIL>", "type": "Primary", "description": [{"lang": "en", "value": "NVD-CWE-noinfo"}]}], "configurations": [{"nodes": [{"operator": "OR", "negate": false, "cpeMatch": [{"vulnerable": true, "criteria": "cpe:2.3:a:hp:security_manager:*:*:*:*:*:*:*:*", "versionEndExcluding": "3.9", "matchCriteriaId": "269DDB99-D097-4CA2-BDD4-29074A409464"}]}]}], "references": [{"url": "https://support.hp.com/us-en/document/ish_7334353-7334378-16", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}}, {"cve": {"id": "CVE-2022-46359", "sourceIdentifier": "<EMAIL>", "published": "2023-01-30T08:15:08.200", "lastModified": "2023-02-07T16:29:57.773", "vulnStatus": "Analyzed", "descriptions": [{"lang": "en", "value": "Potential vulnerabilities have been identified in HP Security Manager which may allow escalation of privilege, arbitrary code execution, and information disclosure."}], "metrics": {"cvssMetricV31": [{"source": "<EMAIL>", "type": "Primary", "cvssData": {"version": "3.1", "vectorString": "CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:H", "attackVector": "LOCAL", "attackComplexity": "LOW", "privilegesRequired": "LOW", "userInteraction": "NONE", "scope": "CHANGED", "confidentialityImpact": "HIGH", "integrityImpact": "HIGH", "availabilityImpact": "HIGH", "baseScore": 8.8, "baseSeverity": "HIGH"}, "exploitabilityScore": 2.0, "impactScore": 6.0}]}, "weaknesses": [{"source": "<EMAIL>", "type": "Primary", "description": [{"lang": "en", "value": "NVD-CWE-noinfo"}]}], "configurations": [{"nodes": [{"operator": "OR", "negate": false, "cpeMatch": [{"vulnerable": true, "criteria": "cpe:2.3:a:hp:security_manager:*:*:*:*:*:*:*:*", "versionEndExcluding": "3.9", "matchCriteriaId": "269DDB99-D097-4CA2-BDD4-29074A409464"}]}]}], "references": [{"url": "https://support.hp.com/us-en/document/ish_7334353-7334378-16", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}}]}