import pandas as pd
import openpyxl
import requests
import urllib
from bs4 import BeautifulSoup
import pandas as pd
import numpy as np

from datetime import datetime,timedelta,tzinfo
from dateutil import tz
import openpyxl
from openpyxl import Workbook
from openpyxl import load_workbook
from openpyxl.styles import Border, Side, Alignment, Font
import time

from pandas import ExcelWriter
import os
from urllib.request import Request, urlopen
import ssl
import re
import shutil

import warnings
warnings.filterwarnings("ignore")

date=[]
vulnsource=[]
vulnname=[]
zday=[]
poc=[]
descrip=[]
des=[]
publicid=[]
temp=[]
temp1=[]
temp2=[]
productname=[]
basescore=[]
vendor=[]
advisory=[]
link=[]
classname=[]
local=[]
remote=[]

excelFilePath= r'C:\Users\<USER>\Downloads\Compressed\vulndbformat2.xlsx'
def loginToOpenVas():
    url = 'https://secinfo.greenbone.net/login'
    values = {'cmd':'login','text':'/gmp?r=1','login': 'gbpsguest',
          'password': 'HalloGreenbone2020'}
    s = requests.session()
    r = s.post(url, values,verify=False)
    if r.status_code == 200:
        soup = BeautifulSoup(r.text, 'html.parser')
        print(r.text)
        token = soup.find_all('token')
        for text in token:
            token = text.string
##        print(token)
##        quote_page = 'https://secinfo.greenbone.net/gmp?cmd=get_info&info_type=nvt&info_name=CVE-1999-0678&details=1&filter=&filt_id=0&token=%s' % (token)
##        r1 = s.get(quote_page)
##        print(r1.text)
        return s,token
    else:
        return None

df = pd.read_excel(excelFilePath)
#print(df['CVE-ID'].to_string(index=False))

wb_obj = openpyxl.load_workbook(excelFilePath) 
sheet_obj = wb_obj.active
columnHeader = sheet_obj.cell(row = 1,column = 3)

#cveID = sheet_obj.cell(row = i,column = 4)
for i in range(df.shape[0]):
    cveID = sheet_obj.cell(row = i+1,column = 4)
    cveID_val = df['CVE-ID'][i]
    print(cveID_val)
    if cveID_val != None or cveID_val != "Null" :
                try:
                    session, tokenVal = loginToOpenVas()
                    if tokenVal == None or session == None:
                        raise Exception('login unsuccessful')
                except:
                    print("Exception occuring in fetching the token value hence retrying")
                    time.sleep(5)
                    session, tokenVal = loginToOpenVas()
##                cve_Val='CVE-2021-3110'
                openVasLink = 'https://secinfo.greenbone.net/gmp?token='+\
                 str(tokenVal)+'&cmd=get_info&info_type=nvt&filter='+str(cveID_val)+\
                 '&Update+Filter.x=0&Update+Filter.y=0&'+\
                'filter_extra=sort-reverse%3Dcreated+rows%3D10+first%3D1+'
                print(openVasLink)
                time.sleep(1);
                req = session.get(openVasLink)
##                req = Request(openVasLink, headers={'User-Agent': 'Mozilla/5.0'})
                #creating ssl context to bypass issue of ssl certificate verification
##                openVasPage = urlopen(req,context = ssl._create_unverified_context()).read()
                soup1 = BeautifulSoup(req.text, 'html.parser')
                plugin_coverage = sheet_obj.cell(row = i+2,column = 20)
                pluginID_c1 = sheet_obj.cell(row = i+2,column = 21)
                try:
                    print("inside try block for fetching openvas plugin id")
##                    table = soup1.find('info' ,attrs={'id': 'sc-bdVaJa sUcSo'})
##                    print(table)
                    pluginID = soup1.find('info').attrs['id'].strip()
##                    statString = "View details of NVT "
##                    pluginID = vendor.replace(statString, "")
                    print(pluginID)

                    plugin_coverage.value = "Y"
                    pluginID_c1.value = pluginID
                    
                except Exception as e:
                    print("inside except block for fetching openvas plugin id")
                    print("Error Message \n:",str(e))
                    
                    plugin_coverage.value = "N"
                    pluginID_c1.value = "Na"
                    print(plugin_coverage.value,pluginID_c1.value)
                    wb_obj.save(excelFilePath)
                    

                    
            






# wb_obj = load_workbook("vulndbformat.xlsx") 
# sheet_obj = wb_obj.active
# cveID = sheet_obj.cell(row = 1,column = 4)

