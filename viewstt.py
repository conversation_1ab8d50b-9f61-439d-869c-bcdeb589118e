from flask import Flask, render_template_string
import base64

app = Flask(__name__)

@app.route("/")
def vulnerable_viewstate():
    # Decoded viewstate string with sensitive data
    decoded_viewstate = (
        "__type=LoginRequest:#WebApp.Models&username=admin&password=SuperSecret123"
        "&sessionid=abcd1234efgh5678"
        "&access_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
        ".eyJzdWIiOiIxMjM0IiwibmFtZSI6IkFkbWluIFVzZXIiLCJpYXQiOjE1MTYyMzkwMjJ9"
        ".dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk"
        "&apikey=sk_test_51H8sX"
    )

    # Encode the viewstate in base64
    encoded_viewstate = base64.b64encode(decoded_viewstate.encode()).decode()

    # Basic HTML form with viewstate
    html = f"""
    <!DOCTYPE html>
    <html>
    <head><title>Test ViewState</title></head>
    <body>
        <form method="post" action="/login">
            <input type="hidden" name="__VIEWSTATE" value="{encoded_viewstate}" />
            <input type="text" name="username" />
            <input type="password" name="password" />
            <input type="submit" value="Login" />
        </form>
    </body>
    </html>
    """
    return render_template_string(html)

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8080)
