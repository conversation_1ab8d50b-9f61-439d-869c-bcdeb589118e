<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<script type="text/javascript">
    var jQueryLoaded = true;
</script>



<script language="JavaScript" src="/webclient/vendor/js/jquery.js?build=6220" type="text/javascript"></script>
<script language="JavaScript" src="/js/jquery-ui.min.js" type="text/javascript"></script>
<script type="text/javascript">
var adsjQuery = jQuery.noConflict();
</script>

















<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<link REL="SHORTCUT ICON" HREF='&#x2f;images&#x2f;logos&#x2f;ADSSPDesktop.ico'>
<title>SFL - Password SelfService Portal</title>
<script language="JavaScript" src="/js/form-util.js?build=6403"></script>
<script language="JavaScript" src="/js/CommonUtil.js?build=6403"></script>
<script>

var jQueryLoaded = jQueryLoaded || false;
if(!jQueryLoaded)
{
	var script = document.createElement( 'script' );
	script.type = 'text/javascript';
	script.src = "/webclient/vendor/js/jquery.js?build=6403";
	if((window.name != "advancedPC_Frames") && (window.name != "advancedIDV_Frames")) //handled to avoid jquery-ui conflict, need to remove this check in future release
	{
		document.getElementsByTagName('head')[0].appendChild( script );
	}

	jQueryLoaded = true;


	var fileNamePattern = '^[a-zA-Z0-9.\\-_ ]{0,255}$';
	
	addEvent(window, 'load', setFileConstraints, false); //NO I18N

	function addEvent(element, eventString, functionReference, useCapture){
		if (element.addEventListener) {
		  element.addEventListener(eventString, functionReference, useCapture);
		}
		else if (element.attachEvent) {
		  element.attachEvent('on'+eventString , functionReference );
		}
	}

	function setFileConstraints()
	{
	try{
		if(fileNamePattern != ''){
			var tip = '檔案名稱應該遵守正則花樣{0}'.replace('{0}', fileNamePattern);
			var errorMsg = '在檔案名中發現無效字元。它應遵循規則運算式模式{0}'.replace('{0}', fileNamePattern);
			var elements = document.getElementsByTagName('input');
			for(i in elements){
				var element = elements[i];
				var elementType = element.type;
				if(elementType == 'file' && element.getAttribute('patternText') == null){
					element.setAttribute('patternText', fileNamePattern);
					if(typeof setADSTooltip == 'function'){
					setADSTooltip(element, tip);
					}
					else{
						element.title = tip;
					}

					addEvent(element, 'change', function(event){ //NO I18N
						var files = this.files;
						for (var i = 0; i < files.length; i++) {
							if(!(new RegExp(fileNamePattern)).test(files[i].name))
							{
								if(typeof preventDefaultAction == 'function'){
									preventDefaultAction(event);
								}
								this.value = '';
								if(typeof parent.alertMsg == 'function'){
									parent.alertMsg(errorMsg);
								}
								else if(typeof alertMsg == 'function'){
									alertMsg(errorMsg);
								}
								return false;
							}
						}
					}, true);
				}
			}
		}	
		}
		catch(err){}		
	}
}

</script>


<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<META HTTP-EQUIV="PRAGMA" CONTENT="NO-CACHE">




<script language="JavaScript" type="text/javascript" src="/js/jsencrypt.min.js"></script>
<script language="JavaScript" type="text/javascript" src="/js/core.min.js"></script>
<script language="JavaScript" type="text/javascript" src="/js/sha256.min.js"></script>
<script language="JavaScript" type="text/javascript" src="/js/hmac.min.js"></script>
<script language="JavaScript" type="text/javascript" src="/js/pbkdf2.min.js"></script>
<script language="JavaScript" type="text/javascript" src="/js/cipher-core.min.js"></script>
<script language="JavaScript" type="text/javascript" src="/js/enc-base64.min.js"></script>
<script language="JavaScript" type="text/javascript" src="/js/aes.min.js"></script>

<script>

var AES_KEY_SIZE = 256;
var AES_IV_SIZE  = 128;
var AES_KEY_ITER_COUNT = 10;

function encryptText(text)
{
    if(false)
    {
	   if(text.length <= 245)
	   {
		   var publicKey="-----BEGIN PUBLIC KEY-----"+ "" + "-----END PUBLIC KEY-----";//No I18N
		   var jsEncryptObj = new JSEncrypt();
		   jsEncryptObj.setPublicKey(publicKey);
		   return jsEncryptObj.encrypt(text);
	   }
	   else
	   {
			var iv = CryptoJS.lib.WordArray.random(AES_IV_SIZE / 8).toString(CryptoJS.enc.Hex);
			var salt = CryptoJS.lib.WordArray.random(AES_KEY_SIZE/ 8).toString(CryptoJS.enc.Hex);
			var key = CryptoJS.PBKDF2("", CryptoJS.enc.Hex.parse(salt), { keySize: this.AES_KEY_SIZE / 32, iterations: this.AES_KEY_ITER_COUNT, hasher : CryptoJS.algo.SHA256 });
            var encrypted = CryptoJS.AES.encrypt(text, key, { iv: CryptoJS.enc.Hex.parse(iv) });
		    var ciphertext = encrypted.ciphertext.toString(CryptoJS.enc.Base64);
			return  salt + iv + ciphertext;
	   }
    }
    else
    {
       return text;
    }

}


</script>


































<html>

<head>
	

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>


 







<head>
	

<style>
 .fntFamily{font-family: verdana;}
 .fntSize{font-size:10px;}
 .common-textcolor{color:#04426c !important;}
 .common-bgcolor{background:#04426c !important;}
 .common-bordercolor{border-color:#04426c !important;}
 .adsfntFamily{font-family: verdana !important;}
 .adsfntSize{font-size:10px !important;}
 .adsfontSize10{font-size:10px !important;}
 .adsfontSize11{font-size:10px !important;}
</style>


</head>

</html>


	
<link href="/styles/styles.css" rel="stylesheet" type="text/css"> 
<link href="styles/adsf/flat/Style.css" rel="stylesheet" type="text/css">
	



    <link href="styles/adsf/flat/Style.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="/webclient/assets/login.css">
    <link rel="stylesheet" href="/webclient/assets/app_overwrite.css">
	<link href="styles/adsf/common/jquery.mCustomScrollbar.css" rel="stylesheet" type="text/css">
	
		
	<link href="styles/customer-styles.css" rel="stylesheet" type="text/css"> 

    <style>
        .fwsmart-card-login .fwsmart-card-icon
        {
            background: url(../images/adsf/common/flat-common-sprites-images.png) -164px -369px;
            width: 20px;
            height: 14px;
            display: inline-block;
            vertical-align: middle;
            margin-right: 5px;
            cursor: pointer;
        }

        .text
        {
            background: none !important;
        }
    </style>

    
    <script type="text/javascript" src="/webclient/vendor/js/jquery.js?build=6403"></script>
    <script>
        var flatThemeJquery = $;
    </script>
    <script type="text/javascript" src="/webclient/vendor/js/bootstrap.js?build=6403"></script>
    <script type="text/javascript" src="/webclient/vendor/js/bootstrap-select.min.js"></script>
    <script language="JavaScript" src="/adsf/js/common/JumpTo.js" type="text/javascript"></script>
    <script language="JavaScript" src="/adsf/js/common/sso/CustomSSO.js" type="text/javascript"></script>
    <script language="JavaScript" src="/adsf/js/common/WindowHandler.js"></script>
    <script language=JavaScript src="/js/report.js?build=6403"></script>
    <script language=JavaScript src="/js/form-util.js?build=6403"></script>
    <script language=JavaScript src="/js/Esearch.js?build=6403"></script>
    <script language=JavaScript src="/js/common/LanguageList.js?build=6403" ></script>
    <script language=JavaScript src="/js/layout/Utils.js?build=6403"></script>
    <script type="text/javascript" src="/js/AjaxAPI.js?build=6403"></script>
    <script type="text/javascript" src="/js/CommonUtil.js"></script>
    <script type="text/javascript" src="/js/ValidateLogon.js?build=6403"></script>
    <script type="text/javascript" src="/js/jquery.mousewheel.js?build=6403"></script>
    <script type="text/javascript" src="/webclient/vendor/js/jquery.mCustomScrollbar.concat.min.js"></script>

    <script language="JavaScript" type="text/JavaScript">
		
        flatThemeJquery('[data-toggle="tooltip"]').tooltip(); //No I18N
        function smartCardLogin(loginType)
        {
            $("#smart-card").css("pointer-events", "none"); //No I18N
            flatThemeJquery('form[name="loginSmartCard'+loginType+'"]').trigger('submit');//No I18N
        }

        function initSAMLLoginRequest(idpLoginUrl, reqMethod)	 
        {	 
            if (reqMethod == 'GET')
            {
                window.top.location.replace(idpLoginUrl);	 
            }
            else if (reqMethod == 'POST')
            {
                var splitUrl = idpLoginUrl.split("?");
                var loginUrl = splitUrl[0];
                var reqParams = splitUrl[1];
                CreateAndSubmitForm(loginUrl,window.name,reqParams);
            }
        }	
        
		function addTitleForSelectPicker(id)
		{
			var title = [];
			$('#'+id+' option').each(function(){	//NO I18N
				title.push($(this).attr('title'));	//NO I18N
			});
			$("#"+id).parent().children("div").children("ul").children("li").each(function(i){	//NO I18N
			  $(this).children("a").attr('title',title[i]);	//NO I18N
			});
		}
		
		function showPasswordLogin(form, defPassVal)
        {
            togglePasswordField(form, true, defPassVal, "密碼");
        }
		
		function showUserNameField(form, defPassVal)
        {
			toggleUsernameField(form, true, defPassVal, "用戶名");
        }
		
		function getUserNameField(defaultVal, placeholder)
        {
           return '<li><input name="j_username" type="text" id="j_username" value="'+defaultVal+'" placeholder="'+placeholder+'" class="linput" autocomplete="off" onkeypress="javascript:onKeyPressEvent(event, document.DomainUserLogin)" onkeyup="javascript:onKeyUpEvent(event, document.DomainUserLogin)" onpaste="javascript:onPasteEvent(this,document.DomainUserLogin)" maxlength="255"/><span class="add-on form-icn-user" style="cursor:text"></span></li>';
        }

    </script>


<script language="JavaScript" type="text/JavaScript" >

	var jsTitleMsg=eval({"adssp_admin_gina_err_incorrect":"函數不正確","ads_admin_product_settings_ha_master_service_not_installed":"主要伺服器的{0}沒有安裝為服務。","adssp_admin_configuration_tech_alert_pwd_length":"密碼長度不能小於7個字元","ads_admin_product_settings_ha_slave_service_not_privileged":"請為備用伺服器上的{0}服務配置具有域管理員許可權的憑證。","adssp_configuration_ppe_dict_spec":"請確保字典檔中的各個單詞以回車換行分隔。","adssp_config_admin_tools_helpdesk_all_policies":"所有策略","adssp_admin_config_pwd_sync_automatic_link_alert_ibmas400":"如果在AD及IBM i/AS400系統中的用戶名相同，則使用者帳戶將自動連結","adssp_admin_logon_customize_dragndrop_htmlarea_italic":"斜體","adssp_license_expiry_available_count_unlimited":"如果您應用了無限用戶的許可，則此選項將不起作用。","adssp_admin_general_server_settgs_not_find_host":"伺服器名稱無效，請指定其它伺服器名稱之後再試！","adssp_admin_policies_scheduler_soon_to_expire_msg_fourth_templete":"您好 %userName%,\\n\\n雖然已經提醒過您，但您依然沒有更改您的密碼，該密碼將在 %dateTime% 過期。如果您仍不更改密碼，您的帳戶將會被鎖定! 請您務必立即更改密碼。\\n\\n謝謝\\n管理員","adssp_admin_logon_customize_dragndrop_htmlarea_fontsize_3_point":"3 (12 pt)","adssp_reports_enroll_rep_select_enroll_type_msg":"請選擇登記註冊的類型","adssp_config_admin_tools_helpdesk_popup_head_reason":"原因","ads_admin_product_settings_ha_master_down":"主要伺服器中的{0}已經停止，請確保它運行正常。","ads_iamapps_custom_saml_choose_sso_mode":"請選擇SSO流","adssp_admin_config_mfa_enable_qr_auth":"啟用二維碼驗證","adssp_admin_gina_couldnot_parse_server_install_path":"不能解析伺服器的安裝路徑","adssp_admin_config_pwd_sync_automatic_link_alert_hpux":"如果在AD及HP UX系統中的用戶名相同，則使用者帳戶將自動連結","adssp_config_admin_tools_helpdesk_que_mapped_in_mfa_confirm_edit_text":"此問題也用於多因素身份驗證。確實要編輯此問題嗎？","adssp_admin_selfupdate_layout_custom_fields_cannot_empty":"欄位不能為空","adssp_config_gs_select_users":"點擊 + 圖示選擇使用者","adssp_admin_server_settings_vpn_client_location":"請輸入VPN用戶端的位置","adssp_js_calendar_month_nov":"十一月","adssp_config_management_gina_custom_ppe_popup_tip":"啟用後，使用者將可以在對話嚮導中查看這個密碼策略。","adssp_admin_system_utililites_save_changes":"真的確定要保存對當前域所做的變更嗎？","adssp_configuration_admin_tools_ext_dat_source_invalid_query":"查詢無效。請檢查查詢語句是否正確。","adssp_configuration_soon_to_exp_enable_html":"啟用HTML","adssp_admin_general_server_settgs_from_address_avoid_colon":"顯示名中不能包含英文冒號、逗號和分號。","adssp_selfservice_gs_subscribe_error_try_again":"請重新嘗試","adssp_common_text_more_reports":"更多報表","adssp_admin_config_mfa_disable_qr_auth":"禁用二維碼驗證","adssp_admin_server_settings_from_address_end_start":"顯示名中雙引號應該配套（不能只有一個）","adssp_common_text_more":"更多","adssp_domain_user_my_info_photo_upload_invalid_dimensions":"尺寸無效","adssp_admin_server_settings_mail_enter_pwd":"請輸入密碼","ads_common_text_save":"保存 ","ads_configuration_mdm_not_valid_mail_domain":"請輸入有效的郵件域位址。","adssp_domain_user_reset_qa_sel_ques_alert_same_ques":"找到相同的問題","adssp_layout_admin_logon_settings_mobile_tab_photo_upload_invalid_format":"檔案格式無效[圖片支援:jpg, jpeg, bmp, png, gif]","adssp_configuration_admin_tool_quick_enroll_enable_push_notification":"必須啟用推送通知，因此，請先啟用'推送通知'","adssp_domain_user_my_info_photo_upload_size_exceeded":"文件大小超過","adssp_config_mfa_remove_configuration_confirm_msg":"您確定要刪除這個配置嗎？","adssp_configuration_sulayout_phone_format_invalid_allowed_value":"請只使用電話號碼格式中允許的值。","InvalidFileFormat":"Invalid file format. Enter a CSV file.","adssp_login_tfa_alert_no_username_in_radius_pattern":"請至少為RADIUS用戶名的式樣選擇一個<b>user_name 或 USER_NAME <\/b>屬性。","adssp_admin_customize_db_backup_schedule_support_with_default_only":"<span class=\\\"redtxtbold\\\">備註: <\/span>目前，自動備份的功能只支援本產品自帶的資料庫。","adssp_admin_server_settings_vpn_port_no":"輸入有效的埠號","adssp_admin_config_mail_attachment_remove_success":"刪除成功","unknown_error":"發生了未知錯誤","adssp_domain_user_change_pwd_alert_old_pwd":"請輸入舊密碼。","adssp_configure_policy_config_apc_umcp_onreset_alert":"當設置密碼從不過期的選項時，將不會強制更改密碼。","adssp_configuration_ppe_only_for_cp_not_for_rp":"本規則只適用於更改密碼的任務。不會應用於重置密碼的任務。","adssp_config_step_running":"運行中","adssp_config_sulayout_rule_del_confirm":"真的要刪除該規則嗎？","ads_admin_product_settings_ha_modify_access_url":"訪問URL已經修改，請將該伺服器的名稱更新到虛擬位址。","adssp_layout_admin_logon_settings_mobile_tab_photo_upload_valid_format_help":"有效的圖片格式:jpg, jpeg, bmp, png, gif。\\n 注意:動畫gif在手機App中將沒有動畫效果","adssp_export_settings_photo_upload_size_exceeded":"圖片尺寸超過2MB的限制，請上傳較小的圖片。","close":"關閉","adssp_common_alert_mail_format_empty":"請填寫郵件域","adssp_js_calendar_month_aug":"八月","ads_admin_product_settings_ha_public_name_not_valid":"虛擬主機名稱無效。","ads_admin_product_settings_ha_master_service_not_privileged":"請為主要伺服器上的{0}服務配置具有域管理員許可權的憑證。","adssp_admin_customize_db_backup_schedule_already_bkup_process_running":"備份資料庫的進程已經在運行。","ads_admin_smartcard_please_alert_are_you_sure_want_to_delete_ca":"真的確定要移除CA配置嗎？","ads_admin_product_settings_ha_master_slave_context_error":"啟用高可用性時出錯。請確保主要伺服器和備用服務器具有相同的上下文路徑，並且其構建號支持高可用性。","adssp_login_tfa_alert_saml_invalid_logout_url":"請使用有效的HTTP/HTTPS網址","adssp_admin_customize_logon_drag_drop_provide_link":"請提供合適的連結","adssp_admin_gina_reinstall_success":"重裝成功","ads_configuration_mdm_setup_country_code_alert":"請確保國家代碼為2個字母。","adssp_config_management_custom_text_provide_frame_text":"請提供框架文字","the":"The","adssp_common_text_the":" ","adssp_admin_policy_config_apc_notify_rp_mail_verify_code_body":"尊敬的%userName%,\\n\\n我們已經得到您希望重置密碼/解鎖帳戶的要求。為了更進一步確認您的身份，防止帳戶被盜用，請在重置密碼/解鎖帳戶的頁面中輸入這裡的驗證碼： %confirmCode%\\n\\n謝謝！\\n管理員","adssp_reports_audit_rep_data_time_all_att":"所有嘗試的日期與時間","adssp_admin_policies_policy_config_adv_ID_ver":"高級","calendarNoSetUp":"日曆設置:  沒有任何設置 (沒有發現任何欄位)。請檢查代碼。","adssp_js_calendar_saturday":"星期六","adssp_enrollment_alert_configure_duo_sec":"您需要使用Duo登記註冊。","adssp_common_text_show":"顯示","adssp_admin_server_settings_mail_content_empty":"請輸入測試消息的內容","adssp_choose_recipient_select_mobile_value_alert":"請選擇一個手機號。","adssp_configuration_ppe_choose_file_less_than_10MB":"檔大小不能超過10 MB。","adssp_js_calendar_day_thu":"四","adssp_admin_policies_sulayout_enter_your_layout_name":"輸入您的佈局名","adssp_admin_settings_connection_ssl_tool_validity_positive":"有效性期限不能為負數","adssp_admin_server_settings_vpn_enter_connect_cmd":"必填欄位不能為空。","adssp_configuration_task_scheduler_enter_enroll_url":"請提供進行登記註冊的伺服器的URL","adssp_admin_adsearch_alert_atleaset_one_obj_should_enable":"至少要啟用一個要搜索的類型。","day_tue":"Tue","adssp_domain_user_enrollment_ver_code_duplicate_email_id":"郵箱地址重複，請提供唯一的郵箱地址。","adssp_domain_user_reset_qa_sel_ques_text_the_ques_should_between_to_chars_long":"'問題的長度應該為' +var1+ ' - ' + var2 + '個字元' ","adssp_su_custom_field_cant_update_mapped_as_login_attribute":"無法更新此屬性。此屬性正在用作登錄屬性。","ads_admin_product_settings_ha_pip_master_range_error":"虛擬IP位址必須與主要伺服器的IP位址在同一網段。","adssp_admin_config_mfa_enable_microsoft_auth":"啟用Microsoft身份驗證","adssp_admin_config_pwd_sync_automatic_link_alert_adlds":"如果在AD和AD LDS中的用戶名是相同的，則會自動連結其使用者帳戶。","adssp_license_expiry_value_notification_days_range":"請確保發送許可即將過期通知的天數的範圍為：0 到 365。","adssp_common_confirm_box_navigate_away_msg":"真的要離開本頁面嗎？您的變更尚未保存。","error_code":"錯誤代碼","admin_schedule_reports_select_reports":"----- 選擇報表 ----- ","ads_configuration_mdm_no_mpm":"要安裝手機應用，必須配置手機應用部署。點擊確定進行配置。","adssp_domain_user_enrollment_dont_leave_code_empty":"代碼欄位不能為空","adssp_domain_user_my_info_photo_upload_invalid_extension":"無效的副檔名","adssp_admin_logon_customize_dragndrop_htmlarea_fontname_garamond":"加拉蒙字體","adssp_common_alert_enter_no_btwn_1_and_365":"請輸入有效的值，範圍：1 - 365。","adssp_admin_general_server_settgs_not_valid_port":"請輸入有效的埠號。","adssp_admin_gina_err_adminshare_not_enabled":"沒有啟用管理共用","adssp_config_gina_err_logon_failure_target_ac_incorrect":"如果兩台計算機具有相同的名稱時會出現該錯誤資訊。其中，一台電腦位於子域中，一台電腦位於父系網域中。","adssp_reset_unlock_accounts_secret_ques_alert_verify_ans":"驗證您的答案","ads_iamapps_account_mapping_account_linking_tooltip2":"從{0}中選擇一個屬性。","ads_iamapps_account_mapping_account_linking_tooltip1":"從Active Directory中選擇一個屬性。","adssp_login_tfa_alert_passcode":"密碼","adssp_admin_policies_policy_config_alert_min_max_not_match_que":"使用者定義的問題的最小字元數和最大字元數不能相同。","adssp_login_admin_page_script_alert_enter_valid_name_pwd":"請輸入用戶名和密碼","adssp_config_mfa_create_configuration_radius_text":"RADIUS","adssp_admin_policies_scheduler_soon_to_expire_msg_second_templete":"尊敬的 %userName%,\\n\\n您的密碼將在%dateTime%過期。雖然已經提醒您，但是您至今仍沒有修改密碼。請儘快修改您的域密碼。\\n\\n謝謝！\\n\\n順致商祺！\\n管理員","positiveValues":"請輸入正的數值","admin_policy_confirm_delete_policy":"真的要刪除該策略？","adssp_js_calendar_month_apr":"四月","adssp_configuration_ppe_fill_all_fields":"請為所有平臺填寫所有自訂欄位。","adssp_login_tfa_alert_pin_mismatch":"新的PIN碼與確認PIN碼不一致。","adssp_configuration_sulayout_phone_format_not_empty":"電話格式不能為空","adssp_admin_settings_connection_ssl_tool_enter_password":"請輸入密碼","ads_configuration_mdm_pushmanager_settings_goto_step3":"您的簽名CSR已經郵件給您，是否仍要返回？","ads_configuration_mdm_pushmanager_settings_goto_step1":"您的CER已經上傳成功。是否仍有返回？","adssp_admin_config_mfa_disable_totp_auth":"禁用基於時間的一次性密碼身份驗證","adssp_admin_settings_connection_ssl_tool_pwd_min_chars_new":"密碼長度必須大於6個字元且不能包含空格。","adssp_login_tfa_alert_confiure_mail_server_new":"必須配置郵件伺服器。請首先到'伺服器設置'頁面配置相應的資訊。真的要不保存更改繼續嗎？","ads_configuration_mdm_pushmanager_settings_goto_step2":"您的CSR已經生成，是否仍要返回？","adssp_configuration_gina_invalid_csv_need_proper_file":"需要合適的檔","adssp_config_admin_tools_helpdesk_empty_question":"安全問題不能為空。","ads_admin_product_settings_ha_master_service_privilege_failure":"未能更新主要伺服器上的該服務的使用者憑證，請手動更新後再試。","adssp_common_text_some_fields_left_blank":"某些欄位為空，請檢查。","adssp_admin_general_enable_audio_capt_txt":"<table style='position:relative;font-size:10px'><tr><td><span class=dot><\/span><\/td><td><div>當啟用音訊驗證碼時，驗證碼圖片中只能顯示數位。<\/div><\/td><\/tr><tr><td><span class=dot><\/span><\/td><td><div>如果流覽器不支持音訊驗證碼，驗證碼圖片中將顯示預設的驗證碼。<\/div><\/td><\/tr><\/table>  ","adssp_admin_selfupdate_layout_custom_field_disp_name_already_exist":"屬性顯示名稱已經存在。","ads_admin_smartcard_please_enter_certificate_mapping_attribute":"請選擇一個證書映射屬性。","adssp_js_calendar_month_jan":"一月","adssp_layout_domain_setting_provide_valid_domain_name":"提供有效的域顯示名","adssp_common_text_pls_wait":"請稍候","adssp_common_text_popuptitle_select_groups_ous":"選擇組織單位/組","adssp_configuration_ppe_restricted_patterns_condn":"限制式樣不能以逗號開始或結束，並且不能包含連續的2個逗號。","adssp_admin_config_pwd_sync_automatic_link_alert_dynamicscrm":"如果用戶在AD和Dynamics CRM中的用戶名相同，則其使用者帳戶將自動連結。","adssp_configuration_admin_tools_ext_dat_source_select_user_to_update":"選擇要更新的用戶","adssp_admin_server_settings_mail_enter_valid_mail_subject":"輸入有效的郵件主題","adssp_admin_selfupdate_layout_single_line_text":"單行文本","adssp_admin_config_mfa_save_yubikey_auth":"保存","adssp_config_gina_err_could_not_connect_adminshare_enable_troubleshoot":"1.請配置具有管理員許可權的認證憑據。如果以應用的方式啟動本產品，請在域設置頁面設置合適的管理員帳戶；如果以Windows服務啟動本產品，請在該服務的登錄選項中設置合適的管理員憑據。<br/><br/>2.檢查是否已經啟用Admin共用。","adssp_configuration_gina_mac_error_authentication_failed":"服務帳戶憑據無效。","adssp_config_gs_selected_users":"用戶","adssp_admin_policies_policy_config_new_pol_name":"新的策略名","adssp_configuration_sulayout_initial_value_valid_char":"初始值應該為字串","ads_iamapps_config_alert_valid_domain_name":"輸入有效的功能變數名稱","adssp_admin_general_custom_disp_settgs_alert_access_control":"真的確定只有從<span style='color:FF3434;'>{0}<\/span>才能訪問管理頁面","adssp_save_button":"保存","adssp_common_layout_footer_feedback_enter_mail":"請輸入電子郵寄地址。","adssp_windows_logon_tfa_configure_access":"確保該產品的訪問URL為SSL模式（HTTPS）。","adssp_common_text_cannot_move":"不能移動該文本。","adssp_su_custom_field_cant_update_mapped_in_ad_question":"無法更新此屬性，可以針對多因素身份驗證或審批工作流中的AD安全問題進行配置。","adssp_admin_config_mfa_disable_microsoft_auth":"禁用Microsoft身份驗證","adssp_config_su_custom_attrib_unsupported_datatype":"不支援的資料類型。","adssp_reports_enroll_rep_push_reg_device_confirm_delete":"真的要取消註冊該用戶的手機號碼嗎？","adssp_admin_schedulelist_enable":"啟用","ads_iamapps_config_alert_some_fields_left_blank":"部分欄位為空，請檢查。","adssp_admin_policyConfig_thirdStep_edit_ques_enter_que":"輸入問題","adssp_license_expiry_valid_domain_count_limit":"請確保用戶數的範圍為：0到","admin_policies_gina_stop_action_progress":"正在處理停止動作，它可能需要花費幾分鐘時間，請稍候再試。","ads_admin_product_settings_ha_slave_bindaddress_error":"當備用伺服器配置綁定位址時不能設置高可用性。","adssp_config_gina_restrict_bad_cert":"此選項將限制載入具有無效SSL證書的應用程式。<br>建議在移動到生產環境時啟用此選項。","adssp_admin_imp_ans_confirm":"從CSV文件導入的問題，如果與已有的不同，則會添加到安全問題列表中。","adssp_configure_policy_config_apc_auto_enable_reset_notification":"啟用重置郵件通知","adssp_config_mfa_create_configuration_confirm_create":"應用這個策略","adssp_admin_enrollment_notification_push_notification_text_push_char_limit":"推送消息的字數超過限制。請精簡消息後再試！","adssp_connection_error_refused":"連接被拒","adssp_admin_selfupdate_layout_drop_fields_app_pos":"請把該欄位放到合適的位置。","prevYr":"Prev year (hold for menu)","adssp_admin_server_settings_proxy_enter_valid_server":"請輸入正確的伺服器名","adssp_admin_server_settings_mail_enter_valid_mail_port":"請輸入有效的郵件伺服器的埠","adssp_commom_text_advance":"高級","ads_admin_product_settings_ha_disabled":"高可用性設置已經禁用成功。要使設置生效必須重啟ADSelfService Plus服務。<br><br><b>注意:<\/b> 請按照以下順序重啟服務：<br><br>1. 備用伺服器 <br>2. 主要伺服器","adssp_admin_restricted_deletedusers_msg_setto_unowned":"對於舊的報表，可設置帳戶類型為<span class=\\\"normalBoldFont\\\">\\\"未被承認的許可\\\"<\/span>。","adssp_admin_general_custom_disp_settgs_alert_access_control_give_least_one_type":"至少要為一個位址，給出管理員登錄頁面的訪問權","adssp_cancel_button":"取消","emptyMailReceipient":"沒有選擇郵件收件人。","adssp_logon_page_page_script_alert_captcha_enter_chars":"請準確輸入圖片中出現的字元。","ads_iamapps_account_mapping_account_linking_ad_tooltip2":"從{0}域中選擇一個屬性，密碼將同步到該域。","adssp_common_text_generate_on":"產生時間","adssp_common_text_type_your_mail_subject":"<在此輸入郵件主題> ","adssp_common_alert_enter_name":"欄位名稱不能為空。","ads_iamapps_account_mapping_account_linking_ad_tooltip1":"從更改密碼的AD域中選擇一個屬性。","adssp_configuration_gina_pls_select_computer":"請選擇電腦","adssp_admin_config_pwd_sync_automatic_link_alert_zoho":"如果AD和Zoho中的用戶名相同的話，將自動連結帳戶。","adssp_admin_customize_db_backup_schedule_hide_msg":"<span class=\\\"redtxtbold\\\">提示: <\/span>隱藏該消息不會取消該處理進程","ads_support_roboupdate_download":"<div style=padding-left:10px><span class=adsfontSize12><b>更新已下載!<\/b><\/span><br/><span class=adsgrayfont>點擊更新 - 它將重啟ADSelfService Plus，以完成更新。<br/>點擊稍後 - 它將在您重啟ADSelfService Plus時進行更新。<\/span><\/div>  ","adssp_admin_server_settings_vpn_enter_port_no":"請輸入VPN的埠號","adssp_config_policy_config_advanced_alert_password_constrain_voilation":"請確保各個限制條件之間沒有矛盾","month_jan":"January","adssp_js_display":"顯示","adssp_admin_selfupdate_layout_photo_dimension_format":"尺寸應該是：寬 \\* 高 的格式","adssp_popup_common_errormsg_text_no_objs_found_refresh":"沒有找到物件，請重試。","adssp_common_text_popuptitle_select_computers":"選擇電腦","emptyLogonTo":"登錄電腦為空","adssp_admin_config_mfa_disable_rsa_auth":"禁用RSA SecureID","ads_admin_product_settings_ha_slave_architecture_error":"不能對一個是32位元系統，另一個是64位元的系統設置高可用性。","monday":"Monday","adssp_configuration_sulayout_phone_format_allowed_value":"允許的值","tree_page_loading_message_message":"Loading...","adssp_admin_settings_connection_ssl_tool_length_positive":"請確保公共金鑰的長度為正的整數，不能為負數或字母","ads_saml_config_alert_fill_idp_logout_url":"您需要更新IDP登出URL才能啟用此選項。","adssp_admin_logon_customize_dragndrop_htmlarea_fontsize_7_point":"7 (36 pt)","splCharacters":"The special characters / \\ [ ]: ; | = + * ? < > @ \\\\\\\" are not allowed. Please remove them and try again.","ads_admin_product_settings_ha_slave_service_success":"成功在備用伺服器上安裝服務，請以服務的形式重啟ADSelfService Plus。","adssp_configuration_sulayout_phone_format_invalid_template":"請使用有效的電話號碼的格式範本。 使用 X,0-9,(),- 及空格。","adssp_admin_config_pwd_sync_automatic_link_alert_oracledb":"如果在AD及Oracle資料庫中的用戶名相同，則使用者帳戶將自動連結","adssp_config_gina_show_addsp_link":"此選項適用於Windows(Vista及以上)、macOS和linux。","adssp_admin_adsearch_select_object":"請選擇一個要搜索的物件(使用者/組/連絡人)","ads_support_roboupdate_alert_port_range":"埠範圍為0 - 65535。","adssp_login_tfa_rsa_not_configured_copy_config_file":"配置沒有完成。請從RSA安全伺服器複製sdconf.rec到&lt;InstallDir&gt;/bin目錄後再試。","adssp_configuration_task_scheduler_enter_net_share_path":"請提供共用的網路路徑","month_apr":"April","password_policy":"密碼不滿足密碼策略的要求。請檢查密碼的長度、複雜度以及密碼歷史是否符合要求。","adssp_admin_customize_logon_drag_drop_self_upd":"自我更新","adssp_admin_policies_identity_invalid_mobile_format":"請使用有效的手機號碼的格式範本。只能使用 X,0-9,(),-,+ 及空格。","no_user_name":"不能連接對應的機器，用戶名為空","adssp_config_multi_sec_qa_admin_defin_ques":"<b>管理員定義的問題數量<\/b>不能大於您實際已經配置的問題數，請輸入較低的問題數量，或者增加配置的問題。","no_adssp_admin_gina_no_install_action":"安裝活動不能為空","licenserestrict":"因為許可限制，安裝沒有開始","adssp_admin_settings_connection_ssl_tool_validity_exceeds_limit":"請確保您輸入的證書有效期值不超過5年。","adssp_admin_config_mfa_disable_mobile_auth":"禁用Google身份驗證","adssp_admin_gina_err_incorrect_troubleshoot":"功能異常","adssp_admin_configuration_tech_alert_confirm_del_enroll_user":"對於所選的用戶，如果他們登記註冊，則註冊資訊將被刪除","adssp_admin_server_settings_mail_enter_valid_mobile_number":"請輸入移動手機號碼","adssp_admin_customize_logon_drag_drop_provide_image":"請提供圖片","adssp_admin_policies_scheduler_soon_to_expire_subject_templete":"密碼/帳戶過期通知","admin_policy_alert_config_attributes":"配置自助更新時的AD屬性。","ads_admin_product_settings_ha_both_invalid_credential":"請輸入有效的憑證。","adssp_admin_logon_customize_dragndrop_htmlarea_fontname_impact":"impact ","adssp_admin_general_custom_disp_settgs_alert_configure_ntlmv2":"配置啟用單點登錄時所需的電腦帳戶。","adssp_login_pwd_nomatch":"密碼不匹配","ads_admin_product_settings_ha_slave_service_failure":"未能在備用伺服器上將ADSelfService Plus安裝為服務。","ads_admin_product_settings_ha_master_slave_bindaddress_error":"當主要伺服器和備用伺服器配置綁定位址時不能設置高可用性。","adssp_configuration_gina_install_process_completed":"'處理完畢 '+val ","adssp_config_mfa_advanced_alert_random_que_lessthan_man_ques":"隨機顯示的問題數不應少於標記為必填項的安全問題總數。","adssp_configure_policy_config_apc_auto_generate_pwd_alert":"密碼將會依照您的密碼策略自動生成。","ads_support_roboupdate_alert_prt_number":"請輸入有效的埠號","report_user_account_status_disabled":"已禁用","adssp_configuration_cached_credentials_connection_name":"VPN連接名稱","adssp_domain_user_reset_qa_sel_ques_alert_create_ques":"請創建問題","adssp_admin_gina_no_install_action":"安裝動作為空","adssp_admin_policyConfig_thirdStep_edit_ques_confirm_del_que":"真的確認要刪除該問題嗎？","adssp_config_mfa_create_configuration_confirm_text":"您想共用這個相同{0}配置到所有策略，還是僅應用到這個策略？","adssp_admin_license_management_restrict_users_service_accounts":"服務帳戶","adssp_common_alert_duplicate_choices":"請去除重複的選擇。","ads_common_text_click_to_install":"點擊這裡安裝","adssp_js_calendar_goToday":"轉到今天","adssp_domain_user_enrollment_ver_code_click_plus_to_add":"點擊 + 圖示，添加更多","adssp_configure_policy_config_apc_unlock_limit_alert":"本選項僅涵蓋使用者通過ADSelfService Plus執行的解鎖帳戶操作。","adssp_config_gs_alert_change_domain":"當前的設置將丟失，真的確定要切換域嗎？","adss_admin_gina_err_could_not_install_client_software":"不能安裝用戶端軟體","adssp_reports_pwd_expired_notification_mail_subject":"密碼過期通知","adssp_common_alert_config_mail_server":"必須配置郵件伺服器的資訊才能發送郵件，因此請首先進行'伺服器設置'的配置。","adssp_admin_policyConfig_thirdStep_sel_ous_groups":"請至少選擇一個組織單位或組，然後再試。","adssp_admin_selfupdate_layout_untitled":"無題","adssp_admin_gina_no_install_action_troubleshoot":"安裝動作為空","adssp_common_text_atleast_one_policy":"至少要選擇一個策略","adssp_admin_customize_logon_drag_drop_reset_pwd_desc":"重置您忘記的密碼","adssp_login_admin_page_script_alert_enter_valid_name":"Please enter valid user name to proceed","adssp_admin_logon_customize_dragndrop_htmlarea_ordered_list":"順序清單","adssp_admin_logon_customize_dragndrop_htmlarea_fontsize_2_point":"2 (10 pt)","adssp_admin_customize_db_backup_schedule_pls_db_backup_processing":"正在備份資料庫... ","adssp_admin_policies_identity_verify_hide_sec_mail_mobile_alert":"禁用本選項，將限制使用者使用備用郵寄地址和手機號碼進行登記註冊。t users from enrolling using their secondary email and mobile data.  ","adssp_config_adsqa_login_as_admin_to_configure":"請使用ADSelfService Plus身份驗證以超級管理員身份登錄以編輯此設置。","adssp_login_validate_fullname":"輸入資訊中有一個或多個非法字元 + \\ \\\\\\\"，請去掉後再試。","adssp_admin_general_custom_disp_settgs_alert_access_control_admin_login_invalid_ip_server":"IP地址/伺服器名稱無效","adssp_admin_logon_customize_dragndrop_htmlarea_fontsize_4_point":"4 (14 pt)","adssp_login_validate_username":"輸入資訊中有一個或多個非法字元/ \\ [ ]: ; | = , + * ? < > @ \\\\\\\"，請去掉後再試。","adssp_popup_viewOU_no_ou_message":"No OUs Found.","ads_admin_product_settings_ha_settings_not_disabled":"設置沒有被禁用。","adssp_support_alert_validemail":"請輸入有效的郵寄地址","ads_configuration_mdm_users_no_user_selected":"請選擇一個用戶後繼續。","adssp_admin_general_show_logon_option_help":"預先準備&quot;.&#92;&quot;您的用戶名，如果您不是域用戶。","today":"Today","adssp_admin_configuration_tech_alert_mandatary_cannot_change_password":"不能重置缺省的Admin帳戶的密碼","friday":"Friday","admin_policy_alert_cannot_applied":"所選的域中已經使用了其它策略，因此不能再應用其它策略。","adssp_admin_restricted_referesh_dc":"要獲取全部休眠使用者的報表，需要在\\\"域設置\\\"頁面配置所有的網域控制站。","ads_admin_product_settings_ha_settings_disabled":"設置禁用成功。","adssp_admin_logon_customize_dragndrop_htmlarea_fontname_timesnewroman":"Times New Roman ","goToday":"Go Today","adssp_login_tfa_alert_code":"代碼","adssp_common_alert_duplicate_empty_choices":"每個選擇應該是唯一的，且不能夠包含重複的空值。","adssp_admin_customize_db_backup_schedule_close_text":"關閉","adssp_login_tfa_alert_message_misses_confirm_code":"消息中應該包含 %confirmCode% 巨集變數。","adssp_configuration_admin_tool_quick_enroll_notify_specified_users":"真的確定要想所指定的使用者發送該消息嗎？","BlankImportCSV":"Import CSV field cannot be blank. Browse to select CSV file.","ads_support_roboupdate_latest_version":"<span class=adsfontSize12>ADSelfService Plus已經是最新版。<\/span>  ","adssp_configuration_gina_mac_error_unsupported_architecture":"由於不支持機器架構，操作失敗。","adssp_selfservice_gs_search_groups":"搜索組","adssp_enrollment_alert_configure_Duo_tab_disable_alert":"在切換頁簽之前完成Duo登記註冊。","adssp_admin_config_mail_attachment_delete_att":"真的確定要刪除該附件嗎？","ads_admin_product_settings_ha_settings_configured":"設置保存成功。","adssp_configuration_admin_tool_quick_enroll_notify_domain_users":"真的確定要想所選域的使用者發送該消息嗎？","ads_iamapps_config_alert_sp_logo_uploading_empty_file":"圖示欄位不能為空。","adssp_js_calendar_nextYr":"下一年(下拉式功能表)","adssp_config_admin_tools_helpdesk_enable_save":"啟用自助服務審批，然後保存","adssp_admin_policies_scheduler_soon_to_expire_notify_users_days_in_advance":"當還有{0}天密碼/帳戶就過期的時候通知用戶。","ads_admin_logon_settings_smartcard_settings_disable_success":"智慧卡已被禁用。","adssp_common_text_yes":"是","adssp_admin_adsearch_alert_atleaset_one_attribute_should_sel":"至少要選擇一個屬性。","adssp_connection_error_timed_out":"連接逾時","adssp_admin_customize_db_backup_schedule_backup_success":"成功備份資料庫。","adssp_admin_general_adsearch_group_details":"組明細","adssp_popup_common_errormsg_text_no_objs_found":"沒有找到物件。","adssp_admin_server_settings_mail_enter_valid_mail_server":"請輸入有效的郵件伺服器的名稱","adssp_admin_adsearch_select_display_columns":"請為\\'顯示欄\\'選擇一個欄位 。","adssp_admin_restricted_serviceaccounts_msg":"為所選電腦，生成 \\'作為服務登錄 \\'","ads_common_button_ok":"確定","password":"指定的網路密碼不正確","ads_iamapps_config_alert_error_generating_certificate":"生成RSA-SHA證書時出錯。","adssp_admin_restrict_users_alert_searchkey":"請輸入要搜索的字元","adssp_admin_policies_identity_atleast_one_verify_sel":"請選擇一種發送驗證碼的方式(郵件或短信)。","adssp_config_management_custom_text_provide_server_name":"請提供伺服器名","adssp_config_gina_err_logon_failure_target_ac_incorrect_troubleshoot":"1.如果兩個計算機具有相同的主機名稱，則會出現本錯誤資訊。其中一台電腦位於子域內，另一台電腦位於父系網域內。","adssp_domain_user_my_info_letters_alert":"'請在\\\"'+labelName+'\\\"欄位中只輸入字母。' ","ads_admin_product_settings_ha_master_child_notification_failed":"部分集成的元件已經停機，請手動更新這些組件的集成設置。","adssp_domain_user_my_info_phone_num_alert":"'請在\\\"'+labelName+'\\\"欄位中輸入數位，格式為'+format","adssp_admin_policyConfig_thirdStep_edit_ques_deletion":"不能執行刪除，該安全問題已被某個用戶使用。","adssp_config_admin_tools_pwd_sync_no_policy_assigned":"沒有指定策略","adssp_admin_restricted_serviceaccounts_message":"服務帳戶是具有 \\'作為服務登錄 \\'許可權的用戶帳戶。","adssp_common_tree_page_loading_message":"正在載入... ","adssp_admin_config_pwd_sync_automatic_link_alert_zendesk":"如果用戶在AD和Zendesk中的用戶名相同，則其使用者帳戶將自動連結。","adssp_common_text_admin_email_duplicated":"請確保\u201c電子郵寄地址\u201d欄位中沒有重複項。","localDirFormat":"The Specified path is not valid. Enter a valid path using the form :drive-letter:\\directory.","adssp_admin_schedulelist_weekly_schedule":"每週的","ads_admin_product_settings_ha_slave_master_same":"主要伺服器和備用伺服器不能是同一個伺服器。","specifyFirstName":"請指定姓","adssp_enrollment_alert_configure_ga":"您需要配置Google身份驗證器。 ","adssp_admin_logon_customize_dragndrop_htmlarea_fontname_verdana":"Verdana ","adssp_reports_enroll_rep_disenroll_invalid_file_format":"檔案格式無效，請選擇一個CSV檔。","adssp_admin_policies_enroll_settings_conf_mail_server":"請配置郵件伺服器","adssp_admin_logon_customize_dragndrop_htmlarea_fontname_wingDings":"wingDings ","adssp_configuration_gina_pls_select_csv_file":"選擇的CSV檔的欄位不能為空，請重新選擇CSV檔。","adssp_reports_audit_rep_success":"成功","adssp_common_text_hide":"隱藏","adssp_common_alert_mob_format_empty":"手機格式不能為空","ads_admin_product_settings_ha_master_not_running_as_service":"主要伺服器中的{0}沒有以服務形式運行，請將其安裝為服務，然後運行。","ads_admin_product_settings_ha_master_service_success":"成功在主要伺服器上安裝服務，請以服務的形式重啟ADSelfService Plus。","adssp_admin_server_settings_mail_enter_valid_smpp_server":"請輸入有效的SMPP伺服器的名稱。","adssp_admin_server_settings_sms_enter_valid_url":"輸入有效的HTTP URL","ads_support_roboupdate_alert_conf_mail_server":"請配置郵件伺服器的資訊","adssp_domain_user_enrollment_ver_code_dont_leave_emp_mob":"請提供接收驗證碼的手機號，它不能為空。","ads_iamapps_config_alert_no_policy_selected":"至少要選擇一個策略。","adssp_config_admin_tools_pwd_sync_no_hosts_selected":"執行該操作之前請選擇主機","ads_admin_product_settings_ha_slave_down":"備用伺服器中的{0}已經停止，請確保它運行正常。","adssp_admin_gina_uninstall_success":"卸載成功","adssp_admin_logon_customize_dragndrop_htmlarea_underline":"底線","adssp_admin_server_settings_modem_enter_valid_pin_number":"輸入有效的PIN號碼","another_ver":"已經安裝了該產品的其它版本。","adssp_admin_gina_network_path_not_found_troubleshoot":"1.確保該電腦真實存在。<br/><br/>2.確保電腦已正常連接到網路。<br/><br/>3.確保可從安裝本產品的伺服器中ping通該電腦","adssp_config_mfa_edit_ques_complete_before_save":"請完成編輯問題後保存","adssp_admin_gina_err_couldnotconnect_troubleshoot":"1.不能與機器建立連接<br> ","adssp_admin_policies_policy_config_sec_que_you_will_not_need":"如果不啟用\\'重置密碼\\'或\\'解鎖帳戶\\'的功能，則不需要\\'安全問題\\'。","adssp_admin_importanswers_policy_permission":"選擇的策略不適合登記註冊。","access_deny":"訪問被拒絕。","adssp_admin_config_pwd_sync_automatic_link_alert_salesforce":"如果在AD及Salesforce中的用戶名相同，則使用者帳戶將自動連結","adssp_admin_customize_logon_drag_drop_change_pwd":"修改密碼","adssp_admin_server_settings_vpn_enter_disconnect_cmd":"必填欄位不能為空。","ads_admin_product_settings_ha_pip_same_as_master":"虛擬IP位址已被映射到主要伺服器。請嘗試其它IP地址。","adssp_admin_policyConfig_advancedpolicyconfig_alert_message_numberic_val":"請輸入數值","adssp_admin_restricted_deletedusers_msg":"生成活動目錄中（最近90天內）\\'已刪除的用戶列表\\'","specifyLogonName":"請指定登錄名","ads_iamapps_config_alert_success_configuration":"設置保存成功。","adssp_admin_gina_couldnot_start_remote_service":"不能啟動遠端服務","ads_admin_product_settings_ha_pip_slave_range_error":"虛擬IP位址必須與備用伺服器的IP位址在同一網段。","adssp_admin_policies_policy_config_apc_reset_unlock_note_content":"必須配置郵件/SMS伺服器的資訊。","ads_iamapps_config_alert_invalid_format":"檔案格式無效","adssp_admin_selfupdate_layout_unable_del_lay_assigned_policy":"不能刪除，因為它已指定給某個/某些策略。","adssp_config_mfa_adsqa_quest_mapped_wf_confirm_change_attr_mapping":"此問題在工作流中使用。確實要更改屬性映射嗎？","adssp_reports_acc_expired_notification_mail_subject":"帳戶過期通知","ads_admin_product_settings_ha_service_failure":"在主要伺服器和備用伺服器上安裝ADSelfService Plus服務失敗。","ads_admin_product_settings_ha_service_success":"服務安裝成功，請同時在主要伺服器和備用伺服器上以服務的形式重啟ADSelfService Plus。","adssp_config_multi_sec_qa_admin_defin_ques_mandatory":"預定義的問題數少於必需的問題數。","adssp_reports_audit_licensedusers_alert_select_user":"選擇要刪除的用戶，以便釋放由他們所佔用的許可數","adssp_configuration_gina_mac_error_no_domain_auth_found":"未找到域的身份驗證詳細資訊","adssp_admin_customize_logon_drag_drop_change_pwd_desc":"更改您的密碼","adssp_domain_user_change_pwd_alert_new_pwd":"請輸入新密碼。","adssp_common_text_atleast_one_domain":"至少要選擇一個域","adssp_admin_policies_scheduler_soon_to_expire_scheduler_name_exist":"您選擇的計畫名已經存在。","adssp_admin_importanswers_invalid_filefromat":"檔案格式不對，輸入CSV檔。","adssp_configuration_gina_mac_error_unsupported_os_troubleshoot":"1. 電腦的OS不支援遠端安裝。<br><br>2.對於Linux設備，請參考'在Linux上安裝代理'。","adssp_configuration_enrollment_under_process":"註冊登記正在處理中","adssp_admin_selfupdate_layout_custom_add_new_field":"新添欄位","adssp_admin_customize_logon_drag_drop_unlock_acc_desc":"解鎖已被鎖定的帳戶","ads_iamapps_title_select_provider":"選擇一個提供商","adssp_config_gina_mac_err_permission_denied":"訪問被拒","adssp_su_custom_field_mapped_in_reports":"不能刪除該屬性，它已被用於報表之中。","adssp_config_su_no_conditionfields":"佈局中沒有可用作條件的欄位。","adssp_admin_policies_identity_more_dup_mobile_attrib":"請移除重複的手機屬性","adssp_config_su_field_duplicate":"欄位名已經存在。","adssp_config_mfa_create_configuration_saml_text":"SAML","ads_layout_admin_logon_settings_saml_tab_need_new_config":"請確保您使用更新的ACS URL或SP中繼資料檔在您的IdP中為ADSelfService Plus創建新的SAML單點登錄配置。","adssp_config_mfa_advanced_alert_random_que_lessthan_total_ADque":"隨機顯示的問題數不應大於已啟用的AD安全問題總數。","adssp_configuration_cached_credentials_invalid_site_name":"請輸入有效的網站名稱","adssp_admin_general_disclaimer_customize_agree_checkbox_content":"我同意","adssp_common_text_selecting_report":"請選擇報表","ads_admin_product_settings_ha_slave_service_not_installed":"備用伺服器的{0}沒有安裝為服務。","adssp_login_tfa_alert_invalid_radius_port":"RADIUS埠號無效。","adssp_user_org_chart_error_try_after_some_time":"出錯了，請稍後再試","adssp_policy_config_apc_notify_ticket_password_content_alert":"請求內容不能包含 %password% 巨集變數","adssp_popup_viewOU_no_ou_tree_page_loading_message_message":"沒有找到組織單位","ads_admin_product_settings_ha_slave_service_privilege_failure":"未能更新備用伺服器上的該服務的使用者憑證，請手動更新後再試。","adssp_admin_settings_connection_ssl_tool_length_512":"RSA金鑰長度必須是512位到32768位，並且必須是64的倍數。","adssp_common_text_enter_field_empty":"該欄位不能為空。","adssp_config_policy_config_advanced_alert_json_format":"請求消息不是json格式","adssp_configuration_admin_tools_ext_dat_source_invalid_port":"您指定的埠不是數值","adssp_js_calendar_today":"今天","adssp_insta_notification_select_atleast_one_type":"選擇至少一個產品通知","ads_iamapps_config_alert_configure_here":"在此配置","ads_admin_product_settings_ha_install_service":"NT服務安裝","validateFullName":"entered has one or more illegal characters + \\ \\\\\\\". These are not allowed. Please remove them and try again.","adssp_reports_to_mgr_only_for_acc_expiry":"只有即將到期的帳戶和已經過期的使用者帳戶的報表才通過郵件發送給用戶的經理。","adssp_admin_customize_db_backup_schedule_err_while_db_backup":"資料庫備份時出錯","adssp_config_policy_config_advanced_alert_min_length_greater":"最小長度大於最大長度","ads_admin_product_settings_ha_slave_db_connection_error":"不能從備用伺服器連接資料庫。","adssp_admin_gina_err_could_not_install_client_software":"不能安裝用戶端軟體","adssp_admin_gina_err_in_progress":"正在處理另一個安裝過程。","adssp_configuration_gina_mac_alert_restart_linux":"成功安裝登錄代理後，所選的Linux機器將自動重新啟動。確定要繼續嗎?","adssp_configuration_sulayout_phone_format_allowed_value_comma":"用英文逗號分隔多個值","adssp_common_text_no":"不","adssp_configuration_ppe_remove_dict":"移除字典","adssp_domain_user_reset_qa_sel_ques_alert_ans_mismatch_verify":"答案不對，請檢查","adssp_admin_logon_customize_dragndrop_htmlarea_bullet_list":"符號清單","ads_iamapps_config_alert_file_invalid":"請上傳有效的文件","adssp_config_step_run_now":"立即運行","adssp_js_calendar_wednesday":"星期三","adssp_config_admin_tools_helpdesk_select_attribute":"選擇屬性","adssp_configuration_add_new_technician":"新添技術員","adssp_configuration_ppe_max_pal":"請為最大密碼長度輸入大於1的值，或者禁用順讀和倒讀都一樣的詞語規則。","ads_admin_smartcard_browse_certificate_file":"流覽證書文件","adssp_admin_configuration_tech_alert_cannot_reset_logon_domain_usr":"正在登錄時，不能重置您的密碼。","adssp_admin_settings_connection_keystore_encryption_enable_ssl":"啟用本選項之前請確保已經啟用SSL。","adssp_js_calendar_friday":"星期五","pwdNomatch":"The passwords do not match.","adssp_ajaxerror_retrieve_xmldata":"獲取XML資料時出錯: ","adssp_common_alert_enter_positive_no":"請輸入正的整數。","adssp_license_expiry_specify_days":"請指定發送許可即將過期通知的天數。","adssp_config_policy_config_advanced_alert_enter_positive_integer":"請輸入正的整數。","adssp_admin_server_settings_vpn_pre_shared_key":"請輸入VPN的預共用金鑰","day_thu":"Thu","adssp_config_policy_config_advanced_alert_script_content_password":"帳戶鎖定指令碼命令不支持 %password% 宏。","adssp_common_alert_search_pls_enter_text":"請輸入要搜索的內容","adssp_js_calendar_day_tue":"二","adssp_configuration_cached_credentials_client_location_msg_sonic_wall":"例如: C:\\\\Program Files (x86)\\\\Sonicwall\\\\SSL-VPN\\\\NetExtender\\\\necli.exe ","adssp_domain_user_my_info_allowed_phone_num_format_alert":"'在\\\"'+labelName+'\\\"欄位中，以定義的格式，輸入數值'  ","adssp_configuration_quick_enrollment_need_csv_file":"請選擇一個CSV檔","adssp_admin_policies_policy_config_apc_reset_unlock_configure":"配置","ads_admin_product_settings_ha_master_inbuilt_db_mismatch_error":"當內置資料庫為MySQL資料庫時不能設置高可用性。","adssp_search_term_limit_characters":"在搜索中少輸入一些字元。","ads_admin_product_settings_ha_server_restart_now":"立即重啟","adssp_domain_user_reset_qa_sel_ques_alert_double_byte":"在答案中請只使用字母、數位和標點符號。","adssp_su_custom_field_in_layout":"不能刪除，該欄位在自助更新佈局中已被使用。","ads_admin_product_settings_ha_enabled":"高可用性設置已經配置成功。要使設置生效必須重啟ADSelfService Plus服務。","adssp_admin_gina_no_product":"產品被卸載","sunday":"Sunday","adssp_admin_selfupdate_layout_custom_field_in_use":"已被使用，不能刪除它。","valueRange2":"值的範圍為： 0 - 24855.","valueRange1":"值的範圍為： 0 - 2097151.","adssp_config_policy_config_advanced_alert_enter_vclength_maxsize":"驗證碼的最大長度為10。","adssp_admin_policyConfig_edit_ques_enter_valid_que":"輸入有效的問題","adssp_admin_gina_another_ver_troubleshoot":"1.該機器中已經安裝了本產品的其它版本。<br>2.請從該機器卸載對應的GINA/CP。","adssp_admin_policies_identity_text_contain_macro":"請修改消息的內容，以便包含 {0} 宏。","ads_admin_product_settings_ha_master_slave_domain_error":"只能對處於同一域的主要伺服器和備用伺服器啟用高可用性。","adssp_admin_logon_customize_dragndrop_htmlarea_fontname_georgia":"Georgia ","adssp_admin_gina_couldnot_parse_server_install_path_troubleshoot":"不能解析伺服器的安裝路徑","adssp_admin_self_update_layout_name_exists":"該佈局名已經存在。","ads_admin_product_settings_ha_master_service_failure":"未能在主要伺服器上將ADSelfService Plus安裝為服務。","ads_support_roboupdate_upgrade_now":"<span class=adsfontSize12><b>更新中...<\/b><\/span><br/><span class=adsgrayfont>請關閉本視窗，並在安裝了ADSelfService Plus的機器上，檢查更新的進程。<\/span>  ","adssp_domain_user_change_pwd_alert_confirm_pwd":"請再次輸入密碼","adssp_admin_gina_err_could_not_install_client_software_troubleshoot":"1.安裝用戶端軟體時發生網路超時。<br/><br/>2.請稍後再安裝/重新安裝","adssp_js_calendar_monday":"星期一","set_attribute":"設置屬性時出錯","adssp_admin_settings_connection_ssl_tool_country_code_alert":"確保國家代碼為2個字元!","adssp_admin_policies_policy_config_apc_pwd_pol_palindrome":"'- 不能包含正讀和反讀都一樣的字串' ","adssp_admin_customize_db_backup_schedule_hide_text":"隱藏","display":"Display","ads_iamapps_config_alert_confirm_delete_multiple":"真的要刪除該系統組態嗎？","adssp_common_alert_select_user":"請選擇一個使用者/組織單位","ads_installation_mdm_pushmanager_settings_app_installation_error":"將安裝命令發送到MDM伺服器時出錯。<br>請稍後重試。","adssp_config_step_notify_pwd_expired_users":"通知密碼過期的使用者。","adssp_common_text_alloulist":"所有組織單位","adssp_admin_server_settings_modem_enter_valid_port_speed":"輸入有效的埠速度","adssp_admin_server_settings_push_register_enter_valid_domain_name":"請輸入一個功能變數名稱","adssp_admin_configuration_tech_alert_enter_valid_password":"請輸入密碼","adssp_configuration_sulayout_initial_value_valid_email":"初始值應該是有效的電子郵寄地址","adssp_configuration_ppe_enter_pass_phrase_len_greater_than_min_len":"請確保要求的密碼長度大於要覆蓋的所有密碼複雜度規則中的最小長度要求。","ads_support_roboupdate_loading":"載入中...  ","adssp_config_mfa_create_configuration_yubikey_text":"Yubikey","adssp_config_management_custom_text_dont_give_spaces_server_name":"伺服器名稱中不能包含空格字元。","adssp_mobile_settings_button_text_alert_message":"文本的長度範圍為1 - 50個字元。","adssp_configuration_cached_credentials_invalid_file_loc":"無效的檔路徑。","adssp_reports_sqa_rep_search":"搜索名稱或問題","adssp_config_mfa_create_configuration_saml_confirm_text":"您希望在登錄SSO設置中映射此配置嗎?","adssp_admin_config_mfa_disable_push_auth":"禁用推送通知驗證","adssp_admin_general_custom_disp_settgs_alert_access_control_with_range":"真的確定要設置訪問管理員登錄頁面的許可權，給：{0}<br><b>唯一地址<\/b><br><div style='color:FF3434;overflow:auto;max-height:110px;height:110px;'>{0} <\/div>{0} <br><b>地址範圍e<\/b><br><div style='color:FF3434;overflow:auto;max-height:110px;height:110px;'>{0} <\/div>  ","adssp_common_layout_footer_feedback_enter_valid_mail":"請輸入有效的電子郵寄地址。","adssp_alert_license_apply_professional_edition":"此功能是ADSelfService Plus專業版的專有功能。如果您希望使用此功能，請購買\\\"專業版\\\"。","adssp_admin_adsearch_select_field":"請選擇欄位到\\'已選欄目\\'中。 ","report_user_account_status_enabled":"已啟用","adssp_common_text_select_notify_method":"至少要選擇一種通知方法","admin_schedule_reports_page_once_in_hr":"每次都在","adssp_common_text_edit":"編輯","adssp_login_tfa_alert_pin":"PIN碼","ads_admin_logon_settings_smartcard_settings_enable_success":"智慧卡已成功啟用。","adssp_admin_policies_identity_atleast_one_identity_sel":"至少要選擇一種身份認證方法。","adssp_admin_configuration_tech_alert_change_pwd":"重置密碼","adssp_admin_gina_network_path_invalid_cred":"沒有找到網路路徑或者憑證無效。","adssp_domain_user_my_info_allowed_phone_num_alert":"'以管理員定義的格式在 \\\" '+標籤名+'\\\" 欄位中輸入號碼'","adssp_reports_enroll_rep_disenroll_mobile_number":"手機號碼","LeastOneDC":"Atleast one Domain Controller should be added","adssp_common_layout_footer_feedback_estimated_time_enter_purchase_time":"請選擇預估的購買時間","ads_iamapps_custom_saml_choose_category":"請選擇應用分類","adssp_config_gs_selected_ous":"組織單位中的所有使用者","adssp_su_custom_field_mapped_in_policy_filter":"不能刪除，該屬性已被用在使用者過濾條件中","adssp_admin_gina_another_ver":"已經按照本產品的其它版本。","adssp_login_admin_page_script_alert_enter_valid_pwd":"Please enter valid password to proceed","adssp_domain_user_my_info_photo_upload_invalid_format":"檔案格式無效","adssp_config_gina_err_could_not_connect_adminshare_enable":"1.要通過本產品安裝GINA，運行本產品的帳戶最好是管理員帳戶。<br/><br/>2.如果產品運行為獨立應用（非服務啟動），請確保在\\\"域設置\\\"頁面配置了合適的管理員帳戶及其密碼。<br/><br/>3.如果產品以Windows服務啟動，請確保該服務的\u201c登錄\u201d屬性中設置了合適的管理員帳戶及其登錄憑據。<br/><br/>3.ADMIN$共用可能沒有啟用。","ads_iamapps_config_alert_no_hosts_provider":"沒有 {PROVIDER} 的系統組態","adssp_config_quick_enroll_sceduler_name_empty":"計畫名稱為空","adssp_admin_policies_scheduler_soon_to_expire_config_mail_server":"尚未配置郵件伺服器，請首先使用'伺服器設置'配置好郵件伺服器。","ads_admin_logon_settings_smartcard_settings_delete_confirm":"真的確定要刪除該智慧卡的配置嗎？","adssp_admin_customize_logon_drag_drop_self_upd_desc":"更新自己的聯繫資訊","adssp_admin_server_settings_mail_enter_valid_smpp_port":"請輸入有效的SMPP伺服器的埠。","adssp_admin_policyConfig_thirdStep_edit_ques_cannot_del_futher":"'管理員定義的最少問題個數為'+var1+'，不能再刪除了。' ","adssp_admin_policies_policy_config_alert_min_que_less_max_que":"問題的最小字元數應該小於最大字元數。","adssp_admin_customize_logon_drag_drop_save_confirm":"它將更改域使用者的登錄頁面，確定要繼續嗎? ","adssp_admin_customize_logon_drag_drop_valid_image_file":"請輸入合適的影像檔","ads_admin_product_settings_ha_slave_cluster_exists":"備用伺服器上已經設置了高可用性。","ads_admin_product_settings_ha_slave_invalid_credential":"請輸入備用伺服器的有效憑證。","ads_common_text_update":"更新 ","ads_admin_product_settings_ha_not_running_as_service":"主要伺服器和備用伺服器中的{0}都必須以服務的形式運行。","adssp_configuration_gina_mac_error_authorization_failed":"服務帳戶的許可權不足。","adssp_configuration_cached_credentials_invalid_connection_name":"請輸入有效的連接名稱","adssp_configuration_ppe_dict_file_name_not_contain_comma":"字典檔案名中不能包含逗號。","adssp_tab_license_alert_click_upgrade_message_txt":"請在指定許可檔之後，點擊'應用許可'按鈕。","adssp_admin_server_settings_empty_attr_value":"不能添加空的屬性","adssp_admin_selfupdate_layout_positive_photo_dimension":"尺寸應該是正的數值","ads_admin_product_settings_ha_virtual_slave_same":"虛擬主機名稱不能與備用伺服器的主機名稱相同。","ads_iamapps_custom_saml_alert_update_custom_app_config":"編輯該應用的設置，將禁用為該域配置的單點登錄設置，繼續嗎？","adssp_configuration_admin_tools_ext_dat_source_name_already_exist":"所選的名稱已經在用，請選擇其它名稱。","adssp_admin_policies_gina_install_text_stopprocess":"正在處理停止動作，該批次處理完成可能需要花費幾分鐘時間。(安裝是按照電腦的順序連續進行)。","adssp_admin_policies_scheduler_soon_to_expire_config_sms_server":"必須配置SMS伺服器。才能發送短信。請首先配置'伺服器設置'。","adssp_config_gina_option_windows_mac":"此選項適用於Windows和macOS。","adssp_common_alert_empty_no_of_days":"天數不能為空。","adssp_admin_policies_policy_config_alert_min_ans_less_max_ans":"答案的最小字元數應該小於最大字元數。","adssp_config_gina_mac_err_couldnt_copy_linux_tar":"無法複製ADSSPLinuxClient.tar.gz","computerNameValidation":"輸入的電腦名包含特殊字元，如：/ \\ [ ] : | = + * ? < > \\\\\\\"，不允許有特殊字元，請刪除之後重試。","ads_iamapps_config_alert_invalid":"無效","admin_policy_alert_already_mapped":"所選的組織單位(OU)中已經有其它策略，因此不能再應用其它策略","adssp_config_gina_mac_err_couldnt_copy_pkg":"不能複製ADSelfServicePlusMacLoginAgent.pkg ","adssp_configuration_ppe_default_dictionary_cannot_delete":"不能刪除預設的字典。","adssp_ember_server_enter_new_attr":"輸入新屬性 ","adssp_config_invalid_yubikey_client_id":"無效Yubico用戶端ID","adssp_common_text_no_changes_found":"沒有找到可以保存的更改。","adssp_admin_gina_network_path_invalid_cred_troubleshoot":"1.請配置具有管理員許可權的認證憑據。如果以應用的方式啟動本產品，請在域設置頁面設置合適的管理員帳戶；如果以Windows服務啟動本產品，請在該服務的登錄選項中設置合適的管理員憑據。<br/><br/>2.檢查是否已經啟用Admin共用。","adssp_admin_server_settings_mail_server_not_configured":"尚未配置郵件伺服器的資訊。","ads_iamapps_config_alert_success_generating_certificate":"成功生成RSA-SHA證書。","adssp_configuration_ppe_choose_dict_file":"已選定字典規則，但是沒有選擇字典檔。","adssp_js_calendar_day_fri":"五","ads_admin_product_settings_ha_settings_already_disabled":"高可用性已處於禁用狀態。","adssp_common_text_selected_no_options":"沒有選擇任何選項。","adssp_config_emp_search_showonly_forest_tooltip":"如果已經啟用本選項，推薦您禁用<b>在登錄頁面顯示員工搜索和組織架構圖<\/b>的選項，否則該使用者可以在搜索結果中查看來自其它域森林的使用者資訊。","removeMailSplChar":"輸入的電子郵件包含特殊字元，請刪除之後重試。","adssp_config_mfa_create_configuration_duo_text":"Duo","adssp_admin_dc_config_save_couldnt":"不能保存配置，請稍後重試。","adssp_config_gs_alert_group_already_selected":"組 <b>{0}<\/b>, 已經選到訂閱中，名稱 <b>{1}<\/b>.","adssp_enrollment_alert_configure_vc":"您需要配置驗證碼","nextYr":"Next year (hold for menu)","ads_admin_product_settings_ha_exception":"不能保存變更，請稍後再試。或者聯繫技術支援。","adssp_configuration_cached_credentials_client_location_msg_open_vpn":"例如: C:\\\\Program Files (x86)\\\\Sophos\\\\Sophos ssl client\\\\bin\\\\openvpn.exe ","adssp_admin_logon_customize_dragndrop_htmlarea_fontname_arial":"Arial ","adssp_admin_general_hide_admin_div_block_disable_sso":"您必須先禁用單點登錄設置，然後才能禁用\u201c隱藏自助服務管理員登錄\u201d。","adssp_reports_enroll_rep_disenroll_sam_account_name":"SAM帳戶名稱","adssp_admin_policies_policy_config_apc_reset_unlock_enforce_pwd_his_ch_pwd_flag":"要使用該功能，必須禁用使用者不能更改密碼的標誌","adssp_domain_user_my_info_mandatory_alert":"必填欄位不能為空。","ads_installation_mdm_pushmanager_settings_app_status_update_error":"將應用程式狀態更新命令發送到MDM伺服器時出錯。<br>請稍後重試。","ads_common_button_cancel":"取消","adssp_login_splchar_notallow":"不允許使用以下特殊字元（/ \\ [ ]: ; | = + * ? < > @ \\\\\\\"） ，請去掉後再試。","adssp_admin_config_mail_attachment_size_limit":"附件大小超過25MB，請確保選擇的檔大小不超過大小限制。","ads_admin_product_settings_ha_invalid_credential":"請輸入有效的憑證。<br><b>注意:<\/b> 主要伺服器和備用伺服器中的超級管理憑據應該是相同的。","adssp_admin_logon_customize_dragndrop_htmlarea_fontname_tahoma":"Tahoma ","adssp_admin_config_mfa_modify_yubikey_auth":"修改","adssp_admin_logon_customize_dragndrop_htmlarea_fontname_serif":"Serif ","ads_configuration_mdm_no_details":"繼續之前，請輸入有效的資訊","adssp_admin_general_add_custom_attributes":"通過在ADSelfService Plus中配置自訂屬性添加新的登錄屬性。","adssp_admin_policies_policy_config_apc_pwd_pol_use_num":"'- 應該同時包含數字' ","adssp_admin_customize_logon_drag_drop_image_duplicate":"同名的圖片已經存在，請提供不同的名稱","ads_admin_product_settings_ha_settings_not_configured":"設置尚未保存。","ads_iamapps_config_alert_invalid_subdomain_name":"無效的子功能變數名稱","adssp_configuration_ppe_min_no_of_condn_positive":"複雜度要求的數位必須是正數，且不能大於所選複雜度策略中規定的數值。<br> <br> <b>注意：<\/ b>字典、花樣、覆蓋及歷史規則不會包含在要滿足的最少條件數內。","adssp_config_gs_select_groups":"點擊 + 圖示選擇郵件組。","ads_layout_admin_logon_settings_saml_tab_photo_upload_dimensions_exceeded":"請確保IdP的標誌圖示的尺寸不超過50x17。","adssp_admin_restricted_deletedusers_days":"最近{0}天刪除的用戶。","adssp_config_gina_err_logon_failure_unknow_user_bad_pwd":"登錄失敗: 用戶名或密碼不正確","adssp_configuration_ppe_dict_name_exists":"上傳的字典名稱與已有的自訂字典相同。","adssp_configuration_enrollment_action_completed":"註冊登記動作完成","adssp_admin_customize_rebranding_provided_title_img_size_exceeds":"圖像大小超出了允許的限制：200kb。請上傳一張較小的圖片。","adssp_config_admin_tools_pwd_sync_already_enabled":"所選的主機已經啟用","adssp_admin_customize_logon_valid_text":"請輸入有效的文字","adssp_admin_policyConfig_thirdStep_edit_ques_reset_mandatory":"重置必填項","adssp_login_user_page_domain_user_text_loading":"正在載入... 請稍候","adssp_common_layout_footer_feedback_enter_name":"請輸入姓名/名稱","adssp_admin_enrollment_notification_push_notification_text_blank_text_fields":"請不要讓該欄位為空","wednesday":"Wednesday","adssp_admin_customize_rebranding_provide_title_img_in_ico":"請提供.ico格式的\\'流覽器標題圖片\\'","networkDirFormat":"指定的路徑無效。請輸入有效的網路路徑，如: \\\\server\\share\\folder。","adssp_admin_configuration_tech_alert_cannot_reset_domain_usr_pwd":"不能重置域使用者的密碼","adssp_domain_user_reset_qa_dont_use_words":"答案中不能使用問題中的詞","adssp_config_gina_cp_install_stopped":"處理進程被使用者終止。","adssp_admin_config_mfa_enable_fp_auth":"啟用指紋身份驗證","adssp_login_admin_page_script_alert_browser_info":"您使用的流覽器本產品不支援，請最好使用Internet Explorer 6.0+, Netscape 7.0+, Mozilla 1.5+. ","ads_admin_product_settings_ha_slave_wrong_product":"不能在兩個不同的產品上啟用高可用性。","thursday":"Thursday","adssp_common_text_selecting_policy_man":"選擇一個策略。","month_oct":"October","adssp_common_text_success_update":"更新成功","adssp_admin_policyConfig_thirdStep_sel_ous":"沒有選擇任何組織單位。","adssp_config_management_gina_custom_restrict_bad_cert_tip":"啟用後，如果產品中應用的SSL證書無效，使用者將無法從登錄螢幕訪問密碼自助服務嚮導。","adssp_admin_gina_no_product_troubleshoot":"產品已被卸載","ads_admin_smartcard_please_enter_ad_mapping_attribute":"請選擇一個AD中的映射屬性。","adssp_configuration_cached_credentials_client_location_msg_fortinet":"例如: C:\\\\Program Files (x86)\\\\Fortinet\\\\FortiClient\\\\FortiSSLVPNclient.exe ","adssp_js_calendar_selectDate":"選擇日期","adssp_admin_settings_connection_ssl_tool_enter_all_fields":"請輸入所有欄位","adssp_admin_config_pwd_sync_automatic_link_alert_openldap":"如果在AD和OpenLDAP中的用戶名是相同的，則會自動連結其使用者帳戶。","ads_support_roboupdate_downloading":"<span class=adsfontSize12>正在下載更新...<\/span>  ","adssp_common_text_valid_url":"請輸入有效的URL ","adssp_admin_config_pwd_sync_automatic_link_alert_google":"如果在AD及Google App中的用戶名相同，則使用者帳戶將自動連結","adssp_configure_auto_enroll_schedule_cancel_confirm":"尚未保存所做的變更，繼續嗎？","adssp_admin_config_mfa_disable_fp_auth":"禁用指紋身份驗證","adssp_domain_user_enrollment_ver_code_dont_leave_emp_mail":"請提供接收驗證碼的郵箱地址，它不能為空。","adssp_admin_server_settings_mail_enter_valid_sms_content":"請輸入有效的郵件內容","adssp_configuration_ppe_enter_restricted_patterns":"請輸入限制的式樣。","adssp_admin_policies_policy_config_apc_reset_unlock_enforce_pwd_his_info1":"由於活動目錄的設計原因，不肯在重置密碼的時候，去檢查\\\"密碼歷史的設置\\\"。","adssp_admin_product_settings_server_settings_enter_vaild_email":"請輸入有效的電子郵寄地址。","adssp_admin_policies_policy_config_apc_reset_unlock_enforce_pwd_his_info2":"因此，系統會嘗試收集該使用者的舊的密碼，或為其指派一個臨時密碼。這樣啟用檢查密碼歷史的設置。","adssp_domain_user_reset_qa_dont_use_username_ans":"不要使用您的名字作為答案。","adssp_configuration_gina_mac_error_unsupported_os":"操作失敗：不支持的OS","adssp_admin_policies_policy_config_apc_reset_unlock_enforce_pwd_his_info3":"或者, 只針對密碼過期的使用者啟用該功能。","adssp_admin_policies_policy_config_apc_reset_unlock_enforce_pwd_his_info4":"要使該功能正常工作，需要：將密碼最短使用壽命置為0，並且禁用使用者不能修改密碼的選項","ads_admin_product_settings_ha_pip_already_used":"虛擬IP位址已被映射到其它元件。請嘗試其它IP地址。","adssp_admin_policies_enroll_settings_alert_enter_email_add":"請輸入使用者的電子郵寄地址（用逗號分隔多個位址）。","adssp_common_text_popuptitle_select_groups":"選擇組","adssp_config_admin_tools_helpdesk_que_mapped_in_mfa_unable_to_delete":"無法刪除此問題。此問題已在多因素身份驗證下的AD安全問題方法中使用。請禁用此問題，然後重試。","month_sep":"September","ads_iamapps_cert_generating_btn":"正在生成證書...","adssp_configuration_selfservice_password_expiry_push_notification_message_limit_exceeded":"消息大小超過限制","adssp_domain_user_reset_qa_sel_ques_text_the_ans_should_between_to_chars_long":"'答案的長度應該為'+ var1 +' - '+ var2 +'個字元' ","admin_policy_alert_selecting_ous":"必須選擇一個組織單位(OU)。","selectADObject":"選擇一個AD物件。","adssp_config_su_layout_phone_format_title_txt":"當指定多種電話格式時，將不會按格式顯示對應的欄位，而會顯示文字方塊。","adssp_config_step_notify_acc_expired_users":"通知帳戶過期的用戶。","adssp_config_admin_tools_helpdesk_alert_select_policy":"至少選擇一個策略。","adssp_login_tfa_alert_confiure_sms_server_new":"必須配置SMS伺服器。請首先到'伺服器設置'頁面配置相應的資訊。是否不保存變更而繼續？","adssp_config_sulayout_rule":"規則","no_host":"不能找到主機","adssp_configuration_cached_credentials_client_location_msg_cisco_anyconnect":"例如: C:\\\\Program Files (x86)\\\\Cisco\\\\Cisco AnyConnect\\\\vpncli.exe ","adssp_config_mfa_adsqa_quest_mapped_wf_cannot_delete_question":"無法刪除此問題。它正在審批工作流中使用。","adssp_admin_sso_hide_admin_login_alert":"在啟用單點登錄設置之前，您必須先啟用\u201c隱藏自助服務管理員登錄\u201d設置。","adssp_common_text_summary_view":"概要視圖","adssp_configuration_sulayout_manager_default_value":"用戶的DN","adssp_common_text_enter_valid_email":"輸入有效的郵寄地址","adssp_reports_acc_expired_notification_mail_content":"您好 %userName%,\\n\\n您的帳戶將在 %dateTime% 過期。請聯繫您的管理員。\\n\\n謝謝\\n管理員","adssp_admin_policies_policy_config_status_unable_message":"不能刪除！對於配置的域必需保留至少一個策略。","adssp_enrollment_alert_configure_sqa":"您需要配置安全保密問題及答案","adssp_reset_unlock_accounts_secret_ques_alert_enter_ans":"請輸入您的答案","adssp_admin_logon_customize_dragndrop_htmlarea_fontname_wide":"寬體","adssp_reports_enroll_rep_disenroll_select_users":"選擇要除名的用戶","adssp_admin_config_pwd_sync_automatic_link_alert_oraebs":"如果在AD及Oracle E-bussiness套件中的用戶名相同，則使用者帳戶將自動連結","adssp_configuration_gina_mac_error_agent_configuration":"由於登錄代理配置不當，操作失敗。","adssp_admin_general_custom_disp_settgs_alert_valid_domain":"請選擇域後繼續","ads_configuration_mdm_not_valid_host":"無效伺服器名稱或IP。<br/>請使用其他伺服器名稱重試。","ads_admin_product_settings_ha_server_restart_later":"稍後重啟","adssp_config_policy_config_advanced_alert_random_que_lessthan_total_que":"隨機顯示的問題數不能大於問題總數。","adssp_domain_user_cp_rp_pwd_strength_too_short":"太短","adssp_select_changepwd_cannotchange_atonetime":"對於同一個用戶，不能同時選擇'用戶下次登錄時必須修改密碼'和'使用者不能修改密碼'兩個選項。","ads_admin_product_settings_ha_slave_service_privilege_success":"備用伺服器上的ADSelfService Plus服務的使用者憑證已更新成功。請重啟該服務已使變更生效。","adssp_reports_audit_licensedusers_alert_select_user_cannot_reset":"無法重置所選用戶的免責聲明狀態。","adssp_admin_server_settings_mail_enter_valid_smpp_cred":"SMPP憑證不能為空 ","adssp_export_settings_no_changes_found":"沒有找到修改。","adssp_admin_policyConfig_thirdStep_edit_ques_force_ques":"您正嘗試選擇更多問題","adssp_admin_policies_policy_config_apc_reset_unlock_enforce_pwd_change_pwd":"要使該功能起效，應該禁用\u201c使用者不能更改密碼\u201d的選項。","adssp_tab_license_alert_enter_proper_license":"請輸入合適的許可檔。","adssp_su_custom_field_cant_delete_mapped_in_ad_question":"無法刪除此屬性。可以針對多因素身份驗證或審批工作流中的AD安全問題進行配置。","adssp_login_pwdexpiry_cannotchangepwd":"您已經選擇了'密碼永不過期'。使用者下次登錄時不需要修改密碼。","ads_iamapps_config_alert_delete":"刪除","pwdExpiry":"You have selected 'Password never expires'. The user will not be required to change the password at next login.","month_jul":"July","adssp_admin_policies_policy_config_apc_pwd_pol_spl_ch":"'- 應該至少包含 '+val+' 個特殊字元' ","month_jun":"June","adssp_admin_selfupdate_layout_do_you_want_to_del":"真的要刪除它嗎? 刪除點擊\\'確定\\'，不刪點擊\\'取消\\'按鈕。","adssp_admin_server_settings_vpn_enter_server":"請輸入VPN的伺服器名稱","ads_admin_product_settings_ha_entries_not_null":"必填欄位不能為空。","adssp_admin_config_mail_attachment_file_not_supported":"檔不支援","adssp_admin_selfupdate_layout_drop_down_box":"下拉清單","ads_iamapps_custom_saml_alert_delete_custom_app_config":"刪除該應用，也將同時刪除該域的單點登錄配置，繼續嗎？","adssp_admin_schedulelist_monthly_schedule":"每月的","adssp_config_policy_config_advanced_alert_enter_positive_no":"請輸入正的整數。","ads_configuration_mdm_devices_no_devices_selected":"請至少選擇一個設備","adssp_config_admin_tools_helpdesk_que_mapped_in_mfa_confirm_change_attr_mapping":"此問題也用於多因素身份驗證。確實要更改屬性映射嗎？","adssp_admin_policy_config_unable_delete_policy":"val+'個用戶已登記註冊，只有在取消註冊之後才能該策略。'","ads_admin_logon_settings_smartcard_settings_partial_success":"要使用智慧卡認證，請至少啟用一個配置。","ads_admin_smartcard_please_select_linked_domains":"請至少選擇一個域。","adssp_admin_customize_db_backup_schedule_try_after_few_minutes":"請稍後幾分鐘再試。","adssp_admin_adsearch_select_search_criteria":"請為\\'搜索條件\\'選擇欄位 。 ","admin_schedule_reports_for_x_days":"'-'+field+'天' ","admin_policy_alert_policy_name_mandatory":"輸入策略名，並選擇一個策略。","adssp_common_alert_select_group_ou":"請選擇一個組/組織單位","adssp_domain_user_enrollment_ver_code_enter_mail_id":"輸入您的電子郵寄地址","adssp_admin_server_settings_vpn_enter_domain_name":"必填欄位不能為空。","adssp_domain_user_reset_qa_sel_ques_alert_ans_should_be":"答案應該是","adssp_common_text_selecting_domain_man":"必需選擇域。","adssp_common_text_cannot_moveup":"不能再上移了","adssp_reports_enroll_rep_non_enroll_show_notified":"只顯示已經通知的使用者","adssp_login_tfa_alert_choose_verify_mode":"請至少要選擇一種驗證碼的方法。","adssp_configuration_cached_credentials_site_name":"VPN網站名稱","adssp_admin_selfupdate_layout_custom_field_ldap_name_already_exist":"屬性LDAP名稱已經存在。","ads_layout_admin_logon_settings_saml_tab_photo_upload_invalid_format":"請確保IdP的標誌圖示採用JPG，JPEG，GIF，PNG或BMP圖像格式。","adssp_common_alert_select_group_sel":"請至少選擇一個組。","adssp_admin_logon_customize_dragndrop_htmlarea_fontsize_6_point":"6 (24 pt)","adssp_admin_policyConfig_thirdStep_edit_ques_max_len":"問題長度不能超過1000個字元。","ads_admin_product_settings_ha_pip_range_error":"虛擬IP位址必須與主要伺服器和備用伺服器的IP位址在同一網段。","adssp_configuration_soon_to_exp_disable_html":"禁用HTML","adssp_domain_user_cp_rp_pwd_strength_good":"很好","nextMon":"Next month (hold for menu)","SelectQues":"Please select a question.","adssp_config_gina_bypass_on_adssp_server_unreach":"如果您取消選中該選項，則當ADSelfService Plus沒有運行時，用戶將不能登錄。","adssp_admin_customize_db_backup_schedule_next_db_backup_text":"<span class=\\\"redtxtbold\\\">提示: <\/span>下次備份至少要等待1分鐘之後","adssp_config_mfa_adsqa_quest_mapped_wf_confirm_edit_text":"此問題在工作流中使用。確實要編輯該問題嗎？","adssp_common_text_no_ou_selected":"沒有選擇組織單位，請選擇組織單位後再繼續處理。","adssp_configuration_task_scheduler_pre_requisite_enable":"要啟用提醒，至少要配置一個重置密碼或解鎖帳戶的策略。","ads_iamapps_config_alert_error_configuration":"啟用了SSO的App不能禁用自動連結","adssp_configuration_cached_credentials_client_location_msg_sonic_wall_global_vpn":"例如: C:\\\\Program Files (x86)\\\\SonicWall\\\\SonicWall Global VPN\\\\swgvc.exe ","adssp_admin_general_custom_disp_settgs_alert_access_control_admin_login_small_ip":"結束IP位址小於起始IP位址","adssp_popup_common_search_errormsg_text_no_objs_found_refresh":"沒有找到匹配的物件。請刷新後再重新搜索。","adssp_admin_gina_err_in_progress_troubleshoot":"1.其它的安裝進程正在運行。<br>2.請稍後再重試安裝","calendarFlat":"日曆設置:  雖然指定了Flat，但是不能找到其父節點","adssp_config_mfa_adsqa_edit_ques_mandatory":"設置為必填項","ads_admin_product_settings_ha_update_service_cred":"更新服務憑證","ads_admin_smartcard_please_import_root_ca_certificate":"請導入有效的CA根證書。","adssp_admin_restricted_unowned_msg":"1.有使用者帳戶已經從活動目錄中刪除，但仍然佔用著許可。","adssp_config_admin_tools_pwd_sync_already_disabled":"所選的主機已被禁用","ads_configuration_mdm_deviceenrollment_warning_mail_sent":"未能向某些使用者發送電子郵件通知。 ","adssp_admin_policies_enroll_settings_separated_commas":"可以在這裡輸入電子郵寄地址，用逗號分隔。","adssp_configuration_ppe_enter_positive_number_for_pass_phrase_len":"請為要覆蓋所有複雜性規則中的密碼長度輸入值，範圍為：1 - 127。","adssp_admin_selfupdate_layout_custom_field_already_exist":"屬性已經存在。","adssp_admin_policies_soon_to_expire_upload_text":"沒有選擇檔","adssp_configuration_gina_invalid_csv_file_format":"檔案格式不對，請輸入CSV檔","adssp_config_admin_tools_helpdesk_policy_permission_alert":"以下動作不在該策略的範疇之內：","ads_common_alert_search_pls_enter_text":"請輸入要搜索的文字","admin_policy_alert_name_already_use":"您選擇的策略名已被使用，請選擇其它策略名。","adssp_login_tfa_alert_passcode_mandatory":"請提供有效的密碼。","adssp_configuration_cached_credentials_client_location_msg_checkpoint":"例如: C:\\\\Program Files (x86)\\\\CheckPoint\\\\Endpoint Connect\\\\trac.exe ","adssp_admin_domain_settgs_leastonedc":"請至少選擇一個網域控制站","adssp_js_calendar_day_sat":"六","ads_admin_logon_settings_smartcard_settings_delete_config":"刪除智慧卡","adssp_configuration_ppe_only_for_rp":"該規則只適用於密碼重置。它不適用於更改密碼以及從活動目錄的使用者及電腦介面重置密碼。","ads_admin_product_settings_ha_server_restart_later_note":"請確保先重啟主要伺服器，然後在重啟備用伺服器。","adssp_configuration_ppe_unic_tip":"萬國碼（Unicode）字元包含非英文字元 (ギ ,羊 ,ما ), 符號(✓ ,✖ ,¿ ,¡ )等。","adssp_config_mfa_advanced_mail_format_invalid":"請輸入有效的功能變數名稱稱。","adssp_admin_general_custom_disp_settgs_alert_valid_captcha_refreshtime":"請為重置無效登錄次數所需的時間間隔指定一個大於0的整數（分鐘數）。","ads_admin_smartcard_disabling_tohttp_will_disable_smart_card_logon_also":"禁用HTTPS將禁用智慧卡登錄。","admin_common_text_confirm_msg_r_u_sure_delete":"真的要刪除嗎 ? ","adssp_admin_logon_customize_dragndrop_htmlarea_fontsize_1_point":"1 (8 pt)","AllOUList":"All","adssp_js_calendar_prevYr":"上一年(下拉式功能表)","adssp_config_sulayout_manager_field":"經理欄位","adssp_configuration_ppe_enter_positive_number_for_min":"可輸入的密碼最小長度的範圍為1 - 127。","adssp_configuration_selfservice_self_update_create_layout_group":"組 ","adssp_admin_policies_policy_config_apc_pwd_pol_both_case":"'- 應該同時包含大小寫字元' ","rem_com":"返回的遠端命令","ads_admin_product_settings_ha_service_not_installed":"主要伺服器和備用伺服器上的{0}都沒有安裝為服務。","adssp_admin_policy_security_ques_should_not_blank":"安全保密問題不能為空。","adssp_reports_audit_alert_enter_valid_date":"請選擇有效的日期。","adssp_domain_user_my_info_mandatory_alert_new":"請為必填欄位提供相應的值。","adssp_layout_admin_logon_settings_mobile_alert_an_actionbtn_enabled":"您必須啟用重置密碼/更改密碼/解鎖帳戶這幾個動作中的至少一個動作。","adssp_admin_configuration_tech_alert_cannot_reset_password":"正在登錄時，不能重置您的密碼。<br>但是可以通過<br><b>管理 --> 個性化<\/b>進行修改","checkProxyAddress":"當有郵件代理伺服器時，需要選中該選項。","adssp_domain_user_reset_qa_use_different_ans":"對每個問題給出唯一的答案","ads_admin_product_settings_ha_master_bindaddress_error":"當主要伺服器伺服器配置綁定位址時不能設置高可用性。","adssp_admin_config_mfa_enable_mobile_auth":"啟用Google身份驗證","day_sun":"Sun","EnterDC":"Enter the Domain Controller to add","adssp_config_gina_err_logon_failure_unknow_user_bad_pwd_troubleshoot":"1.請配置具有管理員許可權的認證憑據。如果以應用的方式啟動本產品，請在域設置頁面設置合適的管理員帳戶；如果以Windows服務啟動本產品，請在該服務的登錄選項中設置合適的管理員憑據。<br/><br/>2.檢查是否已經啟用Admin共用。","adssp_admin_gina_no_file_troubleshoot":"系統沒有找到所指定的檔","adssp_admin_importanswers_blank_importcsv":"選擇的CSV檔的欄位不能為空，請重新選擇CSV檔。","ads_iamapps_config_alert_max_chars_exceeded_desc":"描述欄位的長度最大不能超過 250 個字元","adssp_configuration_cached_credentials_client_location_msg_custom_vpn":"請提供VPN CLI exe的路徑，例如: C:\\\\Program Files(x86)\\\\Fortinet\\\\Forticlient.exe ","adssp_config_admin_tools_helpdesk_atleast_one_action_need_to_be_selected":"至少要選擇一個動作。","adssp_reports_enroll_rep_enroll_confirm_delete":"真的要取消註冊{0}嗎？請注意，該技術員狀態（如果有的話）也將被刪除。","adssp_config_policy_config_no_policy_for_enrollment":"沒有適合登記註冊用的策略，是否要編輯策略？","adssp_admin_selfupdate_layout_do_you_want_to_delete_msg":"刪除該欄位，也將刪除修改規則中的使用該欄位的條件。繼續嗎？","QAOverWritten":"用戶的問題與答案將會被覆蓋","ads_iamapps_config_alert_sp_logo_upload_size_exceeded":"圖示尺寸超過允許的大小。","adssp_admin_gina_couldnot_start_remote_service_troubleshoot":"不能啟動遠端服務","adssp_admin_config_mail_attachment_remove_failure":"刪除檔時出錯","adssp_admin_configuration_tech_alert_mandatary_user_account":"不能更改缺省的Admin帳戶的角色。","adssp_admin_policies_policy_config_alert_positive_no_char_ans_que":"請設置使用者答案的最小字元數和最大字元數（大於0的數值）","adssp_configuration_ppe_choose_txt_file":"請只上傳文字檔。","adssp_config_policies_notification_updated_attr_once":"%updatedAttributes% 巨集變數在郵件內容中只能使用一次。","adssp_js_calendar_month_may":"五月","adssp_admin_logon_customize_dragndrop_htmlarea_justify_center":"居中","key1":"Alert I18N Works Fine.","adssp_admin_policies_enroll_settings_select_domain":"請選擇域","ads_iamapps_cert_regenerating_btn":"重新生成中","adssp_commom_text_advanced":"高級的","adssp_admin_policy_config_secure_link_apc_notify_rp_mail_verify_code_body":"尊敬的%userName%,\\n\\n我們已經得到您希望重置密碼/解鎖帳戶的要求。請點擊以下連結，繼續進一步的處理：%secureLink%\\n\\n謝謝！\\n管理員","adssp_admin_importanswers_select_ques":"請選擇一個問題","adssp_reports_lincensedUsers_reset_users_empty":"選擇使用者以重置免責聲明狀態","adssp_configuration_ldap_name_contains_letters_numbers_hypen":"LDAP名稱只能使用字母、數位和連字號，並且必須以字母開頭。","adssp_common_text_generate":"產生時間","adssp_js_calendar_month_mar":"三月","adssp_admin_configuration_tech_alert_confirm_delete_message":"真的要刪除該技術員嗎？","adssp_domain_user_enrollment_part_enroll_new":"立即完整您的登記註冊！","adssp_login_tfa_alert_confiure_sms_server":"必須配置SMS伺服器才能發送短信。因此，請首先到'伺服器設置'頁面配置SMS伺服器的資訊。","adssp_admin_logon_settings_multi_login_restricted_attr":"您不允許添加該屬性。","adssp_config_mfa_adsqa_edit_ques_reset_mandatory":"重置必填項","adssp_common_alert_empty_choices":"選擇欄位不能為空。","adssp_admin_config_mfa_enable_rsa_auth":"啟用RSA SecureID","adssp_admin_configuration_tech_alert_enter_valid_username":"請輸入有效的用戶名","ads_admin_product_settings_ha_server_restart_error":"不能重啟該伺服器，請手動重啟。","month_mar":"March","adssp_layout_admin_logon_settings_mobile_tab_select_a_domain":"請選擇一個域後繼續。","adssp_admin_logon_customize_dragndrop_htmlarea_insert_link":"插入Web連結","adssp_domain_user_my_info_email_alert":"'請在\\\"'+labelName+'\\\"欄位中輸入有效的電子郵寄地址。' ","adssp_domain_user_enroll_phone_num_any_format_alert":"請按照以下格式輸入您的手機號碼: <br><br>{0}","adssp_admin_customize_logon_drag_drop_url_duplicate":"同名的URL已經存在，請提供不同的名稱","adssp_config_admin_tools_helpdesk_question_delete_confirm_msg":"確實要刪除此問題嗎？","adssp_admin_gina_err_couldnotconnect":"不能連接該機器。","adssp_admin_general_custom_disp_settgs_alert_access_control_admin_login_invalid_ip":"無效的IP地址","day_mon":"Mon","adssp_admin_policies_policy_config_apc_pwd_pol_min_pwd_len":"'- 密碼的最小長度應為 '+val ","adssp_configuration_gina_pls_select_ous":"請選擇組織單位","adssp_config_mfa_advanced_alert_random_que_lessthan_ad_man_ques":"隨機顯示的問題數不應少於已標記為必填項的AD安全問題總數。","notConnect":"不能連接到目的機器","pwdProperty":"You cannot select both 'User must change password at next logon' and  'User cannot change password' for the same user.","adssp_admin_logon_customize_dragndrop_htmlarea_fontsize_5_point":"5 (18 pt)","adssp_admin_selfupdate_layout_fields_cannot_empty":"組容器不能為空，將欄位拖放到合適的容器，然後保存。","adssp_config_admin_tools_helpdesk_enter_question":"輸入安全保密問題","noMailReceipient":"沒有郵件收件人。","month_may":"May","adssp_admin_restricted_referesh_domain":"在使用該功能之前，請刷新活動目錄和本產品，以便獲取最近更新列表。","ads_common_text_click_to_update":"點擊這裡更新","adssp_end_user_click_to_continue":"點擊這裡繼續","adssp_su_custom_field_in_layout_unable_to_update":"不能更新，該欄位已被自助更新佈局使用。","adssp_domain_user_select_account_alert":"請選擇一個帳戶","ads_admin_product_settings_ha_pip_same_as_slave":"虛擬IP位址已被映射到備用伺服器。請嘗試其它IP地址。","adssp_configuration_ppe_enter_positive_number_for_max":"請為密碼最大長度輸入值，範圍為：1 - 127。","adssp_choose_recipient_select_email_value_alert":"請選擇一個郵件ID。","adssp_configure_policy_config_apc_auto_hourly_sch":"每小時的計畫時間不能為\\\"0\\\"，將加以糾正。","adssp_admin_logon_customize_dragndrop_htmlarea_justify_full":"兩端對齊","adssp_admin_policies_policy_config_alert_positive_no_char_user_def_que":"請設置使用者定義問題的最小字元數和最大字元數（大於0的數值）","adssp_admin_settings_connection_ssl_tool_dns_comp_err_msg":"SAN名稱必須以字母開始、已字母或數位結束，其它的只能使用字母、數位或破折號。","ads_configuration_mdm_pushmanager_settings_upload_apns":"上傳.pem格式的證書","adssp_common_text_popuptitle_select_users":"選擇用戶","adssp_admin_logon_customize_dragndrop_htmlarea_fontname_couriernew":"Courier New ","adssp_common_tree_view_expand_collapse":"展開/折疊專案","adssp_admin_policies_policy_config_apc_pwd_pol_start_alph":"'- 應該以字母開始' ","adssp_common_text_cannot_movedown":"不能再下移了","adssp_admin_config_mfa_enable_totp_auth":"啟用基於時間的一次性密碼身份驗證","adssp_admin_enrollment_notification_push_notification_text_push_send_confirm":"真的要向使用者手機設備推送登記註冊的通知嗎？","adssp_admin_logon_customize_dragndrop_htmlarea_bold":"粗體","adssp_configuration_cached_credentials_server_name":"VPN主機名稱/IP","ads_admin_product_settings_ha_restart_slave":"高可用性只能從備用伺服器上禁用。請關閉主要伺服器上的{0}，並從備用伺服器上啟動它。","adssp_admin_customize_logon_drag_drop_enrollment_desc":"通過登記註冊建立您自己的身份標識","adssp_common_navigation_count":"{0} - {1} / {2}","adssp_admin_gina_err_remote_connection":"正在初始化遠端服務連接 . . . 失敗","adssp_login_tfa_alert_empty_fields":"有些欄位為空。","adssp_js_calendar_day_wed":"三","adssp_domain_user_enrollment_ver_code_duplicate_mob_num":"手機號重複，請提供唯一的手機號。","adssp_config_admin_adsqa_less_than_random_ques":"要顯示的隨機問題數大於AD安全問題總數。","adssp_js_calendar_month_oct":"十月","adssp_admin_customize_logon_drag_drop_alert_to_save":"繼續之前必須保存所做的更改，確定要繼續嗎 ","ads_iamapps_custom_saml_fill_app_name":"應用名稱不能為空。","adssp_admin_configuration_tech_alert_cannot_delete_logged_user":"不能刪除登錄中的技術員","adssp_admin_logon_customize_dragndrop_htmlarea_font_color":"字元顏色","adssp_admin_general_adsearch_group_info":"組信息","adssp_admin_logon_customize_dragndrop_htmlarea_justify_right":"居右","ads_layout_admin_logon_settings_saml_tab_photo_upload_size_exceeded":"請確保IdP的標誌圖示的大小不超過20 MB。","ads_admin_product_settings_ha_not_supported_slave":"備用伺服器上的{0}由於版本不相容，不支持高可用性。","adssp_admin_policies_policy_config_apc_pwd_pol_pre_ch_frm_name":"'- 不能連續使用3個或以上用戶名中的字串' ","adssp_admin_policyConfig_thirdStep_edit_ques_minques_del_futher":"'列表中最少要有管理員定義的問題 3 個，不能再刪除了。'  ","ads_support_roboupdate_connecting":"連接中...  ","adssp_admin_policy_config_confirm_delete_policy":"真的確認要刪除該策略嗎? ","admin_schedule_reports_page_select_time":"時間","adssp_reports_enroll_rep_disenroll_blank_import_csv":"請選擇一個CSV檔。","ads_iamapps_custom_saml_alert_choose_rsa_algorithm":"請選擇RSA SHA演算法","selectTheUser":"請選擇用戶","adssp_config_mfa_create_configuration_confirm_overwrite":"應用所有策略","adssp_admin_policies_mobile_app_restriction_disable_mobile_one_auth":"要限制手機訪問，請在多因素認證中對所有策略的禁用手機應用身份驗證器。","adssp_admin_policies_enroll_settings_conf_mail_server_non_empty":"郵件的主題/內容不能為空。","adssp_common_color_picker_hexadecimal":"十六進位&nbsp;:&nbsp; ","adssp_enrollment_alert_configure_one":"請至少選擇一種驗證方法！","adssp_admin_configuration_tech_alert_mandatary_cannot_deleted":"這是缺省的Admin帳戶，不能刪除","adssp_admin_selfupdate_layout_fill_all_values":"填寫所有的值","adssp_configuration_ppe_enter_positive_number_for_spl":"可輸入的密碼特殊字元數的範圍為1 - 127。","adssp_config_admin_tools_helpdesk_alert_must_integrated":"要啟用該功能，必須將ADSelfService Plus和ADManager Plus集成到AD360中。","adssp_config_mfa_adsqa_quest_mapped_other_policies_confirm_change_attr_mapping":"此問題在另一個自助服務策略中使用。確實要更改屬性映射嗎？","adssp_common_layout_footer_feedback_enter_valid_phnum":"請輸入有效的電話號碼。","adssp_admin_configuration_tech_alert_cannot_change_role":"不能更改登錄中的用戶角色","adssp_layout_admin_logon_settings_mobile_tab_photo_uploading_empty_file":"上傳的圖片檔無效。","adssp_admin_server_settings_sms_addon_buy_now":"該功能是ADSelfService Plus SMS付費選包的功能，如果要使用它請購買'SMS付費選包'","adssp_config_sulayout_multi_option_field":"多選項欄位","ads_admin_product_settings_ha_slave_version_error":"不能對兩個不同版本的{0}設置高可用性。","adssp_common_alert_number_exceeds_maximum":"字元長度超過活動目錄允許的最大長度","adssp_common_text_enter_select_computer":"請選擇要限制服務帳戶的電腦","adssp_js_calendar_day_mon":"一","ads_admin_product_settings_ha_slave_not_running_as_service":"備用伺服器中的{0}沒有以服務形式運行，請將其安裝為服務，然後運行。","adssp_config_admin_tools_helpdesk_duplicate_question":"該安全問題已經存在。","adssp_config_admin_tools_helpdesk_duplicate_attribute":"所選的屬性已被映射到其它安全問題。","adssp_configuration_ppe_min_greater_than_sum_of_spl_num":"請確保密碼的最小長度大於或等於所要求的大小寫字元、特殊字元數、數位和萬國碼（Unicode）字元的總和。","adssp_config_gina_cp_process_completed":"處理完成。","adssp_admin_server_settings_sms_enter_valid_url_parameters":"輸入有效的HTTP URL參數","adssp_login_tfa_alert_confiure_duo_server":"必須配置Duo伺服器，請首先到'伺服器設置'下進行配置。","month_nov":"November","adssp_reports_audit_rep_failed":"失敗","adssp_config_management_gina_custom_provide_proper_size":"圖示的檔大小不能超過250kb","adssp_admin_selfupdate_layout_sorry_atleast_one_group":"對不起! 至少要有一個組。","adssp_admin_policies_scheduler_soon_to_expire_msg_third_templete":"尊敬的 %userName%,\\n\\n儘管已經提醒您多次，但是您至今仍沒有修改密碼。您的密碼將在%dateTime%過期。如果現在仍不修改，您的帳戶將被鎖定。請立即修改密碼。\\n\\順祝商祺，謝謝！\\n管理員","adssp_admin_policies_policy_config_alert_atleast_one_que_ans":"用戶必須回答至少一個安全保密問題，因此請輸入大於0的數值。","adssp_layout_admin_logon_settings_mobile_tab_photo_upload_dimensions_exceeded":"所選的圖片超過允許的尺寸限制，請確保圖片的尺寸為: 1000x400圖元","adssp_admin_restricted_inactive_users_days":"最近{0}天內非活動的用戶。","adssp_configure_policy_advanced_config_policy":"配置策略","adssp_admin_policies_scheduler_soon_to_expire_dear_admin_msg":"尊敬的管理員,\\n\\n 本郵件的附件是一份報表，旨在顯示向終端使用者發送\u201c密碼/帳戶過期通知\u201d的統計情況。在您所選擇的存儲路徑中，可以找到相同檔。\\n\\n順祝商祺，謝謝！\\nADSelfService Plus","ads_admin_product_settings_ha_public_ip_not_valid":"虛擬IP位址無效。","adssp_su_custom_field_cant_delete_mapped_in_login_attribute":"無法刪除此屬性。此屬性正在用作登錄屬性。","adssp_configuration_gina_mac_error_unsupported_architecture_troubleshoot":"1. 用戶端軟體不支援此機器架構。<br><br>2.對於linux機器，支持的架構是x86_64(64位)和i686(32位)。","adssp_configuration_ppe_min_no_of_cond_tip":"要滿足的條件的最小數將不包含字典、花樣、覆蓋規則及歷史規則。","nomailToRemove":"沒有要刪除的電子郵寄地址。","adssp_admin_logon_customize_dragndrop_htmlarea_justify_left":"居左","adssp_config_management_custom_text_port_no_valid_msg":"請提供有效的埠號","saturday":"Saturday","adssp_configuration_ppe_dict_filename_size":"檔案名的長度不能超過200字元。","adssp_admin_logon_customize_dragndrop_htmlarea_fontname_narrow":"窄體","adssp_reports_common_text_filter":"過濾","adssp_admin_policies_mobile_sms_identity_text_contain_macro":"請修改手機驗證消息的內容，在其中包含 {0} 宏，以使用所選的選項。","adssp_configuration_ppe_override_pwd_his":"密碼歷史規則不會被本規則覆蓋。","adssp_admin_gina_network_path_not_found":"沒有找到網路路徑。","adssp_export_settings_success":"成功保存匯出設置","ads_admin_product_settings_ha_primary_secondary_same":"主要伺服器和備用伺服器不能是同一個伺服器。","adssp_configuration_ppe_min_greater_than_max":"請確保最大密碼長度大於或等於密碼的最小長度。","adssp_admin_license_management_restrict_users_inactive_users":"休眠的用戶","adssp_reports_enroll_rep_disenroll_e_mail":"郵件ID","ads_admin_product_settings_ha_service_privilege_failure":"未能更新主要伺服器和備用伺服器上的該服務的使用者憑證，請手動更新後再試。","adssp_config_gs_default_name":"訂閱","adssp_successfully_deleted":"刪除成功","adssp_login_tfa_alert_choose_auth_mode":"請選擇一個認證方法。","adssp_js_calendar_thursday":"星期四","adssp_admin_configuration_tech_alert_select_user":"請選擇技術員","ads_iamapps_config_alert_cant_enable_status":"不能啟用該配置的狀態。","adssp_admin_selfupdate_layout_advanced_thumbnail_size_restriction":"縮略圖照片不能超過100KB","adssp_configuration_admin_tools_ext_data_source_delete_datasource":"刪除已經建立的資料來源，將同時刪除相對應的安全保密問題及答案的收集器","adssp_admin_policies_mobile_one_auth_disable_restriction":"請在手機設置中，禁用'限制手機應用訪問'的選項。","adssp_common_text_type_your_mail_message":"<在此輸入郵件內容> ","adssp_config_management_custom_text_provide_installation_modules":"請至少選擇一個模組","ads_common_text_all":"所有","adssp_admin_policies_policy_config_confirm_delete_message":"真的要刪除該佈局嗎? ","adssp_js_calendar_month_jul":"七月","adssp_js_calendar_month_jun":"六月","adssp_configure_policy_config_apc_auto_generate_pwd_pol_fail":"如果ADSelfService Plus的密碼策略與您的域密碼策略不一致，將提示使用者到重置密碼頁面。","ads_iamapps_custom_saml_enter_category_name":"請輸入有效的分類名稱","adssp_admin_policies_policy_config_apc_pwd_pol_exclude_words":"'- 防止用戶使用這些花樣'+val ","adssp_config_mfa_advanced_mail_format_exceed_count":"郵件域的數量不能超過20個。","adssp_domain_user_cp_rp_pwd_strength_strong":"很強","adssp_admin_configuration_tech_alert_contains_symbol":"登錄名中不能包含@,\\\\ 這樣的字元。","adssp_admin_policies_policy_config_apc_reset_unlock_note":"備註","day_wed":"Wed","adssp_admin_customize_rebranding_reset_to_default_values":"將恢復<span class=\\\"normalBoldFont common-textcolor\\\">顯示設定<\/span>的缺省設置，確定繼續嗎? ","adssp_config_admin_tools_helpdesk_select_attribute_warning":"請選擇相對應的LDAP屬性。","adssp_common_text_type_your_mail_message_new":"&lt;在這裡輸入您的郵件內容&gt;  ","adssp_admin_server_settings_mail_enter_valid_email":"請輸入有效的電子郵寄地址","adssp_admin_logon_customize_dragndrop_htmlarea_horizontal_rule":"水平線","adssp_configuration_gina_install_process_out_of_computers":"' / '+val+ ' 電腦' ","day_sat":"Sat","adssp_admin_customize_logon_drag_drop_reset_pwd":"重置密碼","adssp_js_calendar_month_sep":"九月","ads_admin_product_settings_ha_invalid_account_format":"帳戶的格式無效。","adssp_configuration_sulayout_initial_value_valid_integer":"初始值應該為一個整數","adssp_config_admin_tools_helpdesk_one_question_selected":"至少要啟用一個安全問題。","adssp_configuration_ppe_max_greater_than_sum_of_spl_num":"請確保密碼的最大長度大於或等於所要求的大小寫字元、特殊字元數、數位和萬國碼（Unicode）字元的總和。","adssp_config_sulayout_empty_assign_value_txt":"[值沒有指定，該屬性在AD中的值將被清除。]","adssp_login_tfa_alert_saml_metadata_file_invalid":"請上傳有效的中繼資料文件","adssp_admin_policy_config_navigate":"真的要離開本頁面嗎？\\n 您所做的修改尚未保存。 ","adssp_configuration_task_scheduler_enter_enroll_UI_text":"請提供要傳輸的資訊","adssp_config_gina_err_couldnot_copy_msi":"1.沒有足夠的許可權以訪問該用戶端電腦。<br/><br/>2.如果產品以應用的方式啟動，請在\\\"域設置\\\"畫面提供管理員級的使用者登錄憑據。如果產品以Windows服務啟動，請在服務屬性的\\\"登錄\\\"頁簽下，更新該服務的帳戶憑據。","adssp_config_policy_config_advanced_alert_requester_missing":"該請求必須包含請求人的參數。","adssp_admin_gina_install_success":"安裝成功","adssp_admin_config_mfa_enable_push_auth":"啟用推送通知驗證","adssp_domain_user_enroll_phone_num_alert":"'在\u201c手機\u201d欄位中輸入的號碼，格式為{0}'","adssp_login_network_error_message":"Network connection error. Please contact your administrator.","ads_common_heading_message_alert":"告警信息","adssp_common_text_warning":"警告","ads_configuration_mdm_pushmanager_settings_csr_generation_improper_country_length":"請確認國家代碼為2個字母！","admin_policy_alert_policy_mandatory":"沒有選擇任何自助服務的功能。","adssp_reports_license_delete_pop_msg_txt":"'您已選擇<span style=\\\"color:#000;font-size:12px\\\">'+var1+'個要刪除的用戶<\/span>。請確認是否刪除。'","adssp_successfully_updated":"更新成功","adssp_layout_admin_logon_settings_mobile_tab_mobile_settings_reset_to_default_values":"將要恢復到<span class=\\\"normalBoldFont common-textcolor\\\">默認的手機設置<\/span>，繼續嗎？","ads_iamapps_config_alert_sp_big_logo_upload_dimensions_exceeded":"大圖示尺寸超過","adssp_config_sulayout_empty_assign_values":"規則中'指定值'的部分為空的，將不會保存到範本中。繼續嗎？","adssp_support_alert_emptyemail":"郵寄地址不能為空。","ads_admin_product_settings_ha_service_not_privileged":"必須為主要伺服器和備用伺服器上的{0}服務配置具有域管理員許可權的憑證。","adssp_license_expiry_numeric_value_notification_days":"發送許可即將過期通知的天數只能輸入數字。","adssp_admin_selfupdate_layout_sorry_cant_del_default_lay":"對不起! 不能刪除缺省的佈局。","adssp_configuration_admin_tools_ext_dat_source_policy_already_mapped":"所選的策略已經映射給其它配置中。","adssp_login_tfa_alert_confiure_mail_server":"必須配置郵件伺服器才能發送郵件。因此，請首先到'伺服器設置'頁面配置郵件伺服器的資訊。","adssp_license_expiry_valid_usage_count_limit":"請注意：允許的最大配置限制為5。","adssp_domain_user_reset_qa_sel_ques_alert_ans_empty":"答案不能為空。","adssp_login_tfa_alert_saml_metadata_file_empty":"請上傳中繼資料文件","adssp_config_su_section_duplicate":"組名已經存在。","adssp_admin_policies_sulayout_enter_your_choices_here":"輸入您的選擇","adssp_js_calendar_day_sun":"日","SupportEmptyEmail":"Email ID should not be empty!","prevMon":"Prev month (hold for menu)","adssp_admin_server_settings_mail_enter_username":"請輸入用戶名","adssp_admin_policies_policy_config_apc_pwd_pol_pre_same_ch":"'- 不能重複使用一個字元兩次以上' ","adssp_domain_user_my_info_valid_characters":"請輸入有效的值","adssp_admin_policy_config_sec_que_que_entered_already_exist":"您輸入的問題已經存在。","adssp_js_calendar_nextMon":"下月(下拉式功能表)","adssp_license_expiry_select_one":"請至少選擇一個許可過期的通知選項。","validateUserName":"entered has one or more illegal characters / \\ [ ]: ; | = , + * ? < > @ \\\\\\\". These are not allowed. Please remove them and try again.","adssp_login_tfa_alert_invalid_radius_server":"RADIUS伺服器名稱無效。","adssp_reports_pwd_expired_notification_mail_content":"您好 %userName%,\\n\\n您的密碼將在 %dateTime% 過期。請您儘快更改您的域登錄密碼。\\n\\n謝謝\\n管理員","adssp_configuration_gina_pls_enter_text_to_search":"請輸入要搜索的資訊","adssp_configuration_sulayout_phone_format_valid_allowed_value":"輸入有效的允許值","adssp_admin_policyConfig_thirdStep_edit_ques_set_mandatory":"設置為必填項","ads_admin_product_settings_ha_master_service_privilege_success":"主要伺服器上的ADSelfService Plus服務的使用者憑證已更新成功。請重啟該服務已使變更生效。","ajaxError":"There was a problem retrieving the XML data:","adssp_admin_configuration_tech_alert_pwd_dismatch":"密碼不匹配","adssp_common_text_update_failed":"更新失敗","adssp_js_calendar_month_dec":"十二月","adssp_js_first":"開始","adssp_configuration_cached_credentials_client_location_msg_cisco":"例如: C:\\\\Program Files (x86)\\\\Cisco\\\\Cisco IPSec\\\\vpnclient.exe ","adssp_layout_domain_setting_dc_already_exists":"指定的網域控制站已經存在。","adssp_reports_audit_rep_pn_success":"成功將推送通知發送至推送伺服器 ","adssp_common_text_list":"列表","characterNotAllowed":"特殊字元 /:|*?<>\\\\\\\"' 不允許。","dragToMove":"拖動","ads_admin_product_settings_ha_settings_already_configured":"不能配置高可用性。<br>該備用伺服器上已經設置了高可用性。","adssp_reports_enroll_rep_select_enroll_type":"選擇類型","adssp_admin_importanswers_need_properfile":"請輸入合適的檔。","adssp_config_policy_config_advanced_alert_nospl_length_greater":"特殊字元的長度大於最大長度","adssp_admin_policies_identity_more_dup_mail_attrib":"請移除重複的電子郵件屬性","month_aug":"August","adssp_login_tfa_alert_username_twice_in_radius_pattern":"請確保<b>user_name or USER_NAME<\/b>在用戶名模式中不會重複兩次。","adssp_login_tfa_alert_saml_invalid_login_url":"請使用有效的HTTP/HTTPS URL","ads_iamapps_config_alert_only_five_mappings_allowed":"只能連續映射5個","adssp_admin_general_custom_disp_settgs_alert_access_control_invalid_ip":"IP範圍為空、或指定的IP值無效、或IP範圍無效。","adssp_config_gina_mac_err_couldnt_copy_linux_install_sh":"無法複製installLinuxAgent.sh","adssp_admin_logon_customize_dragndrop_htmlarea_enter_url":"輸入URL ","adssp_config_mfa_adsqa_quest_mapped_other_policies_cannot_delete_question":"無法刪除此問題。它正在另一項自助服務策略中使用。","ads_admin_product_settings_ha_cluster_exception":"出現未知錯誤，請聯繫技術支援。","adssp_general_attr_checkbox_alert":"'不能為\\\"'+labelName+'\\\"欄位提供多個值。'","adssp_domain_user_change_pwd_alert_not_match":"您所輸入的新密碼與確認密碼不一致。","adssp_popup_common_search_errormsg_text_no_objs_found":"沒有找到匹配的物件。","adssp_domain_user_ad_questions_alert_enter_ans":"有些問題還沒有指定答案，請在繼續下一步之前指定問題的答案。","adssp_config_gina_show_addsp_tile_applies_to_cp":"本選項適用於Windows Vista及以上版本。","adssp_common_text_enter_valid_mob_num":"請輸入有效的手機號。","adssp_configuration_ppe_select_atleast_one_rule":"至少要選擇一個複雜度規則。","SuppportValidEmail":"Please enter valid Email ID","adssp_admin_importanswers_please_selectques":"-------請選擇一個問題-------","adssp_domain_user_my_info_photo_upload_browse_your_photo_and_upload":"流覽圖片並上傳","adssp_admin_general_custom_disp_settgs_alert_valid_captcha_invalid_attempts":"請為無效登錄次數指定一個大於0的整數","adssp_admin_policies_ste_confirm_delete_scheduler":"真的要刪除該計畫嗎？","adssp_js_calendar_tuesday":"星期二","adssp_reports_license_reset_pop_msg_txt":"'您已選擇重置<span style=\\\"color:#000;font-size:12px\\\">'+var1+'用戶<\/span>的免責聲明狀態。確實要重置嗎？' ","admin_schedule_reports_generate_report_for":"生成報表: ","adssp_reports_schedule_reports_common_expired":"過期前的天數","adssp_admin_configuration_tech_alert_enter_valid_password_radius":"請輸入RADIUS密碼","adssp_configuration_cached_credentials_default_open_vpn_connectcommand":"--config \\\"C:\\\\Program Files (x86)\\\\Sophos\\\\Sophos ssl client\\\\config\\\\%<EMAIL>\\\" --auth-user-pass \\\"%tempFile%\\\" ","ads_iamapps_config_alert_invalid_domain_name":"無效的功能變數名稱","adssp_configuration_ppe_dict_check_type_tip":"如果啟用本選項，則包含有字典中的單詞的密碼將被限制。<br>如果不啟用本選項，則限制使用精確的字典單詞作為密碼。","adssp_config_gina_mac_err_failed":"失敗","adssp_su_custom_field_cant_update_mapped_in_pswdsync":"無法更新。此屬性用於密碼同步器中的帳戶連結。","adssp_config_admin_tools_helpdesk_no_policy":"沒有選擇策略","adssp_common_text_port_positive":"埠號只能是正的整數，請輸入正整數。 ","adssp_config_multi_sec_qa_less_than_random_questions":"要顯示的隨機問題數大於安全問題總數。","ads_iamapps_config_alert_ok":"確定","adssp_configuration_admin_tool_quick_enroll_notify_policy_users":"真的確定要想所選策略的使用者發送該消息嗎？","adssp_admin_schedulelist_disable":"禁用","adssp_js_calendar_sunday":"星期日","adssp_login_tfa_alert_field_invalid_value":"請提供有效的{0}。","ads_saml_config_alert_valid_metadata_file":"請上傳有效的中繼資料文件","adssp_domain_user_enrollment_ver_code_click_cross_to_delete":"點擊刪除圖示刪除","adssp_common_text_success_failure":"不能保存變更。 <b>原因:<\/b> ","adssp_admin_gina_couldnt_copy_client_software_troubleshoot":"1.請配置具有管理員許可權的認證憑據。如果以應用的方式啟動本產品，請在域設置頁面設置合適的管理員帳戶；如果以Windows服務啟動本產品，請在該服務的登錄選項中設置合適的管理員憑據。<br/><br/>2.檢查是否已經啟用Admin共用。<br/>3.檢查 ADSelfServicePlus 用戶端軟體確實存在。","month_dec":"December","ads_admin_product_settings_ha_slave_mssql_native_client_not_installed":"備用伺服器上必須安裝MS SQL Native Client，請安裝之後再試。","adssp_layout_admin_logon_settings_mobile_tab_photo_upload_size_exceeded":"圖片大小超過允許的大小限制：2MB。請上傳一個較小的圖片。","adssp_admin_restrict_report_change_msg":"正在生成報表，點擊確定按鈕，丟棄該操作。或選擇取消按鈕，保留在當前頁面。","adssp_admin_configuration_tech_alert_change_role_message":"更改角色","adssp_admin_policies_policy_config_apc_reset_unlock_advisory":"勸告","adssp_login_tfa_alert_saml_metadata_file_invalid_login_url":"中繼資料的登錄URL無效","adssp_admin_policies_policy_config_apc_pwd_pol_max_pwd_len":"'- 密碼的最大長度應為 '+val ","ads_iamapps_config_alert_atleast_one_attrib_map":"對不起! 至少要映射一個屬性","adssp_configuration_gina_mac_error_login_screen_customization":"設置依賴項時操作失敗","adssp_configuration_ppe_dict_empty":"上傳的字典檔為空檔。","adssp_admin_customize_logon_drag_drop_enrollment":"用戶登記註冊","ads_iamapps_link_popup":"用戶名中包含無效字元，請輸入有效的用戶名","adssp_admin_general_custom_disp_settgs_alert_access_control_invalid_ip_or_server_name":"一些地址欄位中的IP地址/伺服器名稱無效。它不能有多個位址。","adssp_admin_domain_settgs_enterdc":"輸入要添加的網域控制站","adssp_config_management_gina_custom_provide_bmp_file":"圖示格式為BMP檔，請提供BMP檔","adssp_js_calendar_prevMon":"上月(下拉式功能表)","adssp_admin_policies_scheduler_soon_to_expire_delivery_report_sub":"發送郵件通知的統計報表 ","ads_admin_product_settings_ha_service_privilege_success":"成功更新ADSelfService Plus服務的使用者憑證。為使變更生效，請重啟主要伺服器和備用伺服器上的該服務。","adssp_js_calendar_month_feb":"二月","adssp_reports_common_text_none":"沒有 ","adssp_selfservice_gs_subscribe_processing":"處理中<blink>...<\/blink> ","adssp_common_alert_select_computer":"請選擇一台電腦。","ads_support_roboupdate_saved":"保存成功","NeedProperFile":"Please enter a proper file.","adssp_admin_server_settings_proxy_enter_valid_port":"請輸入合適的伺服器埠","adssp_config_policy_config_advanced_alert_script_content_empty":"請輸入有效的腳本。","adssp_admin_config_pwd_sync_automatic_link_alert_office365":"如果在AD及Office365中的用戶名相同，則使用者帳戶將自動連結","ads_admin_product_settings_ha_server_restart_success":"{0}正在重啟，請稍後再檢查。","first":"First","ads_iamapps_cert_gen_failed_contact_support":"請聯繫技術支援。","adssp_admin_gina_err_remote_connection_troubleshoot":"正在和遠端服務建立連接 . . .  連接失敗","adssp_domain_user_enrollment_enter_valid_code":"請輸入有效的代碼","ads_admin_product_settings_ha_not_supported_master":"主要伺服器上的{0}由於版本不相容，不支持高可用性。","ads_iamapps_config_alert_invalid_file_format":"請確保圖片格式為PNG, JPG, GIF, 或JPEG。","ads_saml_config_alert_valid_metadata_file_name":"在檔案名中發現無效字元。它應該遵循規則運算式模式{0}。","adssp_admin_selfupdate_layout_positive_photo_size":"圖片大小應該是正的數值","adssp_admin_server_settings_mail_enter_valid_modem_port":"請輸入有效的數據機埠號","adssp_config_gina_mac_err_couldnt_copy_install_sh":"不能複製installMacAgent.sh ","adssp_admin_policies_policy_config_alert_max":"{0}的最大長度不能超過1000。","calendarCallBack":"沒有給出flatCallback -- 不做任何動作","ads_iamapps_config_alert_sp_small_logo_upload_dimensions_exceeded":"小圖示尺寸超過","ads_iamapps_config_alert_sp_logo_upload_invalid_format":"圖示無效，請確保圖片檔的格式為PNG, JPG, GIF, 或 JPEG。","selectDate":"Select Date","adssp_su_custom_field_mapped_in_empsearch":"不能刪除該屬性，它已被用於員工搜索。","adssp_admin_policies_sulayout_enter_your_group_name":"輸入您的組名","adssp_admin_server_settings_sms_enter_valid_http_header":"輸入有效的HTTP請求頭資訊","adssp_config_gs_selected_groups":"已選的組","adssp_reset_unlock_accounts_username_alert_enter_name":"請輸入用戶名","selectMailToRemove":"選擇要刪除的電子郵寄地址。","adssp_ok_button":"確定","adssp_admin_server_settings_mail_enter_valid_mail_content":"輸入有效的郵件內容","adssp_configuration_ppe_enter_positive_number_for_num":"請為密碼中需要的數值個數輸入值，範圍為：1 - 127。","adssp_domain_user_reset_qa_sel_ques_alert_sel_ques":"請選擇一個問題。","adssp_configure_policy_config_apc_reset_unlock_limit_alert":"本選項使用者通過ADSelfService Plus只能重置密碼和解鎖帳戶。","ads_admin_product_settings_ha_master_cluster_exists":"主要伺服器上已經設置了高可用性。","adssp_admin_general_adsearch_emp_details":"員工明細","adssp_common_text_enter_positive_no":"輸入一個正數","adssp_admin_logon_customize_dragndrop_htmlarea_fontname_comicsans":"Comic Sans MS ","adssp_admin_general_adsearch_emp_info":"員工資訊","adssp_admin_license_management_restrict_users_deleted_users":"刪除的用戶","adssp_admin_configuration_tech_alert_delete_message":"刪除","adssp_admin_server_settings_push_register_enter_valid_admin_mail_id":"請輸入一個管理郵件的ID","day_fri":"Fri","ads_iamapps_config_alert_cancel":"取消","ads_iamapps_config_alert_choose_p12_file":"請選擇P12金鑰檔","ads_iamapps_config_alert_select_host":"至少要選擇一個系統","adssp_config_admin_tools_helpdesk_one_question_needed":"不能刪除，至少需要一個安全問題。","ads_support_roboupdate_alert_entries_notnull":"必填欄位不能為空","adssp_successfully_added":"添加成功","adssp_admin_customize_logon_drag_drop_unlock_acc":"解鎖帳戶","adssp_admin_selfupdate_layout_default_field_type":"缺省欄位類型","adssp_domain_user_cp_rp_pwd_strength_weak":"太弱","adssp_config_mfa_adsqa_quest_mapped_other_policies_confirm_edit_text":"此問題在另一個自助服務策略中使用。確實要編輯該問題嗎？","adssp_configuration_task_scheduler_enter_enroll_ui_text":"null","adssp_common_text_popuptitle_add_ous_groups":"添加組織單位/組","adssp_domain_user_my_info_num_alert":"'請在\\\"'+labelName+'\\\"欄位中只輸入數位。' ","adssp_admin_customize_db_backup_schedule_pls_enter_storage_path":"請輸入備份資料的存儲路徑","enterValue":"Please enter an value for ","adssp_domain_user_enrollment_part_enroll":"請立即進行登記註冊，從而享用本產品為您帶來的便利！","adssp_admin_logon_customize_dragndrop_htmlarea_fontname_trebuchet":"Trebuchet MS ","adssp_config_management_custom_text_provide_button_text":"請提供按鈕標籤名","adssp_su_custom_field_mapped_in_pswdsync":"不能刪除，該屬性被用於連結密碼同步器的帳戶","month_feb":"February","adssp_admin_restricted_unownedlicenses":"未被承認的許可","adssp_export_settings_photo_upload_dimensions_exceeded":"選擇的圖片超過了尺寸限制。請確保圖片尺寸為：200x50圖元。 ","adssp_config_sulayout_manager_field_hint":"應該提供經理的特異名(DN)。","ads_iamapps_config_alert_invalid_host_display_name":"無效的顯示名","ads_iamapps_config_alert_invalid_port":"指定的埠號不是有效的數值","adssp_config_sulayout_mandatory_alert":"欄位為必填欄位，不能為空。","adssp_admin_policies_ste_cannot_del":"不能刪除，至少要保留一個計畫","ads_iamapps_custom_saml_both_sp_idp":"同時包括 SP & IDP流","ads_common_text_select_domain":"選擇一個域","adssp_configuration_policy_config_advanced_identity_mandatory":"必需項","adssp_admin_gina_err_fatal_error":"安裝時出現嚴重錯誤。","adssp_common_invalid_chars_in_file_path":"檔路徑中包含無效的字元，請不要在檔案名中使用 / : * ? \\ < > | \\\\\\\" 等字元。","adssp_admin_policies_scheduler_soon_to_expire_msg_first_templete":"尊敬的 %userName%,\\n\\n您的密碼將在%dateTime%過期。所以，請儘快修改您的域密碼。\\n\\n謝謝！\\n\\n順致商祺！\\n管理員","adssp_config_sulayout_multi_option_field_hint":"對於選項按鈕、下拉清單及複選按鈕，將會略掉它們的值中的雙引號。","admin_policy_alert_enter_policy_name":"輸入策略名","adssp_admin_policies_policy_config_alert_min_max_not_match_ans":"使用者答案的最小字元數和最大字元數不能相同。","adssp_admin_policies_scheduler_soon_to_expire_start_notify_days_before":"在密碼/帳戶過期之前的{0}天通知用戶。","ads_iamapps_custom_saml_alert_some_fields_left_blank":"請選擇該應用的分類。","adssp_configure_policy_config_apc_reset_limit_alert":"本選項僅涵蓋使用者通過ADSelfService Plus執行的重置密碼操作。","adssp_common_text_less":"縮減","tuesday":"Tuesday","adssp_admin_gina_no_file":"系統沒有找到指定的檔","adssp_domain_user_reset_vc_enter_ver_code":"輸入驗證碼","adssp_admin_policyConfig_thirdStep_mandat_ques_more_than_random_ques":"你試圖強制使用更多的問題，而不是你選擇顯示亂數的問題。","ads_admin_product_settings_ha_virtual_master_same":"虛擬主機名稱不能與主要伺服器的主機名稱相同。","adssp_policy_config_apc_notify_admin_password_content_alert":"郵件內容中不能包含 %password% 巨集變數","adssp_admin_server_settings_modem_enter_valid_msg_center":"輸入有效的短信中心的號碼","adssp_admin_gina_couldnt_copy_client_software":"不複製ADSelfServicePlusClientSoftware.msi ","adssp_configuration_sulayout_initial_value_max_char":"初始值不應超過最大字元長度"});
	var hideCap = 1;
	var isOtherLoginAttrEnabled = "false";
	var errorMessage = '';
	var captchaImgURL = 'static\x2Fhip.jpg\x3FisLoginPage\x3Dtrue\x26t\x3D217';

	var smallSizeFrame = false;
	// Appending the alert box in parent body.
	if($('#fTLoginAlert',parent.document).length==0)
	{
		var statusDiv='<div class="mini-status-alert status-alert status-alert-danger status-has-parent status-alert-xmd alert-dismissible ssp-display-none" role="alert" id="fTLoginAlert"><button type="button" class="close" onclick="closeDomainAlertDiv()"><span aria-hidden="true">&times;</span></button><div class="ssp-text-color-default ssp-align-center text-md"><i class="ssp-inline-icon icn-cross ssp-mt-m2"></i><span id="fTLoginAlertText"></span></div></div>'; //No I18N
		$(parent.document.body).append(statusDiv);
        $('#IframeLoadingStatus', window.parent.document).hide(); // No I18N
	}
			
	function domainLoginOnLoadfunc()	
	{
		if(errorMessage!='')
		{			
			flatThemeJquery("#fTLoginAlertText",parent.document).html(errorMessage);	//No I18N
            flatThemeJquery("#fTLoginAlert",parent.document).delay(500).slideDown('slow') //No I18N
			
			flatThemeJquery(".status-alert .close").on("click", function(e) { // No I18N
            flatThemeJquery(this).closest("div.status-alert").slideUp("slow"); // No I18N
        });
			
		}
        $('#IframeLoadingStatus', window.parent.document).hide(); // No I18N
	}
	function refreshLangSelectOption()
	{
		flatThemeJquery('#localeId',parent.document).selectpicker('refresh'); //No I18N
	}

</script>

</head>


<body id="UserLoginFrame" class="ssp_theme_royalblue" onload="domainLoginOnLoadfunc();;showErrorOnLoadPage()" style="min-width:442px;background:none transparent;min-height:340px">

		<script>
			errorMessage = '請重新輸入圖像中的字元。';
		</script>
		
<table style="min-width:325px;max-width:442px;" border="0" align="center" cellpadding="0" cellspacing="0">
	<tr>
	<td valign="top">
	<div class="ssp-login-container" style="top: 0;left: 0;margin-top: 0px;margin-left: 0px; ">
	<div class="login-inner-wrapper">
	
	<script>
	var enableLiveSearch = false;
	var enableScrollBar = false;
	var domainDetailsLength = 0;
	</script>
	
			
				<!-- Login -->
				<form name="DomainUserLogin" method="POST" action="j_security_check" onSubmit="return checkForNull(document.DomainUserLogin,true)">
					<div class="login-wrap user-login-form  open" style=" ">
						<div class="login-header">
							<i class="ssp-icon icn-login-user"></i>
							<span style="width:350px;text-overflow: ellipsis;overflow: hidden;white-space:nowrap;display:inline-block;" >登錄</span>
							<div class="login-caret"></div>
						</div>

						
						
					    <ul class="">
													
							<li class="login-user-info hide">
                             <div class="row">
                              <div class="col-md-12">
                               <span class="pull-left">
								 <label><em>您好</em>&nbsp;<span id="username"></span>,&nbsp;<em>輸入您的密碼進行登錄</em></label>
                               </span>
                                    <a id="editUsername" onclick="editUsername(document.DomainUserLogin)" style="cursor:pointer;" class="pull-right hide">更改</a>
                                 <div class="clearfix"></div>
                             </div>
                            </div>
                           </li>
							
							<li class="relative UseRName" >
								<input name="j_username" type="text" id="j_username" value="" placeholder="用戶名" class="linput" autocomplete="off" onkeypress="javascript:onKeyPressEvent(event, document.DomainUserLogin)" onkeyup="javascript:onKeyUpEvent(event, document.DomainUserLogin)" onpaste="javascript:onPasteEvent(this,document.DomainUserLogin)" maxlength="255"/>
								<span class="add-on form-icn-user" style="cursor:text"></span>
					        </li>
					        
                                <li class="ssp-make-relative PsSwrD">
                                   <div class="form-input-with-icon">
                                       <input name="j_password" type="password" id="j_password" value="" inputMaxLength=256 placeholder="密碼" class="linput" autocomplete="off"/>
                                       <span class="add-on form-icn-password" style="cursor:text"></span>
                                       <span class="add-on form-icn-eye open hide"></span>
                                   </div>
                                </li>
                            

							<div id="LOGIN_DOMAIN_DROPDOWN">
							
							<li>
								<div class="ssp-mt-10">
									<select id="domainName" name="domainName" style="width:205px;" class="bs-custom-scroll selectpicker   limited-option-text-300px   login-dropdown" data-width="100%" onkeypress="javascript:onKeySubmitLogin('DomainUserLogin', event)">
								
										<option value="7af51f278a7a84a62a1a3367b2521ff7837fb4762810d9bdb7376f4d3b42ae36ca7091f9" title="SFL" selected>
											SFL
										</option>
								
										<option value="6f587bdf6e09b28f0560db944076e833a5574bace6468228c15ddc143d55b4e9ac18ac3d" title="SFL-SIS" >
											SFL-SIS
										</option>
								
									</select>
								</div>
								
								<script type="text/javascript">
									// Pre render domain selection
									enableLiveSearch = false;
									enableScrollBar = false;
									domainDetailsLength = 2;
									if(smallSizeFrame && domainDetailsLength > 2)
									{
										flatThemeJquery('#domainName').selectpicker({dropupAuto:false,liveSearch:enableLiveSearch,size : 5,container:'body'}); //No I18N
									}
									else
									{
										flatThemeJquery('#domainName').selectpicker({dropupAuto:false,liveSearch:enableLiveSearch,size : 5}); //No I18N
									}
									addTitleForSelectPicker("domainName");	//NO I18N
								</script>
							</li>
								
						
						</div>
							
							
							<li class="btn-login">
								<input id="loginButton" type="submit" onclick="return validateLoginPage('submit','userLoginPage')" class="btn btn-primary" style="cursor:pointer;" value="登錄">
								<span class="ssp-ml-10" id="progress_DomainUserLogin" style="display: none;">
									<img class="ssp-mr-5" src="webclient/images/circle-dotted-loader.gif" />
								</span>
								<span class="login-rolling-loader pull-left hide">
                                   <img src="./images/Rolling.gif" class="">
                                </span>
                            <div class="clearfix"></div>
							</li>
							
							<li id="PRIMARY_AUTHS" class="row">
						
								

								
							</li>
						</ul>
						
						
						
							<input type="hidden" name="AUTHRULE_NAME" value="ADAuthenticator">
							<input type="hidden" name="domainAuthen" value="true">
							<input type="hidden" id="captchaPage" value="DomainLogin">
					</div>
				</form>
            
        
			
			
			
			
	
			
			
			
			<!-- Reset -->
			<form name="RPForm" method="POST" onSubmit="return validateSelfServiceForm(document.RPForm)" action="ServletAPI/accounts/selfservice?isFromEndUserPage=true&OPERATION=reset">
                <div class="login-wrap ">
                    <div class="login-header">
                        <i class="ssp-icon icn-login-password"></i>
						<span style="width:350px;text-overflow: ellipsis;overflow: hidden;white-space:nowrap;display:inline-block;" >Password Reset</span>
                        <div class="login-caret"></div>
                    </div>
                    <ul>
                        <li>
                            <input type="text" name="DOMAIN_USER_NAME" placeholder="用戶名" onkeypress="javascript:onKeyPressEvent(event, document.RPForm)" onkeyup="javascript:onKeyUpEvent(event, document.RPForm)" onpaste="javascript:onPasteEvent(this,document.RPForm)" class="linput" maxlength="255"/>
                            <span class="add-on form-icn-user"></span>
                        </li>
                        
                        <li>
                            <div>
                                <select name="SELECTED_DOMAIN" id="SELECTED_DOMAIN_RPForm" class="bs-custom-scroll selectpicker limited-option-text-300px ssp-mt-10 login-dropdown" data-width="100%">
                                
                                    <option value="7af51f278a7a84a62a1a3367b2521ff7837fb4762810d9bdb7376f4d3b42ae36ca7091f9" title="SFL" selected>
                                        SFL
                                    </option>
                                
                                    <option value="6f587bdf6e09b28f0560db944076e833a5574bace6468228c15ddc143d55b4e9ac18ac3d" title="SFL-SIS" >
                                        SFL-SIS
                                    </option>
                                
                                </select>
                            </div>
							<script type="text/javascript">
                                // Pre render domain selection
								enableLiveSearch = false;
								enableScrollBar = false;
								domainDetailsLength = 2;
									
                                flatThemeJquery('#SELECTED_DOMAIN_RPForm').selectpicker({liveSearch:enableLiveSearch,size : 5}); //No I18N
								addTitleForSelectPicker("SELECTED_DOMAIN_RPForm");	//NO I18N
                            </script>
                        </li>
                        
                        <li class="btn-login">
                            <input type="submit" class="btn btn-primary" value="繼續" />
                            <span class="ssp-ml-10" id="progress_RPForm" style="display: none;">
                                <img class="ssp-mr-5" src="webclient/images/circle-dotted-loader.gif" />
                                <span class="ssp-dull-text-1">
                                    請稍候。
                                </span>
                            </span>
                        </li>
                    </ul>
                </div>
            </form>
			
			
			
	
			
			
			
			
			
			<!-- Unlock -->
            <form name="UAForm" method="POST" onSubmit="return validateSelfServiceForm(document.UAForm)" action="ServletAPI/accounts/selfservice?isFromEndUserPage=true&OPERATION=unlock">
				<div class="login-wrap ">
                    <div class="login-header">
                        <i class="ssp-icon icn-login-locked"></i>
                        <span style="width:350px;text-overflow: ellipsis;overflow: hidden;white-space:nowrap;display:inline-block;" >Account Unlock</span>
                        <div class="login-caret"></div>
                    </div>
                    <ul class="">
                        <li>
                            <input type="text" name="DOMAIN_USER_NAME" placeholder="用戶名" onkeypress="javascript:onKeyPressEvent(event, document.UAForm)" onkeyup="javascript:onKeyUpEvent(event, document.UAForm)" onpaste="javascript:onPasteEvent(this,document.UAForm)" class="linput" maxlength="255"/>
                            <span class="add-on form-icn-user"></span>
                        </li>
                        
                        <li>
                            <div>
                                <select name="SELECTED_DOMAIN" id="SELECTED_DOMAIN_UAForm" class="bs-custom-scroll selectpicker limited-option-text-300px ssp-mt-10 login-dropdown" data-width="100%">
                                
                                    <option value="7af51f278a7a84a62a1a3367b2521ff7837fb4762810d9bdb7376f4d3b42ae36ca7091f9" title="SFL" selected>
                                        SFL
                                    </option>
                                
                                    <option value="6f587bdf6e09b28f0560db944076e833a5574bace6468228c15ddc143d55b4e9ac18ac3d" title="SFL-SIS" >
                                        SFL-SIS
                                    </option>
                                
                                </select>
                            </div>
							<script type="text/javascript">
                                // Pre render domain selection
                                enableLiveSearch = false;
								enableScrollBar = false;
								domainDetailsLength = 2;
									
                                flatThemeJquery('#SELECTED_DOMAIN_UAForm').selectpicker({liveSearch:enableLiveSearch,size : 5}); //No I18N
								addTitleForSelectPicker("SELECTED_DOMAIN_UAForm");	//NO I18N
                            </script>
                        </li>
                        
                        <li class="btn-login">
                            <input type="submit" class="btn btn-primary" value="繼續" />
                            <span class="ssp-ml-10" id="progress_UAForm" style="display: none;">
                                <img class="ssp-mr-5" src="webclient/images/circle-dotted-loader.gif" />
                                <span class="ssp-dull-text-1">
                                    請稍候。
                                </span>
                            </span>
                        </li>
                    </ul>
                </div>
            </form>					
			
	
	</div>
	</div>
	</td>
	</tr>
</table>
<script>	 
function redirectSAMLUrl(url)	 
{	 
	 window.top.location.replace(url);	 
}

	var isProcessing = false;

    if(flatThemeJquery('.login-wrap.open').closest('form').attr('name') != 'DomainUserLogin')
    {
        flatThemeJquery('.login-wrap.open').css({"height":"194px"});// No I18N
    }
    
    flatThemeJquery(document).ready(function() {
		
        flatThemeJquery(".status-alert .close").on("click", function(e) { // No I18N
            flatThemeJquery(this).closest("div.status-alert").slideUp("slow"); // No I18N
        });


        flatThemeJquery(".login-wrap").on("click", function(e) { // No I18N
            var flatThemeJquerythis=this;
            if (!flatThemeJquery(this).hasClass("open") && !isProcessing) { // No I18N
				isProcessing = true;
                flatThemeJquery(".login-wrap.open").animate({"height":"44px"}, 400, function(){ // No I18N
                    flatThemeJquery(this).removeClass('open');// No I18N

                    if(flatThemeJquery(flatThemeJquerythis).find('input[name="DIGEST"]').length > 0)
                    {
                        reloadCaptcha(flatThemeJquery(flatThemeJquerythis).find('.captcha-container')[0]);
                    }
                        
                    if(!flatThemeJquery(flatThemeJquerythis).hasClass("user-login-form"))
                    {
                        flatThemeJquery(flatThemeJquerythis).animate({"height":"194px"},400).addClass('open'); // No I18N
                    }
                    else
                    {
					     if(document.getElementById("j_password")==undefined)
						 {
						  flatThemeJquery(flatThemeJquerythis).animate({"height":"200px"},400).addClass('open'); // No I18N
						 }
						 else
						 {
                          flatThemeJquery(flatThemeJquerythis).animate({"height":"232px"},400).addClass('open'); // No I18N
						 }
                    }

					isProcessing = false;
                });
            }
        });

        var opt = {
            controls: false,
            auto: true
        };

        flatThemeJquery(".toggle-btn").on("click",function(){ // No I18N
            if(flatThemeJquery(this).closest('.ssp-login-lhs').hasClass('open')){
                flatThemeJquery(".ssp-login-lhs").animate({"left":"-324px"},500,function(){ // No I18N
                    flatThemeJquery(this).removeClass('open'); // No I18N
                })
            }
            else{
                flatThemeJquery(".ssp-login-lhs").addClass('open').animate({"left":"0"},500,function(){ // No I18N
                });
            }
        });

        flatThemeJquery(".ssp-custom-scroll-hover").each(function() { // No I18N
            if (flatThemeJquery(this).attr('data-height')) { // No I18N
                flatThemeJquery(this).height(flatThemeJquery(this).attr('data-height')).css({ // No I18N
                    "overflow": "hidden" // No I18N
                });
            }

            var theme = (flatThemeJquery(this).attr("data-theme")) ? flatThemeJquery(this).attr("data-theme") : "light"; // No I18N
            var sPosition = (flatThemeJquery(this).attr("data-scrollbar-position")) ? flatThemeJquery(this).attr("data-scrollbar-position") : "inside"; // No I18N

            flatThemeJquery(this).mCustomScrollbar({
                theme: theme,
                autoHideScrollbar: true,
                scrollbarPosition: sPosition,
                scrollEasing:"linear",//NO I18N
                scrollInertia:60,
                mouseWheelPixels: 70,
                live:true,
                callbacks: {
                    onScroll: function(evt) {
                        return false;
                    },
                    onTotalScroll: function(evt) {
                        return false;
                    },
                    onTotalScrollBack: function(evt) {
                        return false;
                    }
                }
            });
        });
		
        flatThemeJquery('select[id="domainName"]').on('changed.bs.select', function(){// NO I18N
            isDomainChanged = true;
        });
		
    });

    function validateCaptchaFT(th)
    {
        var captcha = th.getElementsByTagName('input')[th.getElementsByTagName('input').length-1].value;
        if(captcha == '' || captcha == "不區分大小寫")
        {
            flatThemeJquery("#fTLoginAlertText",parent.document).text("請輸入圖片中顯示的字元。");   //No I18N
            flatThemeJquery("#fTLoginAlert",parent.document).delay(500).slideDown('slow') //No I18N
            return false;
        }
        return true;
    }
	
	flatThemeJquery('#j_username').on('focusout', function(){// NO I18N
	        flatThemeJquery('#j_password').removeAttr('style');// NO I18N
            checkAndDisableDomainSelection(document.DomainUserLogin);
        });	

	function showErrorOnLoadPage()	
	{
	 var loginStatus = '';
			if(loginStatus !=null && loginStatus!='')
			{
				adsjQuery("#fTLoginAlertText",parent.document).html(loginStatus);	//No I18N
				adsjQuery("#fTLoginAlert",parent.document).delay(500).slideDown('slow') //No I18N
			}
	}
			
	
 </script>
 





<script language="Javascript" src="/adsf/js/common/Cookies.js" type="text/javascript"></script>

<script>
	window.csrfCookieName="adscsrf";
	window.csrfParamName="adscsrf";
</script>
<div style="display:none" id="csrfDiv">
 <input type="hidden" id="adscsrfFromCookie" name="adscsrfFromCookie" value="7263df06-e35b-4e49-bde1-d18128fd67d2"/>
</div>
<script>

//function to check whether all the forms in a page has csrf field
function isCSRFNotPresent() {
	
	var formsCount=document.getElementsByTagName("form").length;

	if(formsCount>0){
		var formLists=document.getElementsByTagName("form");

		for(i=0;i<formLists.length;i++){
			if(formLists[i][window.csrfParamName] == undefined){
				return true;
			}
		}
	}
	return false;
}

//function to append csrf field to all the forms in a page
function appendCsrf() 
{
	var formsCount=document.getElementsByTagName("form").length;
	if(formsCount>0){
		var formLists=document.getElementsByTagName("form");

		for(i=0;i<formLists.length;i++){
			if(formLists[i][window.csrfParamName] == undefined){
				var x = document.getElementById("adscsrfFromCookie").cloneNode(true); // No I18N
				x.id = window.csrfParamName;
				x.name = window.csrfParamName;
				formLists[i].appendChild(x);
				//console.log("adding csrf hidden param to the url -> "+formLists[i].action+" form name is "+formLists[i].name+" id is "+formLists[i].id);
			}
			if(formLists[i][window.csrfParamName].value=="")
			{
				formLists[i][window.csrfParamName].value=document.getElementById("adscsrfFromCookie").value; // No I18N
			}
			formLists[i].autocomplete="off";
		}
	}
if(isCSRFNotPresent())
	appendCsrf();

}
appendCsrf();
adsjQuery(document).ready(function(){
	appendCsrf();
});
</script>

</body>
</html>



<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">





















<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<link REL="SHORTCUT ICON" HREF='&#x2f;images&#x2f;logos&#x2f;ADSSPDesktop.ico'>
<title>SFL - Password SelfService Portal</title>
<script language="JavaScript" src="/js/form-util.js?build=6403"></script>
<script language="JavaScript" src="/js/CommonUtil.js?build=6403"></script>
<script>

var jQueryLoaded = jQueryLoaded || false;
if(!jQueryLoaded)
{
	var script = document.createElement( 'script' );
	script.type = 'text/javascript';
	script.src = "/webclient/vendor/js/jquery.js?build=6403";
	if((window.name != "advancedPC_Frames") && (window.name != "advancedIDV_Frames")) //handled to avoid jquery-ui conflict, need to remove this check in future release
	{
		document.getElementsByTagName('head')[0].appendChild( script );
	}

	jQueryLoaded = true;


	var fileNamePattern = '^[a-zA-Z0-9.\\-_ ]{0,255}$';
	
	addEvent(window, 'load', setFileConstraints, false); //NO I18N

	function addEvent(element, eventString, functionReference, useCapture){
		if (element.addEventListener) {
		  element.addEventListener(eventString, functionReference, useCapture);
		}
		else if (element.attachEvent) {
		  element.attachEvent('on'+eventString , functionReference );
		}
	}

	function setFileConstraints()
	{
	try{
		if(fileNamePattern != ''){
			var tip = '檔案名稱應該遵守正則花樣{0}'.replace('{0}', fileNamePattern);
			var errorMsg = '在檔案名中發現無效字元。它應遵循規則運算式模式{0}'.replace('{0}', fileNamePattern);
			var elements = document.getElementsByTagName('input');
			for(i in elements){
				var element = elements[i];
				var elementType = element.type;
				if(elementType == 'file' && element.getAttribute('patternText') == null){
					element.setAttribute('patternText', fileNamePattern);
					if(typeof setADSTooltip == 'function'){
					setADSTooltip(element, tip);
					}
					else{
						element.title = tip;
					}

					addEvent(element, 'change', function(event){ //NO I18N
						var files = this.files;
						for (var i = 0; i < files.length; i++) {
							if(!(new RegExp(fileNamePattern)).test(files[i].name))
							{
								if(typeof preventDefaultAction == 'function'){
									preventDefaultAction(event);
								}
								this.value = '';
								if(typeof parent.alertMsg == 'function'){
									parent.alertMsg(errorMsg);
								}
								else if(typeof alertMsg == 'function'){
									alertMsg(errorMsg);
								}
								return false;
							}
						}
					}, true);
				}
			}
		}	
		}
		catch(err){}		
	}
}

</script>



<script language="JavaScript" src="/webclient/vendor/js/jquery.js?build=6220" type="text/javascript"></script>
<script language="JavaScript" src="/js/jquery-ui.min.js" type="text/javascript"></script>
<script type="text/javascript">
var adsjQuery = jQuery.noConflict();
</script>




















<!-- ADS Imports -->


<!-- IDMF Imports -->



<!-- SSP Imports -->










<!-- Old Packages -->



































<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>


 







<head>
	

<style>
 .fntFamily{font-family: verdana;}
 .fntSize{font-size:10px;}
 .common-textcolor{color:#04426c !important;}
 .common-bgcolor{background:#04426c !important;}
 .common-bordercolor{border-color:#04426c !important;}
 .adsfntFamily{font-family: verdana !important;}
 .adsfntSize{font-size:10px !important;}
 .adsfontSize10{font-size:10px !important;}
 .adsfontSize11{font-size:10px !important;}
</style>


</head>

</html>


<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<script language="JavaScript" src="/js/form-util.js?build=6403" type="text/javascript"></script>
	<script language="JavaScript" src="/adsf/js/common/JumpTo.js" type="text/javascript"></script>
	<script language="JavaScript" src="/adsf/js/common/security-core.js" type="text/javascript"></script>
	<script language="JavaScript" src="/adsf/js/common/security-url-validator.min.js" type="text/javascript"></script>
	<script language="JavaScript" src="/adsf/js/common/sso/CustomSSO.js" type="text/javascript"></script>
	<script language="JavaScript" src="/adsf/js/common/WindowHandler.js" type="text/javascript"></script>
	<script type="text/javascript">
		var insideAppsPane = true; //Settings insideAppsPane=true for ignoring loadframe issue in AD360
	</script>
	<script>
        document.addEventListener('DOMContentLoaded', function() {
            $('.login-dropdown').on('show.bs.select',function(){ //No I18N
                $('ul.dropdown-menu.inner').mCustomScrollbar() //No I18N
                $(".mCSB_dragger_bar,.mCSB_2_dragger_vertical,.mCSB_draggerContainer").on("click", function(e) { //No I18N
                    return false;
                });
            })
        });
    </script>

	<script>
		var csrfCookieFromRequest = '7263df06\x2De35b\x2D4e49\x2Dbde1\x2Dd18128fd67d2';
		var csrfParam = 'adscsrf\x3D7263df06\x2De35b\x2D4e49\x2Dbde1\x2Dd18128fd67d2';
		var contextPath = "";
		var login_status = '\u8ACB\u91CD\u65B0\u8F38\u5165\u5716\u50CF\u4E2D\u7684\u5B57\u5143\u3002';

		function showErrorOnLoad()
		{
			if($('#fTLoginAlert',parent.document).length==0)
			{
				var statusDiv='<div class="mini-status-alert status-alert status-alert-danger status-has-parent status-alert-xmd alert-dismissible ssp-display-none" role="alert" id="fTLoginAlert"><button type="button" class="close" onclick="closeDomainAlertDiv()"><span aria-hidden="true">&times;</span></button><div class="ssp-text-color-default ssp-align-center text-md"><i class="ssp-inline-icon icn-cross ssp-mt-m2"></i><span id="fTLoginAlertText"></span></div></div>'; //No I18N
				$(parent.document.body).append(statusDiv);
			}
			if(login_status !=null && login_status!='')
			{
				adsjQuery("#fTLoginAlertText",parent.document).html(login_status);	//No I18N
				adsjQuery("#fTLoginAlert",parent.document).delay(500).slideDown('slow') //No I18N
			}
		}

		function closeDomainAlertDiv()
		{
			adsjQuery("div.status-alert").slideUp("slow"); // No I18N
		}

		function redirectTo(url)
		{		
			var useCustomizedHTML = 'true';
			if(useCustomizedHTML == 'true')
			{
				window.parent.location.replace(url);
			}
			else
			{
				window.location.replace(url);
			}	
    	}	
	</script>
	<div style="display:none" id="csrfDiv">
		<input type="hidden" id="adscsrfFromCookie" name="adscsrfFromCookie" value="7263df06-e35b-4e49-bde1-d18128fd67d2"/>
	</div>
	

 
 <link href="/styles/styles.css" rel="stylesheet" type="text/css">
 <link rel="stylesheet" href="/webclient/assets/login.css">
 	
	<style>
		.employeeSearchContainer #employeeSearch.open {
			background-color: #fff!important;
		}
		body .ssp-chart-link:hover {
			background: #fff !important;
		}
	</style>
 
 <link href="styles/customer-styles.css" rel="stylesheet" type="text/css">

 <style>
         .fntFamily{font-family: verdana;}
         .fntSize{font-size:10px;}
         .common-textcolor{color:#04426c !important;}
         .common-bgcolor{background:#04426c !important;}
         .common-bordercolor{border-color:#04426c !important;}
 </style>



<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

<script language=JavaScript src="/js/popLayer.js?build=6403" type="text/javascript"></script>
<script language=JavaScript src="/js/Esearch.js?build=6403" type="text/javascript"></script>
<script language=JavaScript src="/js/common/LanguageList.js?build=6403" type="text/javascript"></script>
<script language=JavaScript src="/js/validation.js?build=6403" type="text/javascript"></script>
<script language=JavaScript src="/js/ValidateLogon.js?build=6403" type="text/javascript"></script>
<script language=JavaScript src="/js/layout/Utils.js?build=6403" type="text/javascript"></script>
<script language=JavaScript src="/js/CustomLogonScript.js?build=6403" type="text/javascript"></script>
<script language=JavaScript src="/js/AjaxAPI.js?build=6403" type="text/javascript"></script>

<script language=JavaScript src="/js/CommonUtil.js?build=6403" type="text/javascript"></script>
<script>
	function docid(id){return document.getElementById(id);}
	adsjQuery(document).ready(function() {
        function onInputChange(inputRef)
        {
            var state = adsjQuery(inputRef).length>0 && $(inputRef).val().length>0;
            var passwordIcon = adsjQuery(inputRef).siblings('span.add-on.form-icn-password'); //NO I18N
            passwordIcon.toggleClass('hide', state); //NO I18N
        }
        function restoreIconOnLoginPage(inputRef)
        {
        	var passwordIcon = adsjQuery(inputRef).siblings('span.add-on.form-icn-password'); //NO I18N
        	var eyeIcon = adsjQuery(inputRef).siblings('span.add-on.form-icn-eye'); //NO I18N
        	passwordIcon.removeClass('hide'); //NO I18N
        	eyeIcon.addClass('hide'); //NO I18N
        }
        initPasswordVisibility(".form-input-with-icon .linput", "span.add-on.form-icn-eye", 'open', null, onInputChange, restoreIconOnLoginPage); //NO I18N
    });
</script>

		<script language=JavaScript src="/js/common/LanguageList.js?build=6403" type="text/javascript"></script>


</head>

<div id="SSOStatus" style="position:fixed;z-index:500; left:45%; top:40%; display:none;">
	<table cellspacing="0" cellpadding="5" border="0" class="sucessMsg" align="center" style="background:  rgb(242,242,242);border-color:#FFF; padding:5px 10px;">
		<tr>
			<td><img src="webclient/images/loader-sm.gif" > 正在載入...</td>
		</tr>
	</table>
</div>

<div id="IframeLoadingStatus" style="position:fixed;z-index:500; left:45%; top:40%; display:none;">
	<table cellspacing="0" cellpadding="5" border="0" class="sucessMsg" align="center" style="background:  rgb(242,242,242);border-color:#FFF; padding:5px 10px;">
		<tr>
			<td><img src="webclient/images/loader-sm.gif" > 正在載入... </td>
		</tr>
	</table>
</div>

<style>
.transparentbg
{
	background:none transparent;
	overflow:hidden;
}
.loginBgImageClass
{
	background:url('images\2f ssp\2d login\2d page\2e png') no-repeat fixed;
	background-size: cover;
}
</style>
<body onload="createLangBox();createSearch('AnonymousAccess');" onclick="hideOnBlur(event, 'DOWN_ARROW', 'SEARCH_CRI')" class="transparentbg ssp_theme_royalblue ">




	

<script language="JavaScript" src="/js/report.js" type="text/javascript"></script>
<script language="JavaScript" src="/js/popLayer.js" type="text/javascript"></script>
<script>
function alertBoxOk(callBackFunc, params){if(callBackFunc != "") { 
	if(self!=top)
	{
		self.eval(callBackFunc)(params);
    }
    else{
        parent.eval(callBackFunc)(params);
	} 
	}}
function confirmBoxOk(callBackFunc, params){if(eval(callBackFunc)(params)!=false){closeAndUnFreeze('confirmBox');document.body.style.overflow = 'auto';if(eval(window.event)){window.event.returnValue=false;}}}
function confirmBoxCancel(cancelFunc,cancelParams) 
{
	closeAndUnFreeze('confirmBox'); //No I18N
	document.body.style.overflow = 'auto'; //No I18N
	if(cancelFunc !="undefined" && cancelFunc!="") //Since already set to confirmBoxCancelFun <input> element, string check done, instead typeof.
	{	
		eval(cancelFunc)(cancelParams);
	}
	if(eval(window.event)){window.event.returnValue=false;}
}
function docid(id){return document.getElementById(id);}
function alertMsg(msg, leftPos, topPos, scrollTop, fn)
{
	if(scrollTop == "" || (typeof scrollTop == "undefined")){window.scrollTo(0,0);}
	if (window.ActiveXObject){var cW = document.body.clientWidth;var cH = document.body.clientHeight;}
	else{var cW = window.innerWidth;var cH = window.innerHeight;}
	var mouseLeft = new Number((cW/2) - 100);var mouseTop = new Number((cH/2) - 130);

	if(typeof leftPos != "undefined" && leftPos != null){mouseLeft = leftPos;}
	if(typeof topPos != "undefined" && topPos != null){mouseTop  = topPos;}

	freezeAndOpen('alertBox', mouseLeft, mouseTop);//No I18N
	var searchElem = document.getElementById('SEARCH_STRING');
	if(eval(searchElem)) { searchElem.setAttribute('tabIndex', '-1'); }

	docid('alertBox').style.left=mouseLeft+"px";
	docid('alertBox').style.top=mouseTop+"px";
	docid('alertBox').style.display="block";

	docid('alert_InsertMsg').innerHTML = msg;
	docid('freezeSearch').setAttribute('style','display:block; width:'+cW+100+'px; height:'+cH+100+'px;');
	document.body.style.overflow = 'hidden';

	if(typeof fn != "undefined" && fn != null){docid('alertBoxFun').value = fn;}
	
	setTimeout(function() { if(eval(docid('alertBoxFocus'))){try{docid('alertBoxFocus').focus();}catch(err){}}}, 20);
	showHideCombBoxes('hidden');//No I18N 
	return false;
}
function confirmMsg(msg, fun, par,cancelFunc,cancelParam)
{
	window.scrollTo(0,0);
	if (window.ActiveXObject){var cW = document.body.clientWidth;var cH = document.body.clientHeight;}
	else{var cW = window.innerWidth;var cH = window.innerHeight;}
	var mouseLeft = new Number((cW/2) - 100);var mouseTop = new Number((cH/2) - 130);

	freezeAndOpen('confirmBox', mouseLeft, mouseTop);//No I18N
	docid('confirmBox').style.left=mouseLeft+"px";
	docid('confirmBox').style.top=mouseTop+"px";
	docid('confirmBox').style.display="block";
	
	docid('confirm_InsertMsg').innerHTML = msg;
	docid('confirmBoxFun').value = fun;
	docid('confirmBoxParams').value = par;
	docid('confirmBoxCancelFun').value = cancelFunc;
	docid('confirmBoxCancelParams').value = cancelParam;
	docid('freezeSearch').setAttribute('style','width:'+cW+100+'px; height:'+cH+100+'px;');
	document.body.style.overflow = 'hidden';
	docid('freezeSearch').style.display="block";
	setTimeout(function() { if(eval(docid('confirmBoxFocus'))){try{docid('confirmBoxFocus').focus();}catch(err){}}}, 20);
	showHideCombBoxes('hidden');//No I18N	
	return false;
}
function showHideCombBoxes(st){
var selectObjs = document.getElementsByTagName('select');
for(var i=0; i<selectObjs.length;i++){if(eval(selectObjs[i])){selectObjs[i].style.visibility=st;}}}
</script>


<div class="alertDiv" id="confirmBox" style="position:absolute;z-index:5000; left:90px; top:85px; display:none;padding:2px;">
	<input type="hidden" id="confirmBoxFun">
	<input type="hidden" id="confirmBoxParams">
	<input type="hidden" id="confirmBoxCancelFun">
	<input type="hidden" id="confirmBoxCancelParams">
	<div class="common-bgcolor padding5">
		<div class="flLeft normalBoldFont whitefont fntFamily fontSize11" id="CONFIRM_BOX_HEADER">確認資訊</div>
		<div class="flRight divCloseBtn" onclick="closeAndUnFreeze('confirmBox')"></div>
		<div class="clearAll"></div>
	</div>
	<div class="padding10">
		<div class="flLeft confirmboxIcon" id="CONFIRM_BOX_ICON"></div>
		<div style="padding-top:5px" id="confirm_InsertMsg" class="fntFamily fntSize marginLeft40"></div>
		<div class="clearAll"></div>
	</div>	
	<div class="marginLeft100" style="margin-bottom:5px">
		<div class="flLeft alertBtn curPoint" onclick="confirmBoxOk(document.getElementById('confirmBoxFun').value, document.getElementById('confirmBoxParams').value)">
			<div class="flLeft left"></div>
			<div class="flLeft center"><a href="javascript:void(0)" id="confirmBoxFocus" class="normalFont fntSize fntFamily" onkeydown="if(event.keyCode==13){confirmBoxOk(docid('confirmBoxFun').value, docid('confirmBoxParams').value);}" >確定</a></div>
			<div class="flLeft right"></div>
		</div>

		<div class="flLeft">&nbsp;</div>

		<div class="flLeft alertBtn curPoint" onclick="confirmBoxCancel(document.getElementById('confirmBoxCancelFun').value, document.getElementById('confirmBoxCancelParams').value)">
			<div class="flLeft left"></div>
			<div class="flLeft center fntSize fntFamily">取消</div>
			<div class="flLeft right"></div>
		</div>
	</div>
	<div class="clearAll"></div>
</div>


<div class="alertDiv" id="alertBox" style="position:absolute;z-index:5000; left:90px; top:85px; display:none;padding:2px;">
	<input type="hidden" id="alertBoxFun">
	<input type="hidden" id="alertBoxParams">
	<div class="common-bgcolor padding5">
		<div class="flLeft normalBoldFont whitefont fntFamily fontSize11">告警信息</div>
		<div class="flRight divCloseBtn" onclick="closeAndUnFreeze('alertBox'); alertBoxOk(docid('alertBoxFun').value, docid('alertBoxParams').value);"></div>
		<div class="clearAll"></div>
	</div>
	<div class="padding10">
		<div class="flLeft alertboxIcon"></div>
		<div id="alert_InsertMsg" class="fntFamily fntSize marginLeft40"></div>
		<div class="clearAll"></div>
	</div>	
	<div style="margin-left:125px;margin-bottom:5px;float:left">
		<div class="alertBtn curPoint" onclick="closeAndUnFreeze('alertBox'); alertBoxOk(docid('alertBoxFun').value, docid('alertBoxParams').value);">
			<div class="flLeft left"></div>
			<div class="flLeft center"><a href="javascript:void(0)" id="alertBoxFocus" class="normalFont fntSize fntFamily"  >確定</a></div>
			<div class="flLeft right"></div>
		</div>
	</div>
	<div class="clearAll"></div>
</div>

<div id="SMALL_STATUS_BOX" style="position:absolute;z-index:5000; left:540px; top:260px; width:400px; display:none;">
	<table cellspacing="0" cellpadding="0" border="0" width="80%" class="FailureMsg fntSize fntFamily">
	<tr>
		<td align="center" id="SSB_TXT"></td>
		<td align="right" valign="top"><div class="divCloseBtn" onclick="hCEle('SMALL_STATUS_BOX','none')" ></div></td>
	</tr>
	</table>	
</div>