import json
import pandas as pd
import requests

# set the API endpoint and parameters
url = 'https://services.nvd.nist.gov/rest/json/cves/2.0/?pubStartDate=2024-02-11T00:00:00.000&pubEndDate=2024-02-26T00:00:00.00'
# send a GET request to the API endpoint and get the response
response = requests.get(url)
data = response.json()

# create a list of dictionaries containing CVE ID and exploit status
vulns = []
for vuln in data['vulnerabilities']:
    #print(vuln)
    cve_id = vuln['cve']['id']
    exploit = 'no'
    # check if references contain tag with 'Exploit' keyword
    for ref in vuln['cve']['references']:
        #print(ref)
        if 'tags' in ref and 'Exploit' in ref['tags']:
            exploit = 'yes'
            break
    # add CVE ID and exploit status to list
    vulns.append({'CVE ID': cve_id, 'Exploit': exploit})

# create a pandas DataFrame from the list of dictionaries
df = pd.DataFrame(vulns)

# save DataFrame to Excel file
df.to_excel(r'D:\RD\2024\feb\two.xlsx', index=False)

print("done")