import json
import pandas as pd
import requests

# set the API endpoint and parameters
url = 'https://services.nvd.nist.gov/rest/json/cves/1.0/?pubStartDate=2023-02-27T00:00:00.000&pubEndDate=2023-03-31T08:48:00.000'

# send a GET request to the API endpoint and get the response
response = requests.get(url)
data = response.json()

# create a list of dictionaries containing CVE ID and exploit status
vulns = []
for vuln in data['vulnerabilities']:
    cve_id = vuln['cve']['id']
    exploit = 'no'
    # check if references contain tag with 'Exploit' keyword
    for ref in vuln['cve']['references']:
        if 'tags' in ref and 'Exploit' in ref['tags']:
            exploit = 'yes'
            break
    # add CVE ID and exploit status to list
    vulns.append({'CVE ID': cve_id, 'Exploit': exploit})

# create a pandas DataFrame from the list of dictionaries
df = pd.DataFrame(vulns)

# save DataFrame to Excel file
df.to_excel('vulns2.xlsx', index=False)