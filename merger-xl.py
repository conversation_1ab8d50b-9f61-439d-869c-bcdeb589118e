import os
import pandas as pd

# Replace 'folder_path' with the path to your folder containing Excel files
folder_path = r'D:\RD\qualys vs haiku\was'

# Initialize an empty list to store DataFrames
dfs = []

# Iterate through files in the folder
for filename in os.listdir(folder_path):
    if filename.endswith(".xlsx"):
        file_path = os.path.join(folder_path, filename)
        df = pd.read_excel(file_path)
        dfs.append(df)

# Concatenate the DataFrames into a single DataFrame
merged_df = pd.concat(dfs, ignore_index=True)

# Save the merged DataFrame to a new Excel file
merged_df.to_excel(r"D:\RD\qualys vs haiku\was\merged_data.xlsx", index=False)
