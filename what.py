import requests
from bs4 import BeautifulSoup

# URL of the webpage to check
url = "http://testphp.vulnweb.com/login.php"

def has_captcha(url):
    # Send a GET request to the URL
    response = requests.get(url)
    
    # Parse the HTML content of the page using BeautifulSoup
    soup = BeautifulSoup(response.content, "html.parser")
    
    # Find the captcha element on the page
    captcha_element = soup.find_all("captcha")
    print(captcha_element)
    
    # Check if captcha element is missing
    if captcha_element is None:
        return False
    else:
        return True
# Send a GET request to the URL and get the HTML content
response = requests.get(url)
html_content = response.content

# Parse the HTML content using BeautifulSoup
soup = BeautifulSoup(html_content, "html.parser")

# Check if the page contains input fields for username and password and a submit button

password_fields = soup.find_all("input", {"type": "password"})
password_fields += soup.find_all("input", {"name": lambda name: "password" in (name or '')})
password_fields += soup.find_all("label", text=lambda text: "password" in (text or ''), recursive=True)



if  password_fields:
    print("The webpage is a login page")
else:
    print("The webpage is not a login page")
    has_captcha()




