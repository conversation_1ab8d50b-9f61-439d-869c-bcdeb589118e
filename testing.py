

import requests
import urllib
from bs4 import BeautifulSoup
import pandas as pd
import numpy as np
import yagmail
from datetime import datetime,timedelta,tzinfo
from dateutil import tz
import openpyxl
from openpyxl import Workbook
from openpyxl import load_workbook
from openpyxl.styles import Border, Side, Alignment, Font
import time
from stem import Signal
from stem.control import Controller
from pandas import ExcelWriter
import os
from urllib.request import Request, urlopen
import ssl
import re
import shutil

import warnings
warnings.filterwarnings("ignore")

date=[]
vulnsource=[]
vulnname=[]
zday=[]
poc=[]
descrip=[]
des=[]
publicid=[]
temp=[]
temp1=[]
temp2=[]
productname=[]
basescore=[]
vendor=[]
advisory=[]
link=[]
classname=[]
local=[]
remote=[]

from_zone = tz.gettz('UTC')
to_zone = tz.gettz('Asia/Kolkata')
current=datetime.today()#current date
current = current.replace(tzinfo=from_zone)
current = current.astimezone(to_zone)
current1=current.strftime('%Y-%m-%d %H:%M:%S')


def loginToOpenVas():
    url = 'https://secinfo.greenbone.net/login'
    values = {'cmd':'login','text':'/gmp?r=1','login': 'gbpsguest',
          'password': 'HalloGreenbone2020'}
    s = requests.session()
    r = s.post(url, values,verify=False)
    if r.status_code == 200:
        soup = BeautifulSoup(r.text, 'html.parser')
        print(r.text)
        token = soup.find_all('token')
        for text in token:
            token = text.string
            print(token)

##        quote_page = 'https://secinfo.greenbone.net/gmp?cmd=get_info&info_type=nvt&info_name=CVE-1999-0678&details=1&filter=&filt_id=0&token=%s' % (token)
##        r1 = s.get(quote_page)
##        print(r1.text)
        return s,token
    else:
        return None
def currentMonthExcelFile():
    global currentTime, currentMonth
    global excelFilePath, monthlyExcelFiltered, monthlyExcelFilteredBackup
    
    currentMonth = current.strftime('%Y%m')
    currentTime = current.strftime('%Y-%m-%d_%H-%M')
    
    #below path for dynamic files
    #excelFilePath = r"C:\Users\<USER>\Downloads\Compressed"+currentMonth+\
     #               r"vulndbresult_"+currentTime+".xlsx
    #below path if to use static files
    excelFilePath = r"C:\Users\<USER>\Downloads\Compressed\vulndbformat.xlsx"
##    monthlyExcelFilteredBackup = "/opt/ET_Automation_RSSFeed/Vulndb_Files/"+currentMonth+"/vulndbresult_Monthly_"+currentMonth+"_backup.xlsx"
    dirLocation = os.path.dirname(excelFilePath)
    print(dirLocation)
    

loginToOpenVas()
#currentMonthExcelFile()

def fetchWriteCVE():
    headerFont = Font(bold=True)
    thin = Side(border_style="thin", color="000000")
    wb_obj = openpyxl.load_workbook(excelFilePath) 
    sheet_obj = wb_obj.active
    m_row = sheet_obj.max_row

    columnHeader = sheet_obj.cell(row = 1,column = 3)
    #columnHeader.value = "CVE-ID"
    columnHeader.font = Font(b=True)
    columnHeader.border = Border(top=thin, left=thin, right=thin, bottom=thin)
    wb_obj.save(excelFilePath)
    
    for i in range(2, m_row+1):
        #renew_tor_ip()
        time.sleep(1)
        session = requests.session()
        #Fetch value to vuln db source link
        vulnDB_Link = sheet_obj.cell(row = i, column = 3)
        vulnDB_Link = vulnDB_Link.value
        time.sleep(1);
        session.proxies = {}

        session.proxies['http'] = 'socks5://127.0.0.1:9050'
        session.proxies['https'] = 'socks5://127.0.0.1:9050'
        
        l=session.get('http://httpbin.org/ip').text
        print(l)
        vulnDBPage=session.get(vulnDB_Link).text
        soup = BeautifulSoup(vulnDBPage, 'html.parser')
        try:
            print("inside try block for fetching CVE from vulndb")
            cve_Val = soup.find('a',{'target': 'cve'}).text
            print(cve_Val)
            #Write CVE value to CVE column
            cveID = sheet_obj.cell(row = i,column = 4)

            cveID.value = cve_Val
            #Based on CVE fetch the openvas plugin id's
            if cveID.value != None or cveID.value != "Null" :
                try:
                    session, tokenVal = loginToOpenVas()
                    if tokenVal == None or session == None:
                        raise Exception('login unsuccessful')
                except:
                    print("Exception occuring in fetching the token value hence retrying")
                    time.sleep(5)
                    session, tokenVal = loginToOpenVas()
##                cve_Val='CVE-2021-3110'
                openVasLink = 'https://secinfo.greenbone.net/gmp?token='+\
                 str(tokenVal)+'&cmd=get_info&info_type=nvt&filter='+str(cve_Val)+\
                 '&Update+Filter.x=0&Update+Filter.y=0&'+\
                'filter_extra=sort-reverse%3Dcreated+rows%3D10+first%3D1+'
                print(openVasLink)
                time.sleep(1);
                req = session.get(openVasLink)
##                req = Request(openVasLink, headers={'User-Agent': 'Mozilla/5.0'})
                #creating ssl context to bypass issue of ssl certificate verification
##                openVasPage = urlopen(req,context = ssl._create_unverified_context()).read()
                soup1 = BeautifulSoup(req.text, 'html.parser')
                plugin_coverage = sheet_obj.cell(row = i,column = 20)
                pluginID_c1 = sheet_obj.cell(row = i,column = 21)
                try:
                    print("inside try block for fetching openvas plugin id")
##                    table = soup1.find('info' ,attrs={'id': 'sc-bdVaJa sUcSo'})
##                    print(table)
                    pluginID = soup1.find('info').attrs['id'].strip()
##                    statString = "View details of NVT "
##                    pluginID = vendor.replace(statString, "")
                    print(pluginID)

                    plugin_coverage.value = "Y"
                    pluginID_c1.value = pluginID
                    
                except Exception as e:
                    print("inside except block for fetching openvas plugin id")
                    print("Error Message \n:",str(e))
                    
                    plugin_coverage.value = "N"
                    pluginID_c1.value = "Na"
                    print(plugin_coverage.value,pluginID_c1.value)
                    wb_obj.save(excelFilePath)
                    continue
            wb_obj.save(excelFilePath)
        except Exception as e:
            print("inside except block for fetching CVE from vulndb")
            print("Error Message \n:",str(e))
            wb_obj.save(excelFilePath)
            continue

