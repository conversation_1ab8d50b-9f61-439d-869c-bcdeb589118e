from flask import Flask, request, render_template_string

app = Flask(__name__)

@app.route('/')
def index():
    name = request.args.get('name', 'Guest')
    
    html = f'''
    <!DOCTYPE html>
    <html>
    <head>
        <title>CSS Injection Lab</title>
    </head>
    <body>
        <h2>Welcome to the CSS Injection Lab</h2>
        <form method="GET">
            <label>Enter your name:</label><br>
            <input type="text" name="name" placeholder="Your name" />
            <button type="submit">Submit</button>
        </form>
        <hr>
        <div>
            <strong>Output:</strong> Hello, <span id="output">{name}</span>!
        </div>
        <br>
        <p>Try injecting some CSS into the input field.</p>
    </body>
    </html>
    '''

    return render_template_string(html)

if __name__ == '__main__':
    app.run(debug=True)
