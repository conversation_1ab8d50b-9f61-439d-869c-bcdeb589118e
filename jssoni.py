import os
import json
import pandas as pd

# Dictionary to store extracted values
payload_data = {}

# Parent folder path
parent_folder_path = r"D:\main"

# Iterate over each child folder
for folder_name in os.listdir(parent_folder_path):
    child_folder_path = os.path.join(parent_folder_path, folder_name)

    # Check if the path is a directory
    if os.path.isdir(child_folder_path):
        # Iterate over each JSON file in the child folder
        for file_name in os.listdir(child_folder_path):
            if file_name.endswith(".json"):
                file_path = os.path.join(child_folder_path, file_name)

                with open(file_path, "r") as f:
                    data = json.load(f)

                    # Extract payload information from each JSON file
                    for item in data["payload"]:
                        payload = {
                            "Folder Name": folder_name,
                            "JSON Name": file_name,
                        }

                        for field, value in item.items():
                            if field not in payload:
                                payload[field] = value

                        # Add extracted payload to the dictionary
                        if folder_name not in payload_data:
                            payload_data[folder_name] = []

                        payload_data[folder_name].append(payload)

# Create a DataFrame from the extracted payload data
df = pd.DataFrame(payload_data)

# Save DataFrame to Excel file
output_file = "D:\output.xlsx"
df.to_excel(output_file, index=False)

print("Excel file saved successfully.")
