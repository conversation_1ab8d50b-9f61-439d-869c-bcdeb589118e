import pandas as pd

# Read the Excel sheet into a DataFrame
df = pd.read_excel(r'D:\RD\Haiku-new\node\scanner\vulns\www.practo.com-std-vulns-325.txt')

# Load the search strings from the text file
with open('string.txt', 'r') as file:
    search_strings = [line.strip() for line in file]

# Create a new column 'found' and initialize with empty values
df['found'] = ''

# Use apply function to iterate over the 'para' column and check for the search strings
df['found'] = df['para'].apply(lambda para_value: 'header http req' + ' ' + str(para_value) if any(search_string in str(para_value) for search_string in search_strings) else '')

# Save the modified DataFrame back to the Excel file
df.to_excel(r'D:\RD\may2023\resultjune.xlsx', index=False)