import os
from glob import glob
import yt_dlp
import streamlit as st

# Initialize session state
if "video_path" not in st.session_state:
    st.session_state.video_path = ""

# Remove video
def remove_video():
    try:
        os.remove(st.session_state.video_path)
        st.session_state.video_path = ""
    except FileNotFoundError:
        st.error("File not found.")

# Remove all videos
def remove_all_videos():
    videos = glob(os.path.join("data", "*"))
    for video in videos:
        try:
            os.remove(video)
        except FileNotFoundError:
            st.error(f"Couldn't delete file: {video}")

# Download video
def download_video(url, save_path):
    video_id = url.split("/")[-1] or url.split("/")[-2]
    ydl_opts = {
        'outtmpl': os.path.join(save_path, f'{video_id}.%(ext)s'),
        'cookies': st.session_state.get("cookie_file")
    }
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        ydl.download([url])
    
    video_files = glob(os.path.join(save_path, f"{video_id}.*"))
    if video_files:
        return video_files[0]
    return None

# Page configuration
st.title("Download Any Video")
with st.sidebar:
    st.subheader("Manage Cookies")
    cookie_file = st.file_uploader("Upload Cookie File", type=["txt", "json"])
    if cookie_file is not None:
        st.session_state.cookie_file = cookie_file
    st.button("Clear All Videos", on_click=remove_all_videos)

# Main UI
url = st.text_input("Video URL", placeholder="Enter Video URL and Hit Enter")
if url:
    st.session_state.video_path = download_video(url, "data")
    if st.session_state.video_path:
        st.video(st.session_state.video_path)
        try:
            with open(st.session_state.video_path, "rb") as video_file:
                st.download_button("Download Video", video_file, file_name=os.path.basename(st.session_state.video_path), mime="video/mp4")
        except Exception as e:
            st.error("Failed to prepare the video for download.")
