from flask import Flask, request

app = Flask(__name__)

@app.route('/index.php', methods=['POST'])
def handle_post():
    # Read the PHP code from the body of the request
    php_code = request.data.decode('utf-8')
    
    # Simulate running the PHP code and generating the hash
    if '<?php echo md5("CVE-2024-4577"); ?>' in php_code:
        return '3f2ba4ab3b260f4c2dc61a6fac7c3e8a', 200
    else:
        return 'Invalid request', 400

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
