import pandas as pd

# Load the Excel file (as the file extension suggests it's an Excel file)
excel_file_path = r'D:\OneDrive - Indusface Private Limited\RD-new\RD\2024\Application Detection\app\napp.xlsx'
data = pd.read_excel(excel_file_path, engine='openpyxl')  # Corrected to read an Excel file

# Print the column names to verify the correct column name
print("Column names in the Excel file:", data.columns)

# Assuming the column name is 'Result', update this if the column name is different
column_name = 'Result'  # Update this if the column name is different

if column_name in data.columns:
    # Extract the relevant column
    results = data[column_name]

    # Function to parse application detection data
    def parse_application_data(entry):
        apps = []
        # Ensure that the entry is a string
        entry = str(entry)
        
        components = entry.split(" ")
        i = 0
        while i < len(components):
            # Look for the first component that contains a version number
            if ":" in components[i]:
                app_name_version = components[i].split(":")
                app_name = app_name_version[0]
                app_version = app_name_version[1]
                # Check if there are more parts to the name
                j = i + 1
                while j < len(components) and ":" not in components[j]:
                    app_name += " " + components[j]
                    j += 1
                apps.append({"Application Name": app_name, "Version": app_version})
                i = j  # Move to the next component after the current app name
            else:
                i += 1
        return apps

    # Create a list to store all parsed application data
    all_parsed_data = []

    # Apply the parsing function to each entry and collect the data
    for result in results:
        parsed_data = parse_application_data(result)
        all_parsed_data.extend(parsed_data)

    # Convert the parsed data to a DataFrame
    parsed_df = pd.DataFrame(all_parsed_data)

    # Top Applications
    top_applications = parsed_df['Application Name'].value_counts().reset_index()
    top_applications.columns = ['Application Name', 'Count']

    # Top Versions per Application
    top_versions = parsed_df.groupby(['Application Name', 'Version']).size().reset_index(name='Count')
    top_versions = top_versions.sort_values(by=['Application Name', 'Count'], ascending=[True, False]).drop_duplicates('Application Name')

    # Save all DataFrames to one Excel file with different sheet names
    output_excel_path = r'D:\OneDrive - Indusface Private Limited\RD-new\RD\2024\Application Detection\app\re-aapps.xlsx'
    with pd.ExcelWriter(output_excel_path) as writer:
        parsed_df.to_excel(writer, sheet_name='result app', index=False)
        top_applications.to_excel(writer, sheet_name='top application', index=False)
        top_versions.to_excel(writer, sheet_name='top version as per application', index=False)

    print(f"Data has been saved to {output_excel_path} with multiple sheets.")

else:
    print(f"Column '{column_name}' not found in the Excel file. Please check the column names.")
