import webbrowser

# read list of items from file
with open('open-link.txt') as f:
    items = f.readlines()

# strip whitespace and remove empty lines
items = [item.strip() for item in items if item.strip()]

# loop through items and open search results in new tab
for item in items:
    #url = f'https://www.google.com/search?q={item}'
    #url =https://twitter.com/search?q=CVE-2023-2254
    #url = f'https://twitter.com/search?q={item}'
    url = f'https://nvd.nist.gov/vuln/detail/{item}'
    #url = f'https://www.google.com/search?q={item}'
    webbrowser.open_new_tab(url)