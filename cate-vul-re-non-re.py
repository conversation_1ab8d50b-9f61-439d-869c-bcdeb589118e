
import pandas as pd


data = pd.read_excel(r'D:\RD\2024\jan\janet.xlsx')

#data= data[data["Vuln Name"].str.startswith('MVID-' or 'VBD')==False]
required_colum_name = "Vuln Name"
#data= data[data["Vuln Name"].str.startswith('MVID-') or ('VBD')==False] # remove unwanted dats from Vuln Name
# print(data[required_colum_name])

#segragation cve and vuln title

# read the row and append value on ther coulmn
vul_name_list = []
relevent_list = []

#read vuln tile and filter out relvant, owasap and insert new column vuln category 

for row in data[required_colum_name]:
    # print(row)
    if "cross site scripting" in row:
        vul_name_list.append("cross site scripting")
    elif "sql injection" in row:
        vul_name_list.append("sql injection")
    elif ("xxe" in row) or  ("xml external entity" in row):
        vul_name_list.append("xml external entity")
    elif "cross-site request forgery" in row:
        vul_name_list.append("cross-site request forgery")
    elif ("path traversal" in row ) or ("pathname traversal" in row):
        vul_name_list.append("path traversal")
    elif ("file inclusion") in row:
        vul_name_list.append("path traversal")
    elif ("command injection" in row)  or ("os command" in row):
        vul_name_list.append("command injection")
    elif ("buffer overflow") in row:
        vul_name_list.append("buffer overflow")
    elif ("access control") in row:
        vul_name_list.append("access control")
    elif ("denial of service") in row:
        vul_name_list.append("DDOS/DOS")
    elif ("Kernel") in row:
        vul_name_list.append("Kernel")
    elif "use after free" in row:
        vul_name_list.append("use after free")
    elif ("router") in row:
        vul_name_list.append("xml external entity")
    elif "Privilege Escalation" in row:
        vul_name_list.append("Privilege Escalation")
    elif ("stack-based overflow" in row ) or ("stack-based" in row):
        vul_name_list.append("stack-based overflow")
    elif ("out-of-bounds") in row:
        vul_name_list.append("out-of-bounds")
    elif ("missing authentication") in row:
        vul_name_list.append("missing authentication")
    elif ("buffer overflow") in row:
        vul_name_list.append("buffer overflow")
    elif ("BIOS") in row:
        vul_name_list.append("BIOS")
    elif ("Android") in row:
        vul_name_list.append("Android")
    elif ("apple") in row:
        vul_name_list.append("apple")
    elif ("Deserialization") in row:
        vul_name_list.append("Deserialization")
    elif "macOS" in row:
        vul_name_list.append("macOS")
    elif ("heap-based overflow") in row:
        vul_name_list.append("heap-based overflow")
    elif "memory corruptionn" in row:
        vul_name_list.append("memory corruption")
    elif ("backdoor")in row:
        vul_name_list.append("backdoor")
    elif ("resource consumption") in row:
        vul_name_list.append("resource consumption")
    elif ("pointer dereference") in row:
        vul_name_list.append("pointer dereference")
    elif ("MediaTek") in row:
        vul_name_list.append("MediaTek")
    elif ("Mozilla") in row:
        vul_name_list.append("Mozilla")
    elif ("Snapdragon") in row:
        vul_name_list.append("Snapdragon")
    elif ("Tenda") in row:
        vul_name_list.append("Tenda")
    elif ("TOTO-link") in row:
        vul_name_list.append("TOTO-link")
    elif ("TP-link") in row:
        vul_name_list.append("TP-link")
    else:
        vul_name_list.append(None)


# #list add to new coloumn Vuln type/Categroy

# Filter from vulner tilte and paste value relvant and non releavnet
for  row in data[required_colum_name]:
    if ("cross site scripting" in row) or ("cross-site request forgery" in row) or ("path traversal" in row ) or ("pathname traversal" in row) or ("command injection" in row ) or ("os command" in row) or ("sql injection" in row) or ("xxe" in row ) or ("xml external entity" in row):
        relevent_list.append("Relevant")
    elif ("Kernel" in row) or ("use after free" in row) or ("Privilege Escalation" in row) or ("out-of-bounds" in row) or ("apple" in row) or ("android" in row) or ("BIOS" in row) or ("buffer overflow" in row) or ("stack-based" in row) or ("stack-based overflow" in row) or ("router" in row) or ("deserialization" in row) or ("macOS" in row) or ("heap-based overflow" in row) or ("memory corruption" in row) or ("backdoor" in row) or ("chrome" in row) or ("resource consumption" in row) or ("pointer dereference" in row) or ("MediaTek" in row) or ("Mozilla" in row) or ("Snapdragon" in row) or ("Tenda" in row) or ("TOTO-link" in row) or ("TP-link" in row):
        relevent_list.append("non Relevant")  
    else:
        relevent_list.append(None)

"""for row in data[required_colum_name]:
    # print(row)
    if "Kernel" in row:
        vul_name_list.append("Kernel")
    else:
        vul_name_list.append(None)"""

"""for row in data[required_colum_name]:
    # print(row)
    if "Kernel" in row:
        vul_name_list.append("Kernel")
      #  relevent_list.append("non Relevant")
    elif ("use after free") in row:
        vul_name_list.append("use after free")
  #      relevent_list.append("non Relevant")
    elif ("out-of-bounds") in row:
        vul_name_list.append("out-of-bounds")
    elif ("Apple" or "macOS") in row:
        vul_name_list.append("Apple")
    elif ("Android") in row:
        vul_name_list.append("Android")
    elif ("BIOS") in row:
        vul_name_list.append("BIOS")
    elif ("missing authenticationd") in row:
        vul_name_list.append("missing authentication")
    elif ("buffer overflow") in row:
        vul_name_list.append("buffer overflow")
    elif ("stack-based overflow") in row:
        vul_name_list.append("stack-based overflow")
    elif ("router") in row:
        vul_name_list.append("router")
    else:
        vul_name_list.append(None)"""
    
#list add to new coloumn Vuln type/Categroy
data[["C", "D"]] = data[required_colum_name].str.split("|", expand=True)
data.insert(loc=3, column="CVE", value=data["C"])
data.insert(loc=4, column="Vuln tilte", value=data["D"])
del data[required_colum_name], data['C'], data["D"], data["CVE-ID"]
# Filter from vulner tilte and paste value non relvant and non releavnet
"""for  row in data[required_colum_name]:
    if ("cross site scripting") in row:
        relevent_list.append("Relevant")
    elif "cross-site request forgery" in row:
        relevent_list.append("Relevant")
    elif ("path traversal" or "pathname traversal") in row:
        relevent_list.append("Relevant")
    elif ("command injection" or "os command") in row:
        relevent_list.append("Relevant")
    elif "sql injection" in row:
      relevent_list.append("Relevant")  
    elif ("xxe" or "xml external entity") in row:
        relevent_list.append("Relevant") 
    else:
        relevent_list.append(None)"""
data['Relevance'] = relevent_list
data['vuln type\category'] = vul_name_list

# segragation cve and vuln title


data.to_excel(r"D:\RD\2024\jan\Jan-ET.xlsx")

print("meerjada ho gya sab")

