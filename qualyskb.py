import pandas as pd

# Your data in the given format
data = [
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "Content-Based Blind SQL Injection",
    "qid": 150568,
    "userModifiedDate": None,
    "patch": False,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "POTENTIAL"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1675278889000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": False,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "Cacti Unauthenticated Command Injection Vulnerability (CVE-2022-46169)",
    "qid": 150641,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "POTENTIAL"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1696028401000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": True,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "Control Web Panel 7 (CWP7) Unauthenticated Remote Code Execution (RCE) Vulnerability (CVE-2022-44877)",
    "qid": 150642,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [
      "FORM_AUTH"
    ],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "POTENTIAL"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1692140402000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "AUTHENTICATED",
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": True,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "dotCMS Authenticated Directory Traversal Vulnerability (CVE-2022-45783)",
    "qid": 150647,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "POTENTIAL"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1676629463000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": False,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "PHP Incorrect Calculation of Buffer Size Vulnerability (CVE-2023-0568)",
    "qid": 150653,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "POTENTIAL"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1685685675000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": True,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "PHP Buffer Overflow Vulnerability (CVE-2022-37454)",
    "qid": 150663,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "POTENTIAL"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1685664236000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": True,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": True,
    "title": "Microsoft Exchange Server Multiple Vulnerabilities (ProxyNotShell) (CVE-2022-41040,CVE-2022-41082)",
    "qid": 150664,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "POTENTIAL"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1693951201000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": True,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "GeoServer JAI-EXT Remote Code Execution (RCE) Vulnerability (CVE-2022-24816)",
    "qid": 150667,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "VULNERABILITY"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1691452890000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": True,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "Apache Spark Improper Privilege Management (CVE-2023-22946)",
    "qid": 150680,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "POTENTIAL"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1683713436000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": False,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "WordPress Essential Addons for Elementor Plugin: Improper Authentication Vulnerability (CVE-2023-32243)",
    "qid": 150686,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "POTENTIAL"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1692651601000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": True,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": True,
    "title": "Adobe ColdFusion CFC Deserialization RCE Vulnerability (APSB23-25)",
    "qid": 150690,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "VULNERABILITY"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1692824402000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": True,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": True,
    "title": "MOVEit Transfer SQL Injection Vulnerability (CVE-2023-34362)",
    "qid": 150691,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "POTENTIAL"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1693692001000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": True,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "Ivanti Sentry Authentication Bypass Vulnerability (CVE-2023-38035)",
    "qid": 150709,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "POTENTIAL"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1695171601000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": True,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "Ivanti Endpoint Manager Mobile (EPMM) Remote Unauthenticated API Access Vulnerability (CVE-2023-35078)",
    "qid": 150711,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "VULNERABILITY"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1694658581000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": True,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "Ivanti Endpoint Manager Mobile (EPMM) Remote Unauthenticated API Access Vulnerability (CVE-2023-35082)",
    "qid": 150712,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "VULNERABILITY"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1694491201000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": True,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "Adobe ColdFusion Remote Code Execution (RCE) Vulnerability (APSB23-47)",
    "qid": 150715,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "VULNERABILITY"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1697803588000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": True,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "Atlassian Bitbucket Server and Data Center: Remote Code Execution Vulnerability (CVE-2023-22513)",
    "qid": 150718,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "POTENTIAL"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1695881211000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": False,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "Cacti Prior to 1.2.25 Multiple Security Vulnerabilities",
    "qid": 150720,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "POTENTIAL"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1698008401000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": True,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": True,
    "title": "PaperCut NG/MF Remote Code Execution (RCE) Vulnerability (CVE-2023-27350)",
    "qid": 150721,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "VULNERABILITY"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1696050001000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": True,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": True,
    "title": "Atlassian Confluence Server and Data Center Privilege Escalation Vulnerability (CVE-2023-22515)",
    "qid": 150725,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "POTENTIAL"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1698181201000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": True,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "WordPress Media Library Assistant Plugin: Remote Code Execution Vulnerability (CVE-2023-4634)",
    "qid": 150726,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "POTENTIAL"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1697234402000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": True,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "Error Based NoSQL MongoDB Injection",
    "qid": 150727,
    "userModifiedDate": None,
    "patch": False,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "POTENTIAL"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1698077411000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": False,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "Apache Tomcat Multiple Vulnerabilities (CVE-2023-42795, CVE-2023-44487, CVE-2023-45648)",
    "qid": 150732,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "POTENTIAL"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1697749201000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": True,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "Zabbix Stack-buffer Overflow Vulnerability (CVE-2023-32722)",
    "qid": 150734,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "POTENTIAL"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1698062793000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": False,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "Oracle WebLogic Server Multiple Vulnerabilities (CPU - OCT2023)",
    "qid": 150735,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "POTENTIAL"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1697768661000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": True,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "WordPress Royal Elementor Addons Plugin: Unauthenticated Arbitrary File Upload Vulnerability (CVE-2023-5360)",
    "qid": 150736,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "POTENTIAL"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1698062793000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": True,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "Ruby on Rails - Reflected Cross-Site-Scripting (XSS) Vulnerability",
    "qid": 153000,
    "userModifiedDate": None,
    "patch": False,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "VULNERABILITY"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1689415551000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": False,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "Ruby on Rails - HTTP Response Splitting (CRLF Injection)",
    "qid": 153001,
    "userModifiedDate": None,
    "patch": False,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "VULNERABILITY"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1689358045000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": False,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "Ruby On Rails - Stored Cross Site Scripting (XSS) Vulnerability",
    "qid": 153004,
    "userModifiedDate": None,
    "patch": False,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "VULNERABILITY"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1689682019000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": False,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "Ruby On Rails - Stored Cross Site Scripting (XSS) Vulnerability",
    "qid": 153005,
    "userModifiedDate": None,
    "patch": False,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "VULNERABILITY"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1689682021000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": False,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "WordPress Contact Form 7 Plugin: Unrestricted File Upload and Remote Code Execution (RCE) Vulnerability (CVE-2020-35489)",
    "qid": 154132,
    "userModifiedDate": None,
    "patch": True,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "POTENTIAL"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1691445601000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": True,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "EOL/Obsolete Software: Microsoft Internet Information Services (IIS) 1.0 Detected",
    "qid": 520002,
    "userModifiedDate": None,
    "patch": False,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "VULNERABILITY"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1674787801000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": False,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "EOL/Obsolete Software: Microsoft Internet Information Services (IIS) 2.0 Detected",
    "qid": 520003,
    "userModifiedDate": None,
    "patch": False,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "VULNERABILITY"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1674787802000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": False,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "EOL/Obsolete Software: Microsoft Internet Information Services (IIS) 3.0 Detected",
    "qid": 520004,
    "userModifiedDate": None,
    "patch": False,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "VULNERABILITY"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1674787802000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": False,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "EOL/Obsolete Software: Microsoft Internet Information Services (IIS) 4.0 Detected",
    "qid": 520005,
    "userModifiedDate": None,
    "patch": False,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "VULNERABILITY"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1674787802000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": False,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "EOL/Obsolete Software: Microsoft Internet Information Services (IIS) 5.1 Detected",
    "qid": 520007,
    "userModifiedDate": None,
    "patch": False,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "VULNERABILITY"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1674787802000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": False,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "EOL/Obsolete Software: Microsoft Internet Information Services (IIS) 6.0 Detected",
    "qid": 520008,
    "userModifiedDate": None,
    "patch": False,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "VULNERABILITY"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1674787801000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": False,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "EOL/Obsolete Software: Microsoft Internet Information Services (IIS) 7.0 Detected",
    "qid": 520009,
    "userModifiedDate": None,
    "patch": False,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "VULNERABILITY"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1674787802000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": False,
    "cvss3TemporalScore": None,
    "cweCategory": None
  },
  {
    "originalSeverity": 5,
    "wascCategories": None,
    "informationSeverity": None,
    "serviceModifiedDate": None,
    "patchAvailable": None,
    "confirmedSeverity": None,
    "owaspCategories": None,
    "vendorRefs": None,
    "isMalwareAvailable": False,
    "title": "EOL/Obsolete Software: Microsoft Internet Information Services (IIS) 7.5 Detected",
    "qid": 520010,
    "userModifiedDate": None,
    "patch": False,
    "authFlags": [],
    "bugtraqIds": None,
    "complianceTypes": None,
    "vendor": None,
    "cveIds": None,
    "exploitVendors": None,
    "authentication": "",
    "severity": 5,
    "types": [
      "VULNERABILITY"
    ],
    "product": None,
    "malware": None,
    "cveId": None,
    "updateSeverityComment": "",
    "isIgnored": False,
    "vulnDetails": None,
    "listInclusions": None,
    "supportedByIdentifier": "WAS",
    "dateUpdated": 1674787802000,
    "malwareVendors": None,
    "cvss3BaseScore": None,
    "cvss3AttackVector": None,
    "updateStatus": None,
    "discovery": [
      "REMOTE"
    ],
    "compliance": None,
    "fulltextSearch": None,
    "malwareAvailable": None,
    "exploitAvailable": None,
    "complianceDetails": None,
    "potentialSeverity": None,
    "userConfiguration": None,
    "category": "Web Application",
    "exploitability": False,
    "cvss3TemporalScore": None,
    "cweCategory": None
  }
]
# Create a DataFrame from the data
df = pd.DataFrame(data)

# Rename the columns to match your specified names
df = df.rename(columns={
    "title": "Title",
    "qid": "qid",
    "supportedByIdentifier": "supportedByIdentifier",
    "category": "category",
    "original servicity": "original servicity"
})

df.to_excel(r"D:\RD\qualys vs haiku\was5.xlsx", index=False)