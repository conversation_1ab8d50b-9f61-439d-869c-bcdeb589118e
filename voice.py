from pytube import YouTube

def download_audio(url, output_path):
    try:
        yt = YouTube(url)
        audio_stream = yt.streams.filter(only_audio=True).first()
        audio_stream.download(output_path)
        print("Audio downloaded successfully!")
    except Exception as e:
        print(f"Error: {e}")

# Replace 'YOUR_INSTAGRAM_REEL_VIDEO_URL' with the actual URL of the Instagram Reel video
# Replace 'YOUR_OUTPUT_PATH' with the path where you want to save the downloaded audio file
instagram_reel_url = 'https://www.youtube.com/shorts/6NINq_5Pp-U'
output_path = r'D:\RD\meer\resume'

download_audio(instagram_reel_url, output_path)