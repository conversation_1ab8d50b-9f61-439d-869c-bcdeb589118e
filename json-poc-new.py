import json
import pandas as pd

# Load the JSON data
with open(r'D:\RD\sep2023\nvdcve-1.1-2023.json', encoding="utf8") as f:
    data = json.load(f)

# Extract the CVEs with 'Exploit' in their references
data1 = data["CVE_Items"]
temp = []
for i in data1:
    data2 = i["cve"]
    refre = str(data2["references"]["reference_data"])

    if "'Exploit'" in refre:
        temp.append((data2['CVE_data_meta']['ID'], 'Yes'))
    else:
        temp.append((data2['CVE_data_meta']['ID'], 'No'))

# Define the column names
clmn_cve = 'CVE'
clmn_poc = 'POC'

# Create a DataFrame from the 'temp' list
df = pd.DataFrame(temp, columns=[clmn_cve, clmn_poc])

# Save the DataFrame to an Excel file
output_file = r'D:\RD\sep2023\poc1.xlsx'
df.to_excel(output_file, index=False)
print("Done check destination path")