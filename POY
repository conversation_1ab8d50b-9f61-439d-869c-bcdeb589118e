import json
import pandas as pd


with open('nvdcve-1.1-2022.json', encoding="utf8") as f:
#with open('test.json', encoding="utf8") as f:
    data = json.load(f)
    

#print(data["CVE_Items"])
list_cve =[]
data1= (data["CVE_Items"])   # list
temp = []
for i in data1:
    data2 = i["cve"]
    refre= str(data2["references"]["reference_data"])
 
    if "'Exploit'" in refre:
        #print(refre)
        #print(data2['CVE_data_meta']['ID'] + "=Yes" )
        temp.append(data2['CVE_data_meta']['ID'])
        #print(temp)
        

clmn_cve= 'CVE'
clmn_poc='POC'

#excel_file = pd.ExcelFile("result-oct.xlsx")
#df_exel = pd.read_excel(excel_file, "IndusVulnList_2022")
df_exel = pd.read_excel("re2022.xlsx")

cv_id_list = df_exel["CVE"].tolist()#adding into  list
print(len(cv_id_list))
POC_list = df_exel["POC"].tolist()#adding into  list
print(len(POC_list))

for i,cv_id in enumerate(cv_id_list):
	
        cv_id = cv_id.strip()
        if cv_id in temp:
                POC_list[i]="Yes"
                print("found", cv_id)

df_exel["POC"] = POC_list  
df_exel.to_excel('Dec-Poc-Result.xlsx')	

print("December POC result")