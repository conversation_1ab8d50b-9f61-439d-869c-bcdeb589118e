from flask import Flask, request

app = Flask(__name__)

@app.route('/')
def index():
    # Insecure WebSocket URL embedded in the response body
    insecure_ws_url = "ws://insecure-websocket.example.com/socket"
    html = f'''
    <!DOCTYPE html>
    <html>
    <head><title>Insecure WebSocket Test</title></head>
    <body>
        <h1>WebSocket Test Page</h1>
        <p>This page contains an insecure WebSocket URL:</p>
        <code>{insecure_ws_url}</code>
    </body>
    </html>
    '''
    return html

if __name__ == '__main__':
    app.run(debug=True, port=5000)
