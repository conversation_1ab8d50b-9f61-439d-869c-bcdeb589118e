from flask import Flask, request, render_template_string

app = Flask(__name__)

# Basic template with multiple injection points
BASE_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>CSS Injection Test App</title>
    <style>
        /* Default styles */
        body { font-family: Arial; padding: 20px; }
        .user-style { %s }
    </style>
</head>
<body>
    <h1>CSS Injection Test App       
    			s.parentNode.insertBefore(t,s)
		}(window, document,'script','https://connect.facebook.net/en_US/fbevents.js');

        fbq('init', '5281608165210293');
		fbq('track', 'PageView');
	</script>
    <noscript>
        <img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=5281608165210293&ev=PageView&noscript=1"/>
    </noscript>
    <!-- End Meta Pixel Code --></h1>
    
    <!-- Test 1: Direct style attribute reflection -->
    <div style="%s">Test Content 1</div>
    
    <!-- Test 2: Style tag injection -->
    <style>
        %s
    </style>
    
    <!-- Test 3: Class with user input -->
    <div class="user-style">Test Content 2</div>
    
    <!-- Test 4: Dynamic style attribute -->
    <div id="dynamic" style="color: %s">Test Content 3</div>
    
    <h2>Test Forms:</h2>
    <form method="get">
        <label>Style Attribute Test:</label><br>
        <input type="text" name="style" value="%s"><br>
        <label>Style Tag Test:</label><br>
        <input type="text" name="css" value="%s"><br>
        <label>Color Test:</label><br>
        <input type="text" name="color" value="%s"><br>
        <input type="submit" value="Apply Styles">
    </form>
</body>
</html>
"""

@app.route('/')
def index():
    # Get user inputs (intentionally vulnerable)
    style_input = request.args.get('style', 'color: blue')
    style_tag_input = request.args.get('css', 'p { color: green; }')
    color_input = request.args.get('color', 'black')
    user_style = request.args.get('user_style', 'color: red')
    
    # Render template with user inputs (intentionally vulnerable)
    return BASE_TEMPLATE % (
        user_style,
        style_input,
        style_tag_input,
        color_input,
        style_input,
        style_tag_input,
        color_input
    )

if __name__ == '__main__':
    app.run(debug=True, port=5000)