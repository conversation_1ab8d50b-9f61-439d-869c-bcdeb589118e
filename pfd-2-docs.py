import os
from pdf2docx import Converter

def convert_pdf_to_doc(pdf_path, doc_path):
    cv = Converter(pdf_path)
    cv.convert(doc_path, start=0, end=None)
    cv.close()

# Specify the paths for the PDF and output DOC files
pdf_file_path = "C:/Users/<USER>/Downloads/etender.pdf"
doc_file_path = "C:/Users/<USER>/Downloads/output.docx"

# Check if the PDF file exists
if os.path.isfile(pdf_file_path):
    # Convert PDF to DOC
    convert_pdf_to_doc(pdf_file_path, doc_file_path)
    print("Conversion successful!")
else:
    print("PDF file not found.")