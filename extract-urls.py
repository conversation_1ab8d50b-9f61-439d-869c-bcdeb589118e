import re

# open input and output files
input_file = open("url.txt", "r")
output_file = open("output-url.txt", "w")

# read input file and extract URLs using regex pattern
pattern = r"https?://(?:www\.)?[\w-]+(?:\.(?:com|in|net|io|co))(?:$|/)"
for line in input_file:
    matches = re.findall(pattern, line)
    if matches:
        output_file.write(", ".join(matches) + "\n")

# close input and output files
input_file.close()
output_file.close()